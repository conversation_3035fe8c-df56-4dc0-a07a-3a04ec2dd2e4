import { createFileRoute } from '@tanstack/react-router'
import { requireAuth } from '@/lib/auth'
import { BarChart3, Users, Home, FileText, Bell } from 'lucide-react';

export const Route = createFileRoute('/')({
  beforeLoad: () => {
    // This is a protected route
    if (!requireAuth()) {
      // The root route will handle the redirect
      return
    }
  },
  component: Dashboard,
})

function Dashboard() {
  // Mock data for dashboard stats
  const stats = [
    { 
      title: 'Tổng số bài đăng', 
      value: '124', 
      change: '+12%', 
      icon: <Home className="h-8 w-8 text-blue-600" /> 
    },
    { 
      title: 'Nhân viên', 
      value: '45', 
      change: '+5%', 
      icon: <Users className="h-8 w-8 text-green-600" /> 
    },
    { 
      title: 'Bài viết', 
      value: '67', 
      change: '+18%', 
      icon: <FileText className="h-8 w-8 text-purple-600" /> 
    },
    { 
      title: 'Thông báo', 
      value: '12', 
      change: '-3%', 
      icon: <Bell className="h-8 w-8 text-yellow-600" /> 
    },
  ];

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Tổng quan</h1>
      
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-gray-500 text-sm">{stat.title}</p>
                <p className="text-2xl font-semibold mt-1">{stat.value}</p>
                <span className={`text-sm ${stat.change.startsWith('+') ? 'text-green-500' : 'text-red-500'}`}>
                  {stat.change} so với tháng trước
                </span>
              </div>
              {stat.icon}
            </div>
          </div>
        ))}
      </div>
      
      {/* Recent Activity and Chart Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chart */}
        <div className="lg:col-span-2 bg-white p-6 rounded-lg shadow-md">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Thống kê hoạt động</h2>
            <select className="border rounded px-2 py-1 text-sm">
              <option>7 ngày qua</option>
              <option>30 ngày qua</option>
              <option>90 ngày qua</option>
            </select>
          </div>
          <div className="h-64 flex items-center justify-center">
            <BarChart3 size={120} className="text-gray-300" />
            <p className="text-gray-400 ml-4">Biểu đồ thống kê sẽ hiển thị ở đây</p>
          </div>
        </div>
        
        {/* Recent Activity */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">Hoạt động gần đây</h2>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((item) => (
              <div key={item} className="border-b pb-3 last:border-0">
                <div className="flex justify-between">
                  <p className="font-medium">Nguyễn Văn A</p>
                  <span className="text-xs text-gray-500">2 giờ trước</span>
                </div>
                <p className="text-sm text-gray-600">Đã thêm bài đăng mới</p>
              </div>
            ))}
          </div>
          <button className="mt-4 text-blue-600 hover:text-blue-800 text-sm font-medium">
            Xem tất cả hoạt động
          </button>
        </div>
      </div>
    </div>
  )
}
