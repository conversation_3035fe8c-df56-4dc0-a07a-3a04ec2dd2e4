{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/user.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_USER_BASE_URL = `${process.env.API_URL}/api/user`;\r\nconst API_WALLET_TRANSACTION_BASE_URL = `${process.env.API_URL}/api/WalletTransaction`;\r\n\r\nexport async function getUserDashboard() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/dashboard`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserDashboard\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin dashboard\");\r\n  }\r\n}\r\n\r\nexport async function getUserWallet() {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/balance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserWallet\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin ví\");\r\n  }\r\n}\r\n\r\nexport async function getUserPropertyStats() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserPropertyStats\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thống kê bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getUserTransactions(count = 10) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/transactions?pageSize=${count}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTransactions\",\r\n      count,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy lịch sử giao dịch\");\r\n  }\r\n}\r\n\r\nexport async function getUserRanking() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/ranking`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserRanking\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin hạng thành viên\");\r\n  }\r\n}\r\n\r\nexport async function getMonthlySpending(year) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/spending/monthly?year=${year}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getMonthlySpending\",\r\n      year,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy dữ liệu chi tiêu theo tháng\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyPerformance() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/performance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPropertyPerformance\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy hiệu suất bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function topUpWallet(amount, paymentMethod) {\r\n  try {\r\n    // Create a pending transaction\r\n    const pendingTransaction = await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/topup`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        amount,\r\n        paymentMethod,\r\n      }),\r\n    });\r\n\r\n    if (!pendingTransaction.success) {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    const { orderId, callbackUrl, redirectUrl } = pendingTransaction.data;\r\n\r\n    // For bank transfers, we don't need to create a payment gateway transaction\r\n    if (paymentMethod === \"banking\") {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    // For other payment methods, create a payment gateway transaction\r\n    let paymentResult;\r\n\r\n    if (paymentMethod === \"momo\") {\r\n      const { createMomoPayment } = await import(\"@/app/services/payment\");\r\n      paymentResult = await createMomoPayment(\r\n        amount,\r\n        orderId,\r\n        redirectUrl,\r\n        callbackUrl,\r\n        { userId: pendingTransaction.data.userId }\r\n      );\r\n    } else if (paymentMethod === \"card\") {\r\n      paymentResult = {\r\n        success: true,\r\n        paymentUrl: `/user/wallet/card-payment?orderId=${orderId}`,\r\n      };\r\n    }\r\n\r\n    if (!paymentResult.success) {\r\n      // If payment gateway fails, cancel the pending transaction\r\n      await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/cancel`, {\r\n        method: \"POST\",\r\n      });\r\n\r\n      return {\r\n        success: false,\r\n        message: paymentResult.error || \"Không thể xử lý thanh toán\",\r\n        errorType: \"payment_gateway_error\",\r\n      };\r\n    }\r\n\r\n    // Update the transaction with payment gateway info\r\n    await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/update`, {\r\n      method: \"PATCH\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        gatewayTransactionId: paymentResult.transactionId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n      }),\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        orderId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n        message: \"Giao dịch đã được tạo thành công\",\r\n      },\r\n    };\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"topUpWallet\",\r\n      amount,\r\n      paymentMethod,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi nạp tiền vào ví\");\r\n  }\r\n}\r\n\r\n// Verify bank transfer\r\nexport async function verifyBankTransfer(orderId, transactionInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/verify-transfer`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(transactionInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"verifyBankTransfer\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xác minh giao dịch chuyển khoản\");\r\n  }\r\n}\r\n\r\n// Check payment status\r\nexport async function checkPaymentStatus(orderId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/status`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkPaymentStatus\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái thanh toán\");\r\n  }\r\n}\r\n\r\n// Get payment methods and settings\r\nexport async function getPaymentSettings() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/payment-settings`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPaymentSettings\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy cài đặt thanh toán\");\r\n  }\r\n}\r\n\r\nexport async function verifyUserRank() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_USER_BASE_URL}/verify-user-rank`, {\r\n      method: \"GET\",\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error verifying user rank:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xác minh thứ hạng người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get user tax information\r\nexport async function getUserTaxInfo() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Update user tax information\r\nexport async function updateUserTaxInfo(taxInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(taxInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"updateUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Deactivate user account\r\nexport async function deactivateUserAccount(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/deactivate`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deactivateUserAccount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi khóa tài khoản\");\r\n  }\r\n}\r\n\r\n// Request permanent account deletion\r\nexport async function requestAccountDeletion(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/permanent-delete`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"requestAccountDeletion\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi yêu cầu xóa tài khoản\");\r\n  }\r\n}\r\n\r\nexport async function uploadAvatar(file) {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar/upload`, {\r\n      method: \"POST\",\r\n      body: formData,\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"uploadAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên ảnh đại diện\");\r\n  }\r\n}\r\n\r\nexport async function deleteAvatar() {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar`, {\r\n      method: \"DELETE\",\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deleteAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa ảnh đại diện\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAyRsB,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/user.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_USER_BASE_URL = `${process.env.API_URL}/api/user`;\r\nconst API_WALLET_TRANSACTION_BASE_URL = `${process.env.API_URL}/api/WalletTransaction`;\r\n\r\nexport async function getUserDashboard() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/dashboard`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserDashboard\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin dashboard\");\r\n  }\r\n}\r\n\r\nexport async function getUserWallet() {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/balance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserWallet\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin ví\");\r\n  }\r\n}\r\n\r\nexport async function getUserPropertyStats() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserPropertyStats\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thống kê bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getUserTransactions(count = 10) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/transactions?pageSize=${count}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTransactions\",\r\n      count,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy lịch sử giao dịch\");\r\n  }\r\n}\r\n\r\nexport async function getUserRanking() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/ranking`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserRanking\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin hạng thành viên\");\r\n  }\r\n}\r\n\r\nexport async function getMonthlySpending(year) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/spending/monthly?year=${year}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getMonthlySpending\",\r\n      year,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy dữ liệu chi tiêu theo tháng\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyPerformance() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/performance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPropertyPerformance\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy hiệu suất bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function topUpWallet(amount, paymentMethod) {\r\n  try {\r\n    // Create a pending transaction\r\n    const pendingTransaction = await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/topup`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        amount,\r\n        paymentMethod,\r\n      }),\r\n    });\r\n\r\n    if (!pendingTransaction.success) {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    const { orderId, callbackUrl, redirectUrl } = pendingTransaction.data;\r\n\r\n    // For bank transfers, we don't need to create a payment gateway transaction\r\n    if (paymentMethod === \"banking\") {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    // For other payment methods, create a payment gateway transaction\r\n    let paymentResult;\r\n\r\n    if (paymentMethod === \"momo\") {\r\n      const { createMomoPayment } = await import(\"@/app/services/payment\");\r\n      paymentResult = await createMomoPayment(\r\n        amount,\r\n        orderId,\r\n        redirectUrl,\r\n        callbackUrl,\r\n        { userId: pendingTransaction.data.userId }\r\n      );\r\n    } else if (paymentMethod === \"card\") {\r\n      paymentResult = {\r\n        success: true,\r\n        paymentUrl: `/user/wallet/card-payment?orderId=${orderId}`,\r\n      };\r\n    }\r\n\r\n    if (!paymentResult.success) {\r\n      // If payment gateway fails, cancel the pending transaction\r\n      await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/cancel`, {\r\n        method: \"POST\",\r\n      });\r\n\r\n      return {\r\n        success: false,\r\n        message: paymentResult.error || \"Không thể xử lý thanh toán\",\r\n        errorType: \"payment_gateway_error\",\r\n      };\r\n    }\r\n\r\n    // Update the transaction with payment gateway info\r\n    await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/update`, {\r\n      method: \"PATCH\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        gatewayTransactionId: paymentResult.transactionId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n      }),\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        orderId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n        message: \"Giao dịch đã được tạo thành công\",\r\n      },\r\n    };\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"topUpWallet\",\r\n      amount,\r\n      paymentMethod,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi nạp tiền vào ví\");\r\n  }\r\n}\r\n\r\n// Verify bank transfer\r\nexport async function verifyBankTransfer(orderId, transactionInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/verify-transfer`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(transactionInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"verifyBankTransfer\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xác minh giao dịch chuyển khoản\");\r\n  }\r\n}\r\n\r\n// Check payment status\r\nexport async function checkPaymentStatus(orderId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/status`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkPaymentStatus\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái thanh toán\");\r\n  }\r\n}\r\n\r\n// Get payment methods and settings\r\nexport async function getPaymentSettings() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/payment-settings`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPaymentSettings\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy cài đặt thanh toán\");\r\n  }\r\n}\r\n\r\nexport async function verifyUserRank() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_USER_BASE_URL}/verify-user-rank`, {\r\n      method: \"GET\",\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error verifying user rank:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xác minh thứ hạng người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get user tax information\r\nexport async function getUserTaxInfo() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Update user tax information\r\nexport async function updateUserTaxInfo(taxInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(taxInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"updateUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Deactivate user account\r\nexport async function deactivateUserAccount(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/deactivate`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deactivateUserAccount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi khóa tài khoản\");\r\n  }\r\n}\r\n\r\n// Request permanent account deletion\r\nexport async function requestAccountDeletion(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/permanent-delete`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"requestAccountDeletion\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi yêu cầu xóa tài khoản\");\r\n  }\r\n}\r\n\r\nexport async function uploadAvatar(file) {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar/upload`, {\r\n      method: \"POST\",\r\n      body: formData,\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"uploadAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên ảnh đại diện\");\r\n  }\r\n}\r\n\r\nexport async function deleteAvatar() {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar`, {\r\n      method: \"DELETE\",\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deleteAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa ảnh đại diện\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0SsB,oBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/user.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_USER_BASE_URL = `${process.env.API_URL}/api/user`;\r\nconst API_WALLET_TRANSACTION_BASE_URL = `${process.env.API_URL}/api/WalletTransaction`;\r\n\r\nexport async function getUserDashboard() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/dashboard`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserDashboard\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin dashboard\");\r\n  }\r\n}\r\n\r\nexport async function getUserWallet() {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/balance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserWallet\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin ví\");\r\n  }\r\n}\r\n\r\nexport async function getUserPropertyStats() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserPropertyStats\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thống kê bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getUserTransactions(count = 10) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/transactions?pageSize=${count}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTransactions\",\r\n      count,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy lịch sử giao dịch\");\r\n  }\r\n}\r\n\r\nexport async function getUserRanking() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/ranking`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserRanking\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin hạng thành viên\");\r\n  }\r\n}\r\n\r\nexport async function getMonthlySpending(year) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/spending/monthly?year=${year}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getMonthlySpending\",\r\n      year,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy dữ liệu chi tiêu theo tháng\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyPerformance() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/performance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPropertyPerformance\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy hiệu suất bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function topUpWallet(amount, paymentMethod) {\r\n  try {\r\n    // Create a pending transaction\r\n    const pendingTransaction = await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/topup`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        amount,\r\n        paymentMethod,\r\n      }),\r\n    });\r\n\r\n    if (!pendingTransaction.success) {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    const { orderId, callbackUrl, redirectUrl } = pendingTransaction.data;\r\n\r\n    // For bank transfers, we don't need to create a payment gateway transaction\r\n    if (paymentMethod === \"banking\") {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    // For other payment methods, create a payment gateway transaction\r\n    let paymentResult;\r\n\r\n    if (paymentMethod === \"momo\") {\r\n      const { createMomoPayment } = await import(\"@/app/services/payment\");\r\n      paymentResult = await createMomoPayment(\r\n        amount,\r\n        orderId,\r\n        redirectUrl,\r\n        callbackUrl,\r\n        { userId: pendingTransaction.data.userId }\r\n      );\r\n    } else if (paymentMethod === \"card\") {\r\n      paymentResult = {\r\n        success: true,\r\n        paymentUrl: `/user/wallet/card-payment?orderId=${orderId}`,\r\n      };\r\n    }\r\n\r\n    if (!paymentResult.success) {\r\n      // If payment gateway fails, cancel the pending transaction\r\n      await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/cancel`, {\r\n        method: \"POST\",\r\n      });\r\n\r\n      return {\r\n        success: false,\r\n        message: paymentResult.error || \"Không thể xử lý thanh toán\",\r\n        errorType: \"payment_gateway_error\",\r\n      };\r\n    }\r\n\r\n    // Update the transaction with payment gateway info\r\n    await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/update`, {\r\n      method: \"PATCH\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        gatewayTransactionId: paymentResult.transactionId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n      }),\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        orderId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n        message: \"Giao dịch đã được tạo thành công\",\r\n      },\r\n    };\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"topUpWallet\",\r\n      amount,\r\n      paymentMethod,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi nạp tiền vào ví\");\r\n  }\r\n}\r\n\r\n// Verify bank transfer\r\nexport async function verifyBankTransfer(orderId, transactionInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/verify-transfer`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(transactionInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"verifyBankTransfer\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xác minh giao dịch chuyển khoản\");\r\n  }\r\n}\r\n\r\n// Check payment status\r\nexport async function checkPaymentStatus(orderId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/status`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkPaymentStatus\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái thanh toán\");\r\n  }\r\n}\r\n\r\n// Get payment methods and settings\r\nexport async function getPaymentSettings() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/payment-settings`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPaymentSettings\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy cài đặt thanh toán\");\r\n  }\r\n}\r\n\r\nexport async function verifyUserRank() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_USER_BASE_URL}/verify-user-rank`, {\r\n      method: \"GET\",\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error verifying user rank:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xác minh thứ hạng người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get user tax information\r\nexport async function getUserTaxInfo() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Update user tax information\r\nexport async function updateUserTaxInfo(taxInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(taxInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"updateUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Deactivate user account\r\nexport async function deactivateUserAccount(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/deactivate`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deactivateUserAccount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi khóa tài khoản\");\r\n  }\r\n}\r\n\r\n// Request permanent account deletion\r\nexport async function requestAccountDeletion(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/permanent-delete`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"requestAccountDeletion\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi yêu cầu xóa tài khoản\");\r\n  }\r\n}\r\n\r\nexport async function uploadAvatar(file) {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar/upload`, {\r\n      method: \"POST\",\r\n      body: formData,\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"uploadAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên ảnh đại diện\");\r\n  }\r\n}\r\n\r\nexport async function deleteAvatar() {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar`, {\r\n      method: \"DELETE\",\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deleteAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa ảnh đại diện\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA4TsB,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/user.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_USER_BASE_URL = `${process.env.API_URL}/api/user`;\r\nconst API_WALLET_TRANSACTION_BASE_URL = `${process.env.API_URL}/api/WalletTransaction`;\r\n\r\nexport async function getUserDashboard() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/dashboard`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserDashboard\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin dashboard\");\r\n  }\r\n}\r\n\r\nexport async function getUserWallet() {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/balance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserWallet\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin ví\");\r\n  }\r\n}\r\n\r\nexport async function getUserPropertyStats() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserPropertyStats\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thống kê bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getUserTransactions(count = 10) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/transactions?pageSize=${count}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTransactions\",\r\n      count,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy lịch sử giao dịch\");\r\n  }\r\n}\r\n\r\nexport async function getUserRanking() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/ranking`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserRanking\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin hạng thành viên\");\r\n  }\r\n}\r\n\r\nexport async function getMonthlySpending(year) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/spending/monthly?year=${year}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getMonthlySpending\",\r\n      year,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy dữ liệu chi tiêu theo tháng\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyPerformance() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/performance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPropertyPerformance\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy hiệu suất bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function topUpWallet(amount, paymentMethod) {\r\n  try {\r\n    // Create a pending transaction\r\n    const pendingTransaction = await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/topup`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        amount,\r\n        paymentMethod,\r\n      }),\r\n    });\r\n\r\n    if (!pendingTransaction.success) {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    const { orderId, callbackUrl, redirectUrl } = pendingTransaction.data;\r\n\r\n    // For bank transfers, we don't need to create a payment gateway transaction\r\n    if (paymentMethod === \"banking\") {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    // For other payment methods, create a payment gateway transaction\r\n    let paymentResult;\r\n\r\n    if (paymentMethod === \"momo\") {\r\n      const { createMomoPayment } = await import(\"@/app/services/payment\");\r\n      paymentResult = await createMomoPayment(\r\n        amount,\r\n        orderId,\r\n        redirectUrl,\r\n        callbackUrl,\r\n        { userId: pendingTransaction.data.userId }\r\n      );\r\n    } else if (paymentMethod === \"card\") {\r\n      paymentResult = {\r\n        success: true,\r\n        paymentUrl: `/user/wallet/card-payment?orderId=${orderId}`,\r\n      };\r\n    }\r\n\r\n    if (!paymentResult.success) {\r\n      // If payment gateway fails, cancel the pending transaction\r\n      await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/cancel`, {\r\n        method: \"POST\",\r\n      });\r\n\r\n      return {\r\n        success: false,\r\n        message: paymentResult.error || \"Không thể xử lý thanh toán\",\r\n        errorType: \"payment_gateway_error\",\r\n      };\r\n    }\r\n\r\n    // Update the transaction with payment gateway info\r\n    await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/update`, {\r\n      method: \"PATCH\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        gatewayTransactionId: paymentResult.transactionId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n      }),\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        orderId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n        message: \"Giao dịch đã được tạo thành công\",\r\n      },\r\n    };\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"topUpWallet\",\r\n      amount,\r\n      paymentMethod,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi nạp tiền vào ví\");\r\n  }\r\n}\r\n\r\n// Verify bank transfer\r\nexport async function verifyBankTransfer(orderId, transactionInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/verify-transfer`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(transactionInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"verifyBankTransfer\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xác minh giao dịch chuyển khoản\");\r\n  }\r\n}\r\n\r\n// Check payment status\r\nexport async function checkPaymentStatus(orderId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/status`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkPaymentStatus\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái thanh toán\");\r\n  }\r\n}\r\n\r\n// Get payment methods and settings\r\nexport async function getPaymentSettings() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/payment-settings`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPaymentSettings\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy cài đặt thanh toán\");\r\n  }\r\n}\r\n\r\nexport async function verifyUserRank() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_USER_BASE_URL}/verify-user-rank`, {\r\n      method: \"GET\",\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error verifying user rank:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xác minh thứ hạng người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get user tax information\r\nexport async function getUserTaxInfo() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Update user tax information\r\nexport async function updateUserTaxInfo(taxInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(taxInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"updateUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Deactivate user account\r\nexport async function deactivateUserAccount(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/deactivate`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deactivateUserAccount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi khóa tài khoản\");\r\n  }\r\n}\r\n\r\n// Request permanent account deletion\r\nexport async function requestAccountDeletion(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/permanent-delete`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"requestAccountDeletion\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi yêu cầu xóa tài khoản\");\r\n  }\r\n}\r\n\r\nexport async function uploadAvatar(file) {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar/upload`, {\r\n      method: \"POST\",\r\n      body: formData,\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"uploadAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên ảnh đại diện\");\r\n  }\r\n}\r\n\r\nexport async function deleteAvatar() {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar`, {\r\n      method: \"DELETE\",\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deleteAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa ảnh đại diện\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA8UsB,yBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/user.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_USER_BASE_URL = `${process.env.API_URL}/api/user`;\r\nconst API_WALLET_TRANSACTION_BASE_URL = `${process.env.API_URL}/api/WalletTransaction`;\r\n\r\nexport async function getUserDashboard() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/dashboard`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserDashboard\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin dashboard\");\r\n  }\r\n}\r\n\r\nexport async function getUserWallet() {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/balance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserWallet\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin ví\");\r\n  }\r\n}\r\n\r\nexport async function getUserPropertyStats() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserPropertyStats\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thống kê bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getUserTransactions(count = 10) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/transactions?pageSize=${count}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTransactions\",\r\n      count,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy lịch sử giao dịch\");\r\n  }\r\n}\r\n\r\nexport async function getUserRanking() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/ranking`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserRanking\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin hạng thành viên\");\r\n  }\r\n}\r\n\r\nexport async function getMonthlySpending(year) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/spending/monthly?year=${year}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getMonthlySpending\",\r\n      year,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy dữ liệu chi tiêu theo tháng\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyPerformance() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/performance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPropertyPerformance\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy hiệu suất bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function topUpWallet(amount, paymentMethod) {\r\n  try {\r\n    // Create a pending transaction\r\n    const pendingTransaction = await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/topup`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        amount,\r\n        paymentMethod,\r\n      }),\r\n    });\r\n\r\n    if (!pendingTransaction.success) {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    const { orderId, callbackUrl, redirectUrl } = pendingTransaction.data;\r\n\r\n    // For bank transfers, we don't need to create a payment gateway transaction\r\n    if (paymentMethod === \"banking\") {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    // For other payment methods, create a payment gateway transaction\r\n    let paymentResult;\r\n\r\n    if (paymentMethod === \"momo\") {\r\n      const { createMomoPayment } = await import(\"@/app/services/payment\");\r\n      paymentResult = await createMomoPayment(\r\n        amount,\r\n        orderId,\r\n        redirectUrl,\r\n        callbackUrl,\r\n        { userId: pendingTransaction.data.userId }\r\n      );\r\n    } else if (paymentMethod === \"card\") {\r\n      paymentResult = {\r\n        success: true,\r\n        paymentUrl: `/user/wallet/card-payment?orderId=${orderId}`,\r\n      };\r\n    }\r\n\r\n    if (!paymentResult.success) {\r\n      // If payment gateway fails, cancel the pending transaction\r\n      await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/cancel`, {\r\n        method: \"POST\",\r\n      });\r\n\r\n      return {\r\n        success: false,\r\n        message: paymentResult.error || \"Không thể xử lý thanh toán\",\r\n        errorType: \"payment_gateway_error\",\r\n      };\r\n    }\r\n\r\n    // Update the transaction with payment gateway info\r\n    await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/update`, {\r\n      method: \"PATCH\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        gatewayTransactionId: paymentResult.transactionId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n      }),\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        orderId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n        message: \"Giao dịch đã được tạo thành công\",\r\n      },\r\n    };\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"topUpWallet\",\r\n      amount,\r\n      paymentMethod,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi nạp tiền vào ví\");\r\n  }\r\n}\r\n\r\n// Verify bank transfer\r\nexport async function verifyBankTransfer(orderId, transactionInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/verify-transfer`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(transactionInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"verifyBankTransfer\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xác minh giao dịch chuyển khoản\");\r\n  }\r\n}\r\n\r\n// Check payment status\r\nexport async function checkPaymentStatus(orderId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/status`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkPaymentStatus\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái thanh toán\");\r\n  }\r\n}\r\n\r\n// Get payment methods and settings\r\nexport async function getPaymentSettings() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/payment-settings`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPaymentSettings\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy cài đặt thanh toán\");\r\n  }\r\n}\r\n\r\nexport async function verifyUserRank() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_USER_BASE_URL}/verify-user-rank`, {\r\n      method: \"GET\",\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error verifying user rank:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xác minh thứ hạng người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get user tax information\r\nexport async function getUserTaxInfo() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Update user tax information\r\nexport async function updateUserTaxInfo(taxInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(taxInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"updateUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Deactivate user account\r\nexport async function deactivateUserAccount(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/deactivate`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deactivateUserAccount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi khóa tài khoản\");\r\n  }\r\n}\r\n\r\n// Request permanent account deletion\r\nexport async function requestAccountDeletion(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/permanent-delete`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"requestAccountDeletion\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi yêu cầu xóa tài khoản\");\r\n  }\r\n}\r\n\r\nexport async function uploadAvatar(file) {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar/upload`, {\r\n      method: \"POST\",\r\n      body: formData,\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"uploadAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên ảnh đại diện\");\r\n  }\r\n}\r\n\r\nexport async function deleteAvatar() {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar`, {\r\n      method: \"DELETE\",\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deleteAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa ảnh đại diện\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA+VsB,eAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/user.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_USER_BASE_URL = `${process.env.API_URL}/api/user`;\r\nconst API_WALLET_TRANSACTION_BASE_URL = `${process.env.API_URL}/api/WalletTransaction`;\r\n\r\nexport async function getUserDashboard() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/dashboard`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserDashboard\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin dashboard\");\r\n  }\r\n}\r\n\r\nexport async function getUserWallet() {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/balance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserWallet\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin ví\");\r\n  }\r\n}\r\n\r\nexport async function getUserPropertyStats() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserPropertyStats\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thống kê bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getUserTransactions(count = 10) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/transactions?pageSize=${count}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTransactions\",\r\n      count,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy lịch sử giao dịch\");\r\n  }\r\n}\r\n\r\nexport async function getUserRanking() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/ranking`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserRanking\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin hạng thành viên\");\r\n  }\r\n}\r\n\r\nexport async function getMonthlySpending(year) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/spending/monthly?year=${year}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getMonthlySpending\",\r\n      year,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy dữ liệu chi tiêu theo tháng\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyPerformance() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/performance`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPropertyPerformance\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy hiệu suất bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function topUpWallet(amount, paymentMethod) {\r\n  try {\r\n    // Create a pending transaction\r\n    const pendingTransaction = await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/topup`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        amount,\r\n        paymentMethod,\r\n      }),\r\n    });\r\n\r\n    if (!pendingTransaction.success) {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    const { orderId, callbackUrl, redirectUrl } = pendingTransaction.data;\r\n\r\n    // For bank transfers, we don't need to create a payment gateway transaction\r\n    if (paymentMethod === \"banking\") {\r\n      return pendingTransaction;\r\n    }\r\n\r\n    // For other payment methods, create a payment gateway transaction\r\n    let paymentResult;\r\n\r\n    if (paymentMethod === \"momo\") {\r\n      const { createMomoPayment } = await import(\"@/app/services/payment\");\r\n      paymentResult = await createMomoPayment(\r\n        amount,\r\n        orderId,\r\n        redirectUrl,\r\n        callbackUrl,\r\n        { userId: pendingTransaction.data.userId }\r\n      );\r\n    } else if (paymentMethod === \"card\") {\r\n      paymentResult = {\r\n        success: true,\r\n        paymentUrl: `/user/wallet/card-payment?orderId=${orderId}`,\r\n      };\r\n    }\r\n\r\n    if (!paymentResult.success) {\r\n      // If payment gateway fails, cancel the pending transaction\r\n      await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/cancel`, {\r\n        method: \"POST\",\r\n      });\r\n\r\n      return {\r\n        success: false,\r\n        message: paymentResult.error || \"Không thể xử lý thanh toán\",\r\n        errorType: \"payment_gateway_error\",\r\n      };\r\n    }\r\n\r\n    // Update the transaction with payment gateway info\r\n    await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/update`, {\r\n      method: \"PATCH\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        gatewayTransactionId: paymentResult.transactionId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n      }),\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      data: {\r\n        orderId,\r\n        paymentUrl: paymentResult.paymentUrl,\r\n        message: \"Giao dịch đã được tạo thành công\",\r\n      },\r\n    };\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"topUpWallet\",\r\n      amount,\r\n      paymentMethod,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi nạp tiền vào ví\");\r\n  }\r\n}\r\n\r\n// Verify bank transfer\r\nexport async function verifyBankTransfer(orderId, transactionInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/verify-transfer`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(transactionInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"verifyBankTransfer\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xác minh giao dịch chuyển khoản\");\r\n  }\r\n}\r\n\r\n// Check payment status\r\nexport async function checkPaymentStatus(orderId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/status`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkPaymentStatus\",\r\n      orderId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái thanh toán\");\r\n  }\r\n}\r\n\r\n// Get payment methods and settings\r\nexport async function getPaymentSettings() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/payment-settings`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getPaymentSettings\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy cài đặt thanh toán\");\r\n  }\r\n}\r\n\r\nexport async function verifyUserRank() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_USER_BASE_URL}/verify-user-rank`, {\r\n      method: \"GET\",\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error verifying user rank:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xác minh thứ hạng người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get user tax information\r\nexport async function getUserTaxInfo() {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Update user tax information\r\nexport async function updateUserTaxInfo(taxInfo) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(taxInfo),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"updateUserTaxInfo\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật thông tin thuế\");\r\n  }\r\n}\r\n\r\n// Deactivate user account\r\nexport async function deactivateUserAccount(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/deactivate`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deactivateUserAccount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi khóa tài khoản\");\r\n  }\r\n}\r\n\r\n// Request permanent account deletion\r\nexport async function requestAccountDeletion(data) {\r\n  try {\r\n    return await fetchWithAuth(`${API_USER_BASE_URL}/permanent-delete`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"requestAccountDeletion\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi yêu cầu xóa tài khoản\");\r\n  }\r\n}\r\n\r\nexport async function uploadAvatar(file) {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar/upload`, {\r\n      method: \"POST\",\r\n      body: formData,\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"uploadAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên ảnh đại diện\");\r\n  }\r\n}\r\n\r\nexport async function deleteAvatar() {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar`, {\r\n      method: \"DELETE\",\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"deleteAvatar\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa ảnh đại diện\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgXsB,eAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/label.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Label as LabelPrimitive } from \"radix-ui\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root ref={ref} className={cn(labelVariants(), className)} {...props} />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACvD,6LAAC,qMAAA,CAAA,QAAc,CAAC,IAAI;QAAC,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAAa,GAAG,KAAK;;;;;;;AAErF,MAAM,WAAW,GAAG,qMAAA,CAAA,QAAc,CAAC,IAAI,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/textarea.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Textarea = React.forwardRef(({ className, ...props }, ref) => {\r\n  return (\r\n    (<textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-xs placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props} />)\r\n  );\r\n})\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1D,qBACG,6LAAC;QACA,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAEf;;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/alert.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg~*]:pl-7\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props} />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm \", className)}\r\n    {...props} />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,mJACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAEb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAEb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAEb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/dialog.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Dialog as DialogPrimitive } from \"radix-ui\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      {children}\r\n      <DialogPrimitive.Close\r\n        className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)}\r\n    {...props} />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className)}\r\n    {...props} />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,uMAAA,CAAA,SAAe,CAAC,IAAI;AAEnC,MAAM,gBAAgB,uMAAA,CAAA,SAAe,CAAC,OAAO;AAE7C,MAAM,eAAe,uMAAA,CAAA,SAAe,CAAC,MAAM;AAE3C,MAAM,cAAc,uMAAA,CAAA,SAAe,CAAC,KAAK;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC/D,6LAAC,uMAAA,CAAA,SAAe,CAAC,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAPP;AASN,cAAc,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,uMAAA,CAAA,SAAe,CAAC,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBACR;kCACD,6LAAC,uMAAA,CAAA,SAAe,CAAC,KAAK;wBACpB,WAAU;;0CACV,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;MANP;AAQN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;;;;;;MANP;AAQN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,6LAAC,uMAAA,CAAA,SAAe,CAAC,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;;AAEb,YAAY,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACnE,6LAAC,uMAAA,CAAA,SAAe,CAAC,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAEb,kBAAkB,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,WAAW,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/collapsible.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Collapsible as CollapsiblePrimitive } from \"radix-ui\"\r\n\r\nconst Collapsible = CollapsiblePrimitive.Root\r\n\r\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger\r\n\r\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;AAIA,MAAM,cAAc,iNAAA,CAAA,cAAoB,CAAC,IAAI;AAE7C,MAAM,qBAAqB,iNAAA,CAAA,cAAoB,CAAC,kBAAkB;AAElE,MAAM,qBAAqB,iNAAA,CAAA,cAAoB,CAAC,kBAAkB", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/ButtonLoading.jsx"], "sourcesContent": ["import { RotateCw } from \"lucide-react\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nexport default function ButtonLoading({ ...props }) {\r\n  const { showLoading, type, title } = props;\r\n  const t = useTranslations(\"Common\");\r\n  return (\r\n    <button\r\n      type={type}\r\n      disabled={showLoading}\r\n      className=\"\r\n        cursor-pointer\r\n        group relative w-full flex items-center justify-center\r\n        py-2.5 px-4 \r\n        border border-transparent\r\n        text-lg font-semibold\r\n        rounded-md text-white\r\n        bg-teal-600 hover:bg-teal-700\r\n        focus:outline-hidden focus:ring-2 focus:ring-teal-500 focus:ring-offset-2\r\n        transition-all duration-200 ease-in-out\r\n        disabled:opacity-60 disabled:cursor-not-allowed\"\r\n    >\r\n      {showLoading ? (\r\n        <>\r\n          <RotateCw className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" />\r\n          {t(\"loading\")}\r\n        </>\r\n      ) : (\r\n        <>{title}</>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAEe,SAAS,cAAc,EAAE,GAAG,OAAO;;IAChD,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;IACrC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,qBACE,6LAAC;QACC,MAAM;QACN,UAAU;QACV,WAAU;kBAYT,4BACC;;8BACE,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBACnB,EAAE;;yCAGL;sBAAG;;;;;;;AAIX;GA7BwB;;QAEZ,yMAAA,CAAA,kBAAe;;;KAFH", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/authenticate.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse } from \"@/lib/apiUtils\";\r\nimport { changePasswordSchema, forgetPasswordSchema, loginSchema, registerSchema } from \"@/lib/schemas/authSchema\";\r\nimport { createSession, deleteSession, fetchWithAuth, getJwtInfo, getSession, verifyJwtToken } from \"@/lib/sessionUtils\";\r\nimport { redirect } from \"next/navigation\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\nexport async function registerUser(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = registerSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/register`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(validatedFields.data),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      console.error(\"Registration failed:\", response);\r\n      const errorData = await response.json().catch(() => null);\r\n      return handleErrorResponse(false, formDataObject, errorData?.message || \"Registration failed. Please try again.\");\r\n    }\r\n  } catch (error) {\r\n    return handleErrorResponse(false, formDataObject, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n\r\n  redirect(\"/dang-ki/dang-ki-thanh-cong\");\r\n}\r\n\r\nexport async function loginUser(prevState, formData) {\r\n  const validatedFields = loginSchema.safeParse({\r\n    email: formData.get(\"email\"),\r\n    password: formData.get(\"password\"),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: {\r\n        email: formData.get(\"email\"),\r\n      },\r\n    };\r\n  }\r\n\r\n  let urlCallback = \"/user/profile\";\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/login`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        email: validatedFields.data.email,\r\n        password: validatedFields.data.password,\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => null);\r\n      return handleErrorResponse(false, { email: formData.get(\"email\") }, errorData?.message || \"Thông tin đăng nhập không đúng\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    const token = data.token;\r\n    const user = {\r\n      id: data.id,\r\n      fullName: data.fullName,\r\n      email: data.email,\r\n      userType: data.userType,\r\n      phone: data.phone,\r\n      lastLogin: data.lastLogin,\r\n    };\r\n\r\n    await createSession(\"Authorization\", token);\r\n    await createSession(\"UserProfile\", JSON.stringify(user));\r\n  } catch (error) {\r\n    return handleErrorResponse(false, { email: formData.get(\"email\") }, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n  revalidatePath('/');\r\n\r\n  await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n  redirect(urlCallback);\r\n}\r\n\r\nexport async function changePassword(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = changePasswordSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  const jwtData = await getJwtInfo();\r\n\r\n  let payload = {\r\n    email: jwtData.email,\r\n    oldPassword: validatedFields.data.oldPassword,\r\n    newPassword: validatedFields.data.newPassword,\r\n  };\r\n\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me/password`, {\r\n    method: \"PATCH\",\r\n    body: JSON.stringify(payload),\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n  });\r\n}\r\n\r\nexport async function forgotPassword(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = forgetPasswordSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/reset-password`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        email: validatedFields.data.email,\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    return handleErrorResponse(false, { email: formData.get(\"email\") }, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n\r\n  redirect(\"/da-gui-email-khoi-phuc-mat-khau\");\r\n}\r\n\r\nexport async function getUserProfile() {\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me`);\r\n}\r\n\r\nexport async function validateTokenDirectlyFromAPIServer() {\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/validate-token`);\r\n}\r\n\r\nexport async function validateTokenServer() {\r\n  const token = await getSession(\"Authorization\");\r\n  if (!token) {\r\n    return { isLoggedIn: false, isExpired: false, errorType: 'no_token' };\r\n  }\r\n\r\n  const decoded = await verifyJwtToken(token);\r\n  if (!decoded) {\r\n    deleteSession(\"Authorization\");\r\n    deleteSession(\"UserProfile\");\r\n    return { isLoggedIn: false, isExpired: true};\r\n  }\r\n\r\n  return { isLoggedIn: true, isExpired: false };\r\n}\r\n\r\nexport async function logout() {\r\n  await deleteSession(\"Authorization\");\r\n  await deleteSession(\"UserProfile\");\r\n  redirect(\"/\");\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAoGsB,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 497, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/profile/ChangePassword.jsx"], "sourcesContent": ["\"use client\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Circle<PERSON>lert, Eye, EyeOff } from \"lucide-react\";\r\nimport ButtonLoading from \"@/components/ui/ButtonLoading\";\r\nimport { useActionState } from \"react\";\r\nimport { changePassword, logout } from \"@/app/actions/server/authenticate\";\r\nimport { useAlert } from \"@/contexts/AlertContext\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport { useTranslations } from 'next-intl';\r\n\r\nconst initialState = {\r\n  message: null,\r\n  success: false,\r\n  errors: {},\r\n  errorType: null,\r\n};\r\n\r\nexport default function ChangePassword() {\r\n  const t = useTranslations('UserProfilePage');\r\n  const [showOldPassword, setShowOldPassword] = useState(false);\r\n  const [showNewPassword, setShowNewPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [state, formAction, isPending] = useActionState(changePassword, initialState);\r\n  const { showAlert } = useAlert();\r\n\r\n  useEffect(() => {\r\n    if (state.success) {\r\n      showAlert({\r\n        title: t('passwordSuccessTitle'),\r\n        message: t('passwordSuccessMessage'),\r\n        hasCancel: false,\r\n        onConfirm: async () => {\r\n          await logout();\r\n        },\r\n      });\r\n    } else if (state.message && (state.errorType === \"token_expired\" || state.errorType === \"unauthorized\" || state.errorType === \"no_token\")) {\r\n      showAlert(state);\r\n    }\r\n  }, [state, showAlert, t]);\r\n\r\n  return (\r\n    <section className=\"border-t border-gray-900/10 pt-12\">\r\n      <h3 className=\"text-base/7 font-semibold text-gray-900\">{t('passwordSectionTitle')}</h3>\r\n      <p className=\"mt-1 text-sm leading-6 text-gray-600\">\r\n        {t('passwordSectionDescription')}\r\n      </p>\r\n\r\n      <form action={formAction} className=\"mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6\">\r\n        <div className=\"sm:col-span-4\">\r\n          <div className=\"space-y-4\">\r\n            {state.message && !state.success && state.errorType !== \"token_expired\" && state.errorType !== \"unauthorized\" && state.errorType !== \"no_token\" && (\r\n              <Alert variant=\"destructive\">\r\n                <CircleAlert className=\"h-4 w-4\" />\r\n                <AlertTitle>{t('passwordErrorTitle')}</AlertTitle>\r\n                <AlertDescription>{state.message}</AlertDescription>\r\n              </Alert>\r\n            )}\r\n            <div>\r\n              <label htmlFor=\"oldPassword\" className=\"block text-sm font-medium text-gray-700\">\r\n                {t('oldPasswordLabel')}\r\n              </label>\r\n              <div className=\"relative mt-1\">\r\n                <input\r\n                  id=\"oldPassword\"\r\n                  name=\"oldPassword\"\r\n                  type={showOldPassword ? \"text\" : \"password\"}\r\n                  required\r\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-teal-500 focus:border-teal-500 sm:text-sm\"\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\r\n                  onClick={() => setShowOldPassword(!showOldPassword)}\r\n                  aria-label={showOldPassword ? \"Hide password\" : \"Show password\"}\r\n                >\r\n                  {showOldPassword ? (\r\n                    <EyeOff className=\"h-5 w-5 text-gray-400\" />\r\n                  ) : (\r\n                    <Eye className=\"h-5 w-5 text-gray-400\" />\r\n                  )}\r\n                </button>\r\n              </div>\r\n              {state.errors?.oldPassword && (\r\n                <p className=\"mt-1 text-xs text-red-500\">{state.errors.oldPassword[0]}</p>\r\n              )}\r\n            </div>\r\n\r\n            <div>\r\n              <label htmlFor=\"newPassword\" className=\"block text-sm font-medium text-gray-700\">\r\n                {t('newPasswordLabel')}\r\n              </label>\r\n              <div className=\"relative mt-1\">\r\n                <input\r\n                  id=\"newPassword\"\r\n                  name=\"newPassword\"\r\n                  type={showNewPassword ? \"text\" : \"password\"}\r\n                  required\r\n                  minLength={6}\r\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-teal-500 focus:border-teal-500 sm:text-sm\"\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\r\n                  onClick={() => setShowNewPassword(!showNewPassword)}\r\n                  aria-label={showNewPassword ? \"Hide password\" : \"Show password\"}\r\n                >\r\n                  {showNewPassword ? (\r\n                    <EyeOff className=\"h-5 w-5 text-gray-400\" />\r\n                  ) : (\r\n                    <Eye className=\"h-5 w-5 text-gray-400\" />\r\n                  )}\r\n                </button>\r\n              </div>\r\n              {state.errors?.newPassword && (\r\n                <p className=\"mt-1 text-xs text-red-500\">{state.errors.newPassword[0]}</p>\r\n              )}\r\n            </div>\r\n\r\n            <div>\r\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\r\n                {t('confirmNewPasswordLabel')}\r\n              </label>\r\n              <div className=\"relative mt-1\">\r\n                <input\r\n                  id=\"confirmPassword\"\r\n                  name=\"confirmPassword\"\r\n                  type={showConfirmPassword ? \"text\" : \"password\"}\r\n                  required\r\n                  minLength={6}\r\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-teal-500 focus:border-teal-500 sm:text-sm\"\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\r\n                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                  aria-label={showConfirmPassword ? \"Hide password\" : \"Show password\"}\r\n                >\r\n                  {showConfirmPassword ? (\r\n                    <EyeOff className=\"h-5 w-5 text-gray-400\" />\r\n                  ) : (\r\n                    <Eye className=\"h-5 w-5 text-gray-400\" />\r\n                  )}\r\n                </button>\r\n              </div>\r\n              {state.errors?.confirmPassword && (\r\n                <p className=\"mt-1 text-xs text-red-500\">{state.errors.confirmPassword[0]}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mt-6\">\r\n            <ButtonLoading type=\"submit\" showLoading={isPending} title={t('saveButton')} />\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAUA,MAAM,eAAe;IACnB,SAAS;IACT,SAAS;IACT,QAAQ,CAAC;IACT,WAAW;AACb;AAEe,SAAS;;IACtB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,YAAY,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE,mKAAA,CAAA,iBAAc,EAAE;IACtE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,MAAM,OAAO,EAAE;gBACjB,UAAU;oBACR,OAAO,EAAE;oBACT,SAAS,EAAE;oBACX,WAAW;oBACX,SAAS;oDAAE;4BACT,MAAM,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD;wBACb;;gBACF;YACF,OAAO,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,SAAS,KAAK,mBAAmB,MAAM,SAAS,KAAK,kBAAkB,MAAM,SAAS,KAAK,UAAU,GAAG;gBACzI,UAAU;YACZ;QACF;mCAAG;QAAC;QAAO;QAAW;KAAE;IAExB,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAG,WAAU;0BAA2C,EAAE;;;;;;0BAC3D,6LAAC;gBAAE,WAAU;0BACV,EAAE;;;;;;0BAGL,6LAAC;gBAAK,QAAQ;gBAAY,WAAU;0BAClC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,SAAS,KAAK,mBAAmB,MAAM,SAAS,KAAK,kBAAkB,MAAM,SAAS,KAAK,4BACnI,6LAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;;sDACb,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC,6HAAA,CAAA,aAAU;sDAAE,EAAE;;;;;;sDACf,6LAAC,6HAAA,CAAA,mBAAgB;sDAAE,MAAM,OAAO;;;;;;;;;;;;8CAGpC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDACpC,EAAE;;;;;;sDAEL,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,kBAAkB,SAAS;oDACjC,QAAQ;oDACR,WAAU;;;;;;8DAEZ,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,mBAAmB,CAAC;oDACnC,cAAY,kBAAkB,kBAAkB;8DAE/C,gCACC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAIpB,MAAM,MAAM,EAAE,6BACb,6LAAC;4CAAE,WAAU;sDAA6B,MAAM,MAAM,CAAC,WAAW,CAAC,EAAE;;;;;;;;;;;;8CAIzE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDACpC,EAAE;;;;;;sDAEL,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,kBAAkB,SAAS;oDACjC,QAAQ;oDACR,WAAW;oDACX,WAAU;;;;;;8DAEZ,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,mBAAmB,CAAC;oDACnC,cAAY,kBAAkB,kBAAkB;8DAE/C,gCACC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAIpB,MAAM,MAAM,EAAE,6BACb,6LAAC;4CAAE,WAAU;sDAA6B,MAAM,MAAM,CAAC,WAAW,CAAC,EAAE;;;;;;;;;;;;8CAIzE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDACxC,EAAE;;;;;;sDAEL,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,sBAAsB,SAAS;oDACrC,QAAQ;oDACR,WAAW;oDACX,WAAU;;;;;;8DAEZ,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,uBAAuB,CAAC;oDACvC,cAAY,sBAAsB,kBAAkB;8DAEnD,oCACC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAIpB,MAAM,MAAM,EAAE,iCACb,6LAAC;4CAAE,WAAU;sDAA6B,MAAM,MAAM,CAAC,eAAe,CAAC,EAAE;;;;;;;;;;;;;;;;;;sCAK/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,UAAa;gCAAC,MAAK;gCAAS,aAAa;gCAAW,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1E;GA5IwB;;QACZ,yMAAA,CAAA,kBAAe;QAIc,6JAAA,CAAA,iBAAc;QAC/B,4HAAA,CAAA,WAAQ;;;KANR", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/profile/ProfileCard.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { useAlert } from \"@/contexts/AlertContext\";\r\nimport { Mail, Phone, RotateCw, User, Building, AlertTriangle, Trash2, Save, X, ChevronDown, Upload, Camera } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport {\r\n  getUserTaxInfo,\r\n  updateUserTaxInfo,\r\n  deactivateUserAccount,\r\n  requestAccountDeletion,\r\n  uploadAvatar,\r\n  deleteAvatar,\r\n} from \"@/app/actions/server/user\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\";\r\nimport { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger } from \"@/components/ui/collapsible\";\r\nimport { LoadingSpinner } from \"@/components/ui/loading-spinner\";\r\nimport ChangePassword from \"./ChangePassword\";\r\nimport Image from \"next/image\";\r\nimport { logout } from \"@/app/actions/server/authenticate\";\r\n\r\nexport default function ProfileCard() {\r\n  const t = useTranslations(\"UserProfilePage\");\r\n  const { profile, loading, refreshProfile } = useAuth();\r\n  const [taxInfo, setTaxInfo] = useState(null);\r\n  const [taxInfoLoading, setTaxInfoLoading] = useState(true);\r\n  const [isEditingTaxInfo, setIsEditingTaxInfo] = useState(false);\r\n  const [taxFormData, setTaxFormData] = useState({});\r\n  const [deactivateDialogOpen, setDeactivateDialogOpen] = useState(false);\r\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\r\n  const [password, setPassword] = useState(\"\");\r\n  const [reason, setReason] = useState(\"\");\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [avatarFile, setAvatarFile] = useState(null);\r\n  const [avatarPreview, setAvatarPreview] = useState(null);\r\n  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);\r\n  const { showAlert } = useAlert();\r\n  const { toast } = useToast();\r\n\r\n  // Fetch tax info when component mounts\r\n  useEffect(() => {\r\n    const fetchTaxInfo = async () => {\r\n      setTaxInfoLoading(true);\r\n      try {\r\n        const response = await getUserTaxInfo();\r\n        if (response && response?.success) {\r\n          setTaxInfo(response?.data);\r\n          setTaxFormData(response?.data || {});\r\n        } else {\r\n          showAlert(response, null, null);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching tax info:\", error);\r\n        showAlert({ success: false, message: t(\"taxInfoLoadingError\") }, null, null);\r\n      } finally {\r\n        setTaxInfoLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchTaxInfo();\r\n  }, [showAlert, t]);\r\n\r\n  const handleTaxInfoChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setTaxFormData((prev) => {\r\n      // Handle nested properties for invoiceInfo\r\n      if (name.includes(\".\")) {\r\n        const [parent, child] = name.split(\".\");\r\n        return {\r\n          ...prev,\r\n          [parent]: {\r\n            ...prev[parent],\r\n            [child]: value,\r\n          },\r\n        };\r\n      }\r\n      return { ...prev, [name]: value };\r\n    });\r\n  };\r\n\r\n  const handleTaxInfoSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n    try {\r\n      const response = await updateUserTaxInfo(taxFormData);\r\n      if (response && response?.success) {\r\n        setTaxInfo(taxFormData);\r\n        setIsEditingTaxInfo(false);\r\n        toast({\r\n          description: t(\"taxInfoUpdateSuccess\"),\r\n          className: \"bg-teal-600 text-white\",\r\n        });\r\n      } else {\r\n        showAlert(response, null, null);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating tax info:\", error);\r\n      showAlert({ success: false, message: t(\"taxInfoUpdateError\") }, null, null);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDeactivateAccount = async () => {\r\n    if (!password) {\r\n      toast({\r\n        description: t(\"passwordRequired\"),\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    try {\r\n      const response = await deactivateUserAccount({ password, reason });\r\n      if (response && response?.success) {\r\n        showAlert({\r\n          title: t(\"deactivateAccountButton\"),\r\n          message: t(\"accountDeactivateSuccess\"),\r\n          hasCancel: false,\r\n          onConfirm: async () => {\r\n            await logout();\r\n          },\r\n        });\r\n      } else {\r\n        showAlert(response, null, null);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deactivating account:\", error);\r\n      showAlert({ success: false, message: t(\"accountDeactivateError\") }, null, null);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n      setDeactivateDialogOpen(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteAccount = async () => {\r\n    if (!password) {\r\n      toast({\r\n        description: t(\"passwordRequired\"),\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    try {\r\n      const response = await requestAccountDeletion({ password, reason });\r\n      if (response && response?.success) {\r\n        showAlert({\r\n          title: t(\"deleteAccountButton\"),\r\n          message: t(\"accountDeleteRequestSuccess\"),\r\n          hasCancel: false,\r\n          onConfirm: async () => {\r\n            await logout();\r\n          },\r\n        });\r\n      } else {\r\n        showAlert(response, null, null);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error requesting account deletion:\", error);\r\n      showAlert({ success: false, message: t(\"accountDeleteRequestError\") }, null, null);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n      setDeleteDialogOpen(false);\r\n    }\r\n  };\r\n\r\n  const handleAvatarChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n\r\n    // Check file size (200KB = 200 * 1024 bytes)\r\n    if (file.size > 200 * 1024) {\r\n      toast({\r\n        description: t(\"avatarSizeError\"),\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    setAvatarFile(file);\r\n    const previewUrl = URL.createObjectURL(file);\r\n    setAvatarPreview(previewUrl);\r\n  };\r\n\r\n  const handleAvatarUpload = async () => {\r\n    if (!avatarFile) return;\r\n\r\n    setIsUploadingAvatar(true);\r\n    try {\r\n      // If there's an existing avatar, delete it first\r\n      if (profile?.user?.avatarURL) {\r\n        await deleteAvatar();\r\n      }\r\n\r\n      const response = await uploadAvatar(avatarFile);\r\n      if (response && response?.success) {\r\n        toast({\r\n          description: t(\"avatarUploadSuccess\"),\r\n          className: \"bg-teal-600 text-white\",\r\n        });\r\n        await refreshProfile();\r\n      } else {\r\n        showAlert(response, null, null);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error uploading avatar:\", error);\r\n      showAlert({ success: false, message: t(\"avatarUploadError\") }, null, null);\r\n    } finally {\r\n      setIsUploadingAvatar(false);\r\n      setAvatarFile(null);\r\n      setAvatarPreview(null);\r\n    }\r\n  };\r\n\r\n  const handleRemoveAvatar = async () => {\r\n    setIsUploadingAvatar(true);\r\n    try {\r\n      const response = await deleteAvatar();\r\n      if (response && response?.success) {\r\n        toast({\r\n          description: t(\"avatarDeleteSuccess\"),\r\n          className: \"bg-teal-600 text-white\",\r\n        });\r\n        await refreshProfile();\r\n      } else {\r\n        showAlert(response, null, null);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting avatar:\", error);\r\n      showAlert({ success: false, message: t(\"avatarDeleteError\") }, null, null);\r\n    } finally {\r\n      setIsUploadingAvatar(false);\r\n    }\r\n  };\r\n\r\n  if (loading || taxInfoLoading) return <LoadingSpinner text={t(\"loadingMessage\")} />;\r\n  if (!profile) return <p>{t(\"userNotFound\")}</p>;\r\n\r\n  return (\r\n    <Tabs defaultValue=\"personal\" className=\"w-full\">\r\n      <TabsList className=\"grid w-full grid-cols-2\">\r\n        <TabsTrigger value=\"personal\">Thông tin cá nhân</TabsTrigger>\r\n        <TabsTrigger value=\"settings\">Cài đặt tài khoản</TabsTrigger>\r\n      </TabsList>\r\n\r\n      <TabsContent value=\"personal\">\r\n        {/* Avatar Section */}\r\n        <section className=\"mb-10 border-t border-gray-900/10 pt-12\">\r\n          <div className=\"space-y-4\">\r\n            <h3 className=\"text-base/7 font-semibold text-gray-900\">{t(\"avatarTitle\")}</h3>\r\n            <hr className=\"mb-6 border-gray-200\" />\r\n            <div className=\"flex items-center space-x-6\">\r\n              <div className=\"relative h-24 w-24\">\r\n                {profile?.user?.avatarURL || avatarPreview ? (\r\n                  <Image src={avatarPreview || profile?.user?.avatarURL} alt=\"Avatar\" className=\"rounded-full object-cover\" fill />\r\n                ) : (\r\n                  <div className=\"h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center\">\r\n                    <Camera className=\"h-8 w-8 text-gray-400\" />\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex space-x-2\">\r\n                  <Button variant=\"outline\" size=\"sm\" className=\"relative\" disabled={isUploadingAvatar}>\r\n                    <input\r\n                      type=\"file\"\r\n                      className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\r\n                      onChange={handleAvatarChange}\r\n                      accept=\"image/*\"\r\n                    />\r\n                    <Upload className=\"mr-2 h-4 w-4\" />\r\n                    {t(\"uploadAvatarButton\")}\r\n                  </Button>\r\n\r\n                  {profile?.user?.avatarURL && (\r\n                    <Button variant=\"outline\" size=\"sm\" onClick={handleRemoveAvatar} disabled={isUploadingAvatar}>\r\n                      <X className=\"mr-2 h-4 w-4\" />\r\n                      {t(\"removeAvatarButton\")}\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n\r\n                {avatarFile && (\r\n                  <Button size=\"sm\" onClick={handleAvatarUpload} disabled={isUploadingAvatar}>\r\n                    {isUploadingAvatar ? <RotateCw className=\"mr-2 h-4 w-4 animate-spin\" /> : <Save className=\"mr-2 h-4 w-4\" />}\r\n                    {t(\"saveButton\")}\r\n                  </Button>\r\n                )}\r\n\r\n                <p className=\"text-sm text-gray-500\">{t(\"avatarRequirements\")}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Account Information Section */}\r\n        <section className=\"mb-10 border-t border-gray-900/10 pt-12\">\r\n          <div className=\"space-y-4\">\r\n            <h3 className=\"text-base/7 font-semibold text-gray-900\">{t(\"contactInfoTitle\")}</h3>\r\n            <hr className=\"mb-6 border-gray-200\" />\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div className=\"flex items-center space-x-4\">\r\n                <User className=\"h-6 w-6 text-gray-400\" />\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">{t(\"fullNameLabel\")}</p>\r\n                  <p>{profile?.user?.fullName}</p>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-center space-x-4\">\r\n                <Mail className=\"h-6 w-6 text-gray-400\" />\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">{t(\"emailLabel\")}</p>\r\n                  <p>{profile?.user?.email}</p>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-center space-x-4\">\r\n                <Phone className=\"h-6 w-6 text-gray-400\" />\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">{t(\"phoneLabel\")}</p>\r\n                  <p>{profile?.user?.phone}</p>\r\n                </div>\r\n              </div>\r\n              {profile?.user?.userType && (\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <Building className=\"h-6 w-6 text-gray-400\" />\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-gray-500\">{t(\"userTypeLabel\")}</p>\r\n                    <p>{profile?.user?.userType}</p>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Tax and Invoice Information Section */}\r\n        <section className=\"mb-10\">\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <h3 className=\"text-base/7 font-semibold text-gray-900\">{t(\"taxInfoTitle\")}</h3>\r\n            {!isEditingTaxInfo ? (\r\n              <Button onClick={() => setIsEditingTaxInfo(true)} variant=\"outline\">\r\n                {t(\"editButton\")}\r\n              </Button>\r\n            ) : (\r\n              <Button onClick={() => setIsEditingTaxInfo(false)} variant=\"outline\">\r\n                <X className=\"mr-2 h-4 w-4\" />\r\n                {t(\"cancelButton\")}\r\n              </Button>\r\n            )}\r\n          </div>\r\n          <hr className=\"mb-6 border-gray-200\" />\r\n\r\n          {isEditingTaxInfo ? (\r\n            <form onSubmit={handleTaxInfoSubmit} className=\"space-y-4\">\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"personalTaxCode\">{t(\"personalTaxCodeLabel\")}</Label>\r\n                  <Input\r\n                    id=\"personalTaxCode\"\r\n                    name=\"personalTaxCode\"\r\n                    value={taxFormData.personalTaxCode || \"\"}\r\n                    onChange={handleTaxInfoChange}\r\n                    placeholder={t(\"personalTaxCodePlaceholder\")}\r\n                  />\r\n                  <span className=\"text-xs text-gray-500\">{t(\"personalTaxCodeHelperText\")}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <h3 className=\"text-base/7 font-semibold text-gray-900 mt-6\">{t(\"invoiceInfoTitle\")}</h3>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"invoiceInfo.buyerName\">{t(\"buyerNameLabel\")}</Label>\r\n                  <Input\r\n                    id=\"invoiceInfo.buyerName\"\r\n                    name=\"invoiceInfo.buyerName\"\r\n                    value={taxFormData.invoiceInfo?.buyerName || \"\"}\r\n                    onChange={handleTaxInfoChange}\r\n                    placeholder={t(\"buyerNamePlaceholder\")}\r\n                  />\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"invoiceInfo.email\">{t(\"invoiceEmailLabel\")}</Label>\r\n                  <Input\r\n                    id=\"invoiceInfo.email\"\r\n                    name=\"invoiceInfo.email\"\r\n                    type=\"email\"\r\n                    value={taxFormData.invoiceInfo?.email || \"\"}\r\n                    onChange={handleTaxInfoChange}\r\n                    placeholder={t(\"invoiceEmailPlaceholder\")}\r\n                  />\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"invoiceInfo.companyName\">{t(\"companyNameLabel\")}</Label>\r\n                  <Input\r\n                    id=\"invoiceInfo.companyName\"\r\n                    name=\"invoiceInfo.companyName\"\r\n                    value={taxFormData.invoiceInfo?.companyName || \"\"}\r\n                    onChange={handleTaxInfoChange}\r\n                    placeholder={t(\"companyNamePlaceholder\")}\r\n                  />\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"invoiceInfo.taxCode\">{t(\"taxCodeLabel\")}</Label>\r\n                  <Input\r\n                    id=\"invoiceInfo.taxCode\"\r\n                    name=\"invoiceInfo.taxCode\"\r\n                    value={taxFormData.invoiceInfo?.taxCode || \"\"}\r\n                    onChange={handleTaxInfoChange}\r\n                    placeholder={t(\"taxCodePlaceholder\")}\r\n                  />\r\n                  <span className=\"text-xs text-gray-500\">{t(\"personalTaxCodeHelperText\")}</span>\r\n                </div>\r\n                <div className=\"space-y-2 md:col-span-2\">\r\n                  <Label htmlFor=\"invoiceInfo.address\">{t(\"addressLabel\")}</Label>\r\n                  <Textarea\r\n                    id=\"invoiceInfo.address\"\r\n                    name=\"invoiceInfo.address\"\r\n                    value={taxFormData.invoiceInfo?.address || \"\"}\r\n                    onChange={handleTaxInfoChange}\r\n                    placeholder={t(\"addressPlaceholder\")}\r\n                    rows={3}\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex justify-end mt-6\">\r\n                <Button type=\"submit\" disabled={isSubmitting}>\r\n                  {isSubmitting ? <RotateCw className=\"mr-2 h-4 w-4 animate-spin\" /> : <Save className=\"mr-2 h-4 w-4\" />}\r\n                  {t(\"saveButton\")}\r\n                </Button>\r\n              </div>\r\n            </form>\r\n          ) : (\r\n            <div className=\"space-y-4\">\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">{t(\"personalTaxCodeLabel\")}</p>\r\n                  <p>{taxInfo?.personalTaxCode || t(\"notProvided\")}</p>\r\n                </div>\r\n              </div>\r\n\r\n              <h3 className=\"text-base/7 font-semibold text-gray-900 mt-6\">{t(\"invoiceInfoTitle\")}</h3>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">{t(\"buyerNameLabel\")}</p>\r\n                  <p>{taxInfo?.invoiceInfo?.buyerName || t(\"notProvided\")}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">{t(\"invoiceEmailLabel\")}</p>\r\n                  <p>{taxInfo?.invoiceInfo?.email || t(\"notProvided\")}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">{t(\"companyNameLabel\")}</p>\r\n                  <p>{taxInfo?.invoiceInfo?.companyName || t(\"notProvided\")}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">{t(\"taxCodeLabel\")}</p>\r\n                  <p>{taxInfo?.invoiceInfo?.taxCode || t(\"notProvided\")}</p>\r\n                </div>\r\n                <div className=\"md:col-span-2\">\r\n                  <p className=\"text-sm font-medium text-gray-500\">{t(\"addressLabel\")}</p>\r\n                  <p>{taxInfo?.invoiceInfo?.address || t(\"notProvided\")}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </section>\r\n      </TabsContent>\r\n\r\n      <TabsContent value=\"settings\">\r\n        {/* Change Password Section */}\r\n        <section className=\"mb-10\">\r\n          <ChangePassword />\r\n        </section>\r\n        <hr className=\"mb-6 border-gray-200\" />\r\n\r\n        {/* Account Management Section */}\r\n        <section className=\"space-y-4\">\r\n          {/* Deactivate Account Collapsible */}\r\n          <Collapsible>\r\n            <CollapsibleTrigger className=\"flex w-full items-center justify-between rounded-lg border p-4 hover:bg-muted\">\r\n              <div className=\"flex items-center space-x-2 text-red-600\">\r\n                <AlertTriangle className=\"h-5 w-5\" />\r\n                <span className=\"font-semibold\">Yêu cầu khóa tài khoản</span>\r\n              </div>\r\n              <ChevronDown className=\"h-5 w-5\" />\r\n            </CollapsibleTrigger>\r\n            <CollapsibleContent className=\"px-4 pt-2\">\r\n              <Alert variant=\"destructive\" className=\"mb-4\">\r\n                <AlertTriangle className=\"h-4 w-4\" />\r\n                <AlertTitle>Lưu ý</AlertTitle>\r\n                <AlertDescription>\r\n                  <p className=\"mb-2\">Quý khách sẽ không thể đăng nhập lại vào tài khoản này sau khi khóa.</p>\r\n                  <p className=\"mb-2\">Các tin đăng đang hiển thị của quý khách sẽ tiếp tục được hiển thị tới hết thời gian đăng tin đã chọn.</p>\r\n                  <p className=\"mb-2\">Số dư tiền (nếu có) trong các tài khoản của quý khách sẽ không được hoàn lại.</p>\r\n                  <p className=\"mb-2\">Tài khoản dịch vụ của quý khách chỉ có thể được khóa khi không còn số dư nợ.</p>\r\n                  <p>\r\n                    Số điện thoại chính đăng ký tài khoản này và các số điện thoại đăng tin của quý khách sẽ không thể được sử dụng lại để đăng ký tài\r\n                    khoản mới.\r\n                  </p>\r\n                  <p className=\"mt-2\">\r\n                    Trong trường hợp bạn muốn sử dụng lại số điện thoại chính này, vui lòng liên hệ CSKH 1900.1881 để được hỗ trợ.\r\n                  </p>\r\n                </AlertDescription>\r\n              </Alert>\r\n\r\n              <Dialog open={deactivateDialogOpen} onOpenChange={setDeactivateDialogOpen}>\r\n                <DialogTrigger asChild>\r\n                  <Button variant=\"destructive\">\r\n                    <AlertTriangle className=\"mr-2 h-4 w-4\" />\r\n                    {t(\"deactivateAccountButton\")}\r\n                  </Button>\r\n                </DialogTrigger>\r\n                <DialogContent>\r\n                  <DialogHeader>\r\n                    <DialogTitle>{t(\"deactivateAccountTitle\")}</DialogTitle>\r\n                    <DialogDescription>{t(\"deactivateAccountDescription\")}</DialogDescription>\r\n                  </DialogHeader>\r\n\r\n                  <div className=\"space-y-4 py-4\">\r\n                    <div className=\"space-y-2\">\r\n                      <Label htmlFor=\"deactivate-password\">{t(\"passwordLabel\")}</Label>\r\n                      <Input\r\n                        id=\"deactivate-password\"\r\n                        type=\"password\"\r\n                        value={password}\r\n                        onChange={(e) => setPassword(e.target.value)}\r\n                        placeholder={t(\"passwordPlaceholder\")}\r\n                        required\r\n                        autoComplete=\"new-password\"\r\n                      />\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Label htmlFor=\"deactivate-reason\">{t(\"reasonLabel\")}</Label>\r\n                      <Textarea\r\n                        id=\"deactivate-reason\"\r\n                        value={reason}\r\n                        onChange={(e) => setReason(e.target.value)}\r\n                        placeholder={t(\"reasonPlaceholder\")}\r\n                        rows={3}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <DialogFooter>\r\n                    <Button variant=\"outline\" onClick={() => setDeactivateDialogOpen(false)}>\r\n                      {t(\"cancelButton\")}\r\n                    </Button>\r\n                    <Button variant=\"destructive\" onClick={handleDeactivateAccount} disabled={isSubmitting}>\r\n                      {isSubmitting ? <RotateCw className=\"mr-2 h-4 w-4 animate-spin\" /> : null}\r\n                      {t(\"confirmDeactivateButton\")}\r\n                    </Button>\r\n                  </DialogFooter>\r\n                </DialogContent>\r\n              </Dialog>\r\n            </CollapsibleContent>\r\n          </Collapsible>\r\n\r\n          {/* Delete Account Collapsible */}\r\n          <Collapsible>\r\n            <CollapsibleTrigger className=\"flex w-full items-center justify-between rounded-lg border p-4 hover:bg-muted\">\r\n              <div className=\"flex items-center space-x-2 text-red-600\">\r\n                <Trash2 className=\"h-5 w-5\" />\r\n                <span className=\"font-semibold\">Yêu cầu xóa tài khoản</span>\r\n              </div>\r\n              <ChevronDown className=\"h-5 w-5\" />\r\n            </CollapsibleTrigger>\r\n            <CollapsibleContent className=\"px-4 pt-2\">\r\n              <Alert variant=\"destructive\" className=\"mb-4\">\r\n                <AlertTriangle className=\"h-4 w-4\" />\r\n                <AlertTitle>Lưu ý</AlertTitle>\r\n                <AlertDescription>\r\n                  <p>Gửi yêu cầu xoá toàn bộ thông tin của tài khoản. Sau khi được xử lý, toàn bộ thông tin sẽ được xoá và không thể hoàn tác.</p>\r\n                </AlertDescription>\r\n              </Alert>\r\n\r\n              <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>\r\n                <DialogTrigger asChild>\r\n                  <Button variant=\"destructive\">\r\n                    <Trash2 className=\"mr-2 h-4 w-4\" />\r\n                    {t(\"deleteAccountButton\")}\r\n                  </Button>\r\n                </DialogTrigger>\r\n                <DialogContent>\r\n                  <DialogHeader>\r\n                    <DialogTitle>{t(\"deleteAccountTitle\")}</DialogTitle>\r\n                    <DialogDescription>{t(\"deleteAccountDescription\")}</DialogDescription>\r\n                  </DialogHeader>\r\n\r\n                  <div className=\"space-y-4 py-4\">\r\n                    <div className=\"space-y-2\">\r\n                      <Label htmlFor=\"delete-password\">{t(\"passwordLabel\")}</Label>\r\n                      <Input\r\n                        id=\"delete-password\"\r\n                        type=\"password\"\r\n                        value={password}\r\n                        onChange={(e) => setPassword(e.target.value)}\r\n                        placeholder={t(\"passwordPlaceholder\")}\r\n                        required\r\n                        autoComplete=\"new-password\"\r\n                      />\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Label htmlFor=\"delete-reason\">{t(\"reasonLabel\")}</Label>\r\n                      <Textarea\r\n                        id=\"delete-reason\"\r\n                        value={reason}\r\n                        onChange={(e) => setReason(e.target.value)}\r\n                        placeholder={t(\"reasonPlaceholder\")}\r\n                        rows={3}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <DialogFooter>\r\n                    <Button variant=\"outline\" onClick={() => setDeleteDialogOpen(false)}>\r\n                      {t(\"cancelButton\")}\r\n                    </Button>\r\n                    <Button variant=\"destructive\" onClick={handleDeleteAccount} disabled={isSubmitting}>\r\n                      {isSubmitting ? <RotateCw className=\"mr-2 h-4 w-4 animate-spin\" /> : null}\r\n                      {t(\"confirmDeleteButton\")}\r\n                    </Button>\r\n                  </DialogFooter>\r\n                </DialogContent>\r\n              </Dialog>\r\n            </CollapsibleContent>\r\n          </Collapsible>\r\n        </section>\r\n      </TabsContent>\r\n    </Tabs>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA3BA;;;;;;;;;;;;;;;;;;;;AA6Be,SAAS;;IACtB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAChD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEzB,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;sDAAe;oBACnB,kBAAkB;oBAClB,IAAI;wBACF,MAAM,WAAW,MAAM,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD;wBACpC,IAAI,YAAY,UAAU,SAAS;4BACjC,WAAW,UAAU;4BACrB,eAAe,UAAU,QAAQ,CAAC;wBACpC,OAAO;4BACL,UAAU,UAAU,MAAM;wBAC5B;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,UAAU;4BAAE,SAAS;4BAAO,SAAS,EAAE;wBAAuB,GAAG,MAAM;oBACzE,SAAU;wBACR,kBAAkB;oBACpB;gBACF;;YAEA;QACF;gCAAG;QAAC;QAAW;KAAE;IAEjB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,eAAe,CAAC;YACd,2CAA2C;YAC3C,IAAI,KAAK,QAAQ,CAAC,MAAM;gBACtB,MAAM,CAAC,QAAQ,MAAM,GAAG,KAAK,KAAK,CAAC;gBACnC,OAAO;oBACL,GAAG,IAAI;oBACP,CAAC,OAAO,EAAE;wBACR,GAAG,IAAI,CAAC,OAAO;wBACf,CAAC,MAAM,EAAE;oBACX;gBACF;YACF;YACA,OAAO;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM;QAClC;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,mKAAA,CAAA,oBAAiB,AAAD,EAAE;YACzC,IAAI,YAAY,UAAU,SAAS;gBACjC,WAAW;gBACX,oBAAoB;gBACpB,MAAM;oBACJ,aAAa,EAAE;oBACf,WAAW;gBACb;YACF,OAAO;gBACL,UAAU,UAAU,MAAM;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,UAAU;gBAAE,SAAS;gBAAO,SAAS,EAAE;YAAsB,GAAG,MAAM;QACxE,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI,CAAC,UAAU;YACb,MAAM;gBACJ,aAAa,EAAE;gBACf,SAAS;YACX;YACA;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,mKAAA,CAAA,wBAAqB,AAAD,EAAE;gBAAE;gBAAU;YAAO;YAChE,IAAI,YAAY,UAAU,SAAS;gBACjC,UAAU;oBACR,OAAO,EAAE;oBACT,SAAS,EAAE;oBACX,WAAW;oBACX,WAAW;wBACT,MAAM,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD;oBACb;gBACF;YACF,OAAO;gBACL,UAAU,UAAU,MAAM;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,UAAU;gBAAE,SAAS;gBAAO,SAAS,EAAE;YAA0B,GAAG,MAAM;QAC5E,SAAU;YACR,gBAAgB;YAChB,wBAAwB;QAC1B;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,UAAU;YACb,MAAM;gBACJ,aAAa,EAAE;gBACf,SAAS;YACX;YACA;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,mKAAA,CAAA,yBAAsB,AAAD,EAAE;gBAAE;gBAAU;YAAO;YACjE,IAAI,YAAY,UAAU,SAAS;gBACjC,UAAU;oBACR,OAAO,EAAE;oBACT,SAAS,EAAE;oBACX,WAAW;oBACX,WAAW;wBACT,MAAM,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD;oBACb;gBACF;YACF,OAAO;gBACL,UAAU,UAAU,MAAM;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,UAAU;gBAAE,SAAS;gBAAO,SAAS,EAAE;YAA6B,GAAG,MAAM;QAC/E,SAAU;YACR,gBAAgB;YAChB,oBAAoB;QACtB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC,MAAM;QAEX,6CAA6C;QAC7C,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM;YAC1B,MAAM;gBACJ,aAAa,EAAE;gBACf,SAAS;YACX;YACA;QACF;QAEA,cAAc;QACd,MAAM,aAAa,IAAI,eAAe,CAAC;QACvC,iBAAiB;IACnB;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,YAAY;QAEjB,qBAAqB;QACrB,IAAI;YACF,iDAAiD;YACjD,IAAI,SAAS,MAAM,WAAW;gBAC5B,MAAM,CAAA,GAAA,mKAAA,CAAA,eAAY,AAAD;YACnB;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,mKAAA,CAAA,eAAY,AAAD,EAAE;YACpC,IAAI,YAAY,UAAU,SAAS;gBACjC,MAAM;oBACJ,aAAa,EAAE;oBACf,WAAW;gBACb;gBACA,MAAM;YACR,OAAO;gBACL,UAAU,UAAU,MAAM;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,UAAU;gBAAE,SAAS;gBAAO,SAAS,EAAE;YAAqB,GAAG,MAAM;QACvE,SAAU;YACR,qBAAqB;YACrB,cAAc;YACd,iBAAiB;QACnB;IACF;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,mKAAA,CAAA,eAAY,AAAD;YAClC,IAAI,YAAY,UAAU,SAAS;gBACjC,MAAM;oBACJ,aAAa,EAAE;oBACf,WAAW;gBACb;gBACA,MAAM;YACR,OAAO;gBACL,UAAU,UAAU,MAAM;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,UAAU;gBAAE,SAAS;gBAAO,SAAS,EAAE;YAAqB,GAAG,MAAM;QACvE,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,IAAI,WAAW,gBAAgB,qBAAO,6LAAC,0IAAA,CAAA,iBAAc;QAAC,MAAM,EAAE;;;;;;IAC9D,IAAI,CAAC,SAAS,qBAAO,6LAAC;kBAAG,EAAE;;;;;;IAE3B,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,cAAa;QAAW,WAAU;;0BACtC,6LAAC,4HAAA,CAAA,WAAQ;gBAAC,WAAU;;kCAClB,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;kCAAW;;;;;;kCAC9B,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;kCAAW;;;;;;;;;;;;0BAGhC,6LAAC,4HAAA,CAAA,cAAW;gBAAC,OAAM;;kCAEjB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C,EAAE;;;;;;8CAC3D,6LAAC;oCAAG,WAAU;;;;;;8CACd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,SAAS,MAAM,aAAa,8BAC3B,6LAAC,gIAAA,CAAA,UAAK;gDAAC,KAAK,iBAAiB,SAAS,MAAM;gDAAW,KAAI;gDAAS,WAAU;gDAA4B,IAAI;;;;;qEAE9G,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAKxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,WAAU;4DAAW,UAAU;;8EACjE,6LAAC;oEACC,MAAK;oEACL,WAAU;oEACV,UAAU;oEACV,QAAO;;;;;;8EAET,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,EAAE;;;;;;;wDAGJ,SAAS,MAAM,2BACd,6LAAC,8HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,SAAS;4DAAoB,UAAU;;8EACzE,6LAAC,+LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;gEACZ,EAAE;;;;;;;;;;;;;gDAKR,4BACC,6LAAC,8HAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAS;oDAAoB,UAAU;;wDACtD,kCAAoB,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;iFAAiC,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACzF,EAAE;;;;;;;8DAIP,6LAAC;oDAAE,WAAU;8DAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C,EAAE;;;;;;8CAC3D,6LAAC;oCAAG,WAAU;;;;;;8CACd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAqC,EAAE;;;;;;sEACpD,6LAAC;sEAAG,SAAS,MAAM;;;;;;;;;;;;;;;;;;sDAGvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAqC,EAAE;;;;;;sEACpD,6LAAC;sEAAG,SAAS,MAAM;;;;;;;;;;;;;;;;;;sDAGvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAqC,EAAE;;;;;;sEACpD,6LAAC;sEAAG,SAAS,MAAM;;;;;;;;;;;;;;;;;;wCAGtB,SAAS,MAAM,0BACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAqC,EAAE;;;;;;sEACpD,6LAAC;sEAAG,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/B,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C,EAAE;;;;;;oCAC1D,CAAC,iCACA,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,oBAAoB;wCAAO,SAAQ;kDACvD,EAAE;;;;;6DAGL,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,oBAAoB;wCAAQ,SAAQ;;0DACzD,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CACZ,EAAE;;;;;;;;;;;;;0CAIT,6LAAC;gCAAG,WAAU;;;;;;4BAEb,iCACC,6LAAC;gCAAK,UAAU;gCAAqB,WAAU;;kDAC7C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAmB,EAAE;;;;;;8DACpC,6LAAC,6HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,YAAY,eAAe,IAAI;oDACtC,UAAU;oDACV,aAAa,EAAE;;;;;;8DAEjB,6LAAC;oDAAK,WAAU;8DAAyB,EAAE;;;;;;;;;;;;;;;;;kDAI/C,6LAAC;wCAAG,WAAU;kDAAgD,EAAE;;;;;;kDAChE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAyB,EAAE;;;;;;kEAC1C,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,YAAY,WAAW,EAAE,aAAa;wDAC7C,UAAU;wDACV,aAAa,EAAE;;;;;;;;;;;;0DAGnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAqB,EAAE;;;;;;kEACtC,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,OAAO,YAAY,WAAW,EAAE,SAAS;wDACzC,UAAU;wDACV,aAAa,EAAE;;;;;;;;;;;;0DAGnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAA2B,EAAE;;;;;;kEAC5C,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,YAAY,WAAW,EAAE,eAAe;wDAC/C,UAAU;wDACV,aAAa,EAAE;;;;;;;;;;;;0DAGnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAuB,EAAE;;;;;;kEACxC,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,YAAY,WAAW,EAAE,WAAW;wDAC3C,UAAU;wDACV,aAAa,EAAE;;;;;;kEAEjB,6LAAC;wDAAK,WAAU;kEAAyB,EAAE;;;;;;;;;;;;0DAE7C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAuB,EAAE;;;;;;kEACxC,6LAAC,gIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,MAAK;wDACL,OAAO,YAAY,WAAW,EAAE,WAAW;wDAC3C,UAAU;wDACV,aAAa,EAAE;wDACf,MAAM;;;;;;;;;;;;;;;;;;kDAKZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,UAAU;;gDAC7B,6BAAe,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;yEAAiC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACpF,EAAE;;;;;;;;;;;;;;;;;qDAKT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAqC,EAAE;;;;;;8DACpD,6LAAC;8DAAG,SAAS,mBAAmB,EAAE;;;;;;;;;;;;;;;;;kDAItC,6LAAC;wCAAG,WAAU;kDAAgD,EAAE;;;;;;kDAChE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC,EAAE;;;;;;kEACpD,6LAAC;kEAAG,SAAS,aAAa,aAAa,EAAE;;;;;;;;;;;;0DAE3C,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC,EAAE;;;;;;kEACpD,6LAAC;kEAAG,SAAS,aAAa,SAAS,EAAE;;;;;;;;;;;;0DAEvC,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC,EAAE;;;;;;kEACpD,6LAAC;kEAAG,SAAS,aAAa,eAAe,EAAE;;;;;;;;;;;;0DAE7C,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC,EAAE;;;;;;kEACpD,6LAAC;kEAAG,SAAS,aAAa,WAAW,EAAE;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC,EAAE;;;;;;kEACpD,6LAAC;kEAAG,SAAS,aAAa,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC,4HAAA,CAAA,cAAW;gBAAC,OAAM;;kCAEjB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC,2KAAA,CAAA,UAAc;;;;;;;;;;kCAEjB,6LAAC;wBAAG,WAAU;;;;;;kCAGd,6LAAC;wBAAQ,WAAU;;0CAEjB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC,mIAAA,CAAA,qBAAkB;wCAAC,WAAU;;0DAC5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,6LAAC,mIAAA,CAAA,qBAAkB;wCAAC,WAAU;;0DAC5B,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAc,WAAU;;kEACrC,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,6LAAC,6HAAA,CAAA,aAAU;kEAAC;;;;;;kEACZ,6LAAC,6HAAA,CAAA,mBAAgB;;0EACf,6LAAC;gEAAE,WAAU;0EAAO;;;;;;0EACpB,6LAAC;gEAAE,WAAU;0EAAO;;;;;;0EACpB,6LAAC;gEAAE,WAAU;0EAAO;;;;;;0EACpB,6LAAC;gEAAE,WAAU;0EAAO;;;;;;0EACpB,6LAAC;0EAAE;;;;;;0EAIH,6LAAC;gEAAE,WAAU;0EAAO;;;;;;;;;;;;;;;;;;0DAMxB,6LAAC,8HAAA,CAAA,SAAM;gDAAC,MAAM;gDAAsB,cAAc;;kEAChD,6LAAC,8HAAA,CAAA,gBAAa;wDAAC,OAAO;kEACpB,cAAA,6LAAC,8HAAA,CAAA,SAAM;4DAAC,SAAQ;;8EACd,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEACxB,EAAE;;;;;;;;;;;;kEAGP,6LAAC,8HAAA,CAAA,gBAAa;;0EACZ,6LAAC,8HAAA,CAAA,eAAY;;kFACX,6LAAC,8HAAA,CAAA,cAAW;kFAAE,EAAE;;;;;;kFAChB,6LAAC,8HAAA,CAAA,oBAAiB;kFAAE,EAAE;;;;;;;;;;;;0EAGxB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6HAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAuB,EAAE;;;;;;0FACxC,6LAAC,6HAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,MAAK;gFACL,OAAO;gFACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gFAC3C,aAAa,EAAE;gFACf,QAAQ;gFACR,cAAa;;;;;;;;;;;;kFAIjB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6HAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAqB,EAAE;;;;;;0FACtC,6LAAC,gIAAA,CAAA,WAAQ;gFACP,IAAG;gFACH,OAAO;gFACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gFACzC,aAAa,EAAE;gFACf,MAAM;;;;;;;;;;;;;;;;;;0EAKZ,6LAAC,8HAAA,CAAA,eAAY;;kFACX,6LAAC,8HAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,SAAS,IAAM,wBAAwB;kFAC9D,EAAE;;;;;;kFAEL,6LAAC,8HAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAc,SAAS;wEAAyB,UAAU;;4EACvE,6BAAe,6LAAC,iNAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;uFAAiC;4EACpE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASf,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC,mIAAA,CAAA,qBAAkB;wCAAC,WAAU;;0DAC5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,6LAAC,mIAAA,CAAA,qBAAkB;wCAAC,WAAU;;0DAC5B,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAc,WAAU;;kEACrC,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,6LAAC,6HAAA,CAAA,aAAU;kEAAC;;;;;;kEACZ,6LAAC,6HAAA,CAAA,mBAAgB;kEACf,cAAA,6LAAC;sEAAE;;;;;;;;;;;;;;;;;0DAIP,6LAAC,8HAAA,CAAA,SAAM;gDAAC,MAAM;gDAAkB,cAAc;;kEAC5C,6LAAC,8HAAA,CAAA,gBAAa;wDAAC,OAAO;kEACpB,cAAA,6LAAC,8HAAA,CAAA,SAAM;4DAAC,SAAQ;;8EACd,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,EAAE;;;;;;;;;;;;kEAGP,6LAAC,8HAAA,CAAA,gBAAa;;0EACZ,6LAAC,8HAAA,CAAA,eAAY;;kFACX,6LAAC,8HAAA,CAAA,cAAW;kFAAE,EAAE;;;;;;kFAChB,6LAAC,8HAAA,CAAA,oBAAiB;kFAAE,EAAE;;;;;;;;;;;;0EAGxB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6HAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAmB,EAAE;;;;;;0FACpC,6LAAC,6HAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,MAAK;gFACL,OAAO;gFACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gFAC3C,aAAa,EAAE;gFACf,QAAQ;gFACR,cAAa;;;;;;;;;;;;kFAIjB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6HAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAiB,EAAE;;;;;;0FAClC,6LAAC,gIAAA,CAAA,WAAQ;gFACP,IAAG;gFACH,OAAO;gFACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gFACzC,aAAa,EAAE;gFACf,MAAM;;;;;;;;;;;;;;;;;;0EAKZ,6LAAC,8HAAA,CAAA,eAAY;;kFACX,6LAAC,8HAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,SAAS,IAAM,oBAAoB;kFAC1D,EAAE;;;;;;kFAEL,6LAAC,8HAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAc,SAAS;wEAAqB,UAAU;;4EACnE,6BAAe,6LAAC,iNAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;uFAAiC;4EACpE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzB;GAxmBwB;;QACZ,yMAAA,CAAA,kBAAe;QACoB,2HAAA,CAAA,UAAO;QAa9B,4HAAA,CAAA,WAAQ;QACZ,wHAAA,CAAA,WAAQ;;;KAhBJ", "debugId": null}}]}