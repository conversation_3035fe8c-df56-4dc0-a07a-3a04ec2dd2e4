using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class PropertyHighlightResultDto
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Guid PropertyId { get; set; }
        public string PropertyTitle { get; set; }
        public bool IsHighlighted { get; set; }
        public decimal Cost { get; set; }
        public Guid? InvoiceId { get; set; }
        public Guid? TransactionId { get; set; }
        public PropertyDto Property { get; set; }
    }
}
