{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/checkbox.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Checkbox as CheckboxPrimitive } from \"radix-ui\"\r\nimport { Check } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Checkbox = React.forwardRef(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow-sm focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    <CheckboxPrimitive.Indicator className={cn(\"flex items-center justify-center text-current\")}>\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n))\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1D,6LAAC,2MAAA,CAAA,WAAiB,CAAC,IAAI;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2QACA;QAED,GAAG,KAAK;kBACT,cAAA,6LAAC,2MAAA,CAAA,WAAiB,CAAC,SAAS;YAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;sBACzC,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,2MAAA,CAAA,WAAiB,CAAC,IAAI,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties with payment processing\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with detailed success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n\r\n    // The new API returns detailed result information\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      return {\r\n        success: data.success,\r\n        message: data.message,\r\n        totalProcessed: data.totalProcessed,\r\n        successfullyHighlighted: data.successfullyHighlighted,\r\n        alreadyHighlighted: data.alreadyHighlighted,\r\n        failed: data.failed,\r\n        totalCost: data.totalCost,\r\n        propertyResults: data.propertyResults\r\n      };\r\n    } else {\r\n      // Handle HTTP error responses\r\n      const errorData = await response.json().catch(() => ({}));\r\n      return {\r\n        success: false,\r\n        message: errorData.message || `HTTP Error: ${response.status}`,\r\n        errorType: \"api_error\",\r\n        statusCode: response.status\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: error.message || \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAiSsB,oBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties with payment processing\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with detailed success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n\r\n    // The new API returns detailed result information\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      return {\r\n        success: data.success,\r\n        message: data.message,\r\n        totalProcessed: data.totalProcessed,\r\n        successfullyHighlighted: data.successfullyHighlighted,\r\n        alreadyHighlighted: data.alreadyHighlighted,\r\n        failed: data.failed,\r\n        totalCost: data.totalCost,\r\n        propertyResults: data.propertyResults\r\n      };\r\n    } else {\r\n      // Handle HTTP error responses\r\n      const errorData = await response.json().catch(() => ({}));\r\n      return {\r\n        success: false,\r\n        message: errorData.message || `HTTP Error: ${response.status}`,\r\n        errorType: \"api_error\",\r\n        statusCode: response.status\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: error.message || \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0LsB,qBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties with payment processing\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with detailed success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n\r\n    // The new API returns detailed result information\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      return {\r\n        success: data.success,\r\n        message: data.message,\r\n        totalProcessed: data.totalProcessed,\r\n        successfullyHighlighted: data.successfullyHighlighted,\r\n        alreadyHighlighted: data.alreadyHighlighted,\r\n        failed: data.failed,\r\n        totalCost: data.totalCost,\r\n        propertyResults: data.propertyResults\r\n      };\r\n    } else {\r\n      // Handle HTTP error responses\r\n      const errorData = await response.json().catch(() => ({}));\r\n      return {\r\n        success: false,\r\n        message: errorData.message || `HTTP Error: ${response.status}`,\r\n        errorType: \"api_error\",\r\n        statusCode: response.status\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: error.message || \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA4YsB,+BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties with payment processing\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with detailed success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n\r\n    // The new API returns detailed result information\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      return {\r\n        success: data.success,\r\n        message: data.message,\r\n        totalProcessed: data.totalProcessed,\r\n        successfullyHighlighted: data.successfullyHighlighted,\r\n        alreadyHighlighted: data.alreadyHighlighted,\r\n        failed: data.failed,\r\n        totalCost: data.totalCost,\r\n        propertyResults: data.propertyResults\r\n      };\r\n    } else {\r\n      // Handle HTTP error responses\r\n      const errorData = await response.json().catch(() => ({}));\r\n      return {\r\n        success: false,\r\n        message: errorData.message || `HTTP Error: ${response.status}`,\r\n        errorType: \"api_error\",\r\n        statusCode: response.status\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: error.message || \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0QsB,uBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties with payment processing\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with detailed success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n\r\n    // The new API returns detailed result information\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      return {\r\n        success: data.success,\r\n        message: data.message,\r\n        totalProcessed: data.totalProcessed,\r\n        successfullyHighlighted: data.successfullyHighlighted,\r\n        alreadyHighlighted: data.alreadyHighlighted,\r\n        failed: data.failed,\r\n        totalCost: data.totalCost,\r\n        propertyResults: data.propertyResults\r\n      };\r\n    } else {\r\n      // Handle HTTP error responses\r\n      const errorData = await response.json().catch(() => ({}));\r\n      return {\r\n        success: false,\r\n        message: errorData.message || `HTTP Error: ${response.status}`,\r\n        errorType: \"api_error\",\r\n        statusCode: response.status\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: error.message || \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAqiBsB,uBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties with payment processing\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with detailed success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n\r\n    // The new API returns detailed result information\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      return {\r\n        success: data.success,\r\n        message: data.message,\r\n        totalProcessed: data.totalProcessed,\r\n        successfullyHighlighted: data.successfullyHighlighted,\r\n        alreadyHighlighted: data.alreadyHighlighted,\r\n        failed: data.failed,\r\n        totalCost: data.totalCost,\r\n        propertyResults: data.propertyResults\r\n      };\r\n    } else {\r\n      // Handle HTTP error responses\r\n      const errorData = await response.json().catch(() => ({}));\r\n      return {\r\n        success: false,\r\n        message: errorData.message || `HTTP Error: ${response.status}`,\r\n        errorType: \"api_error\",\r\n        statusCode: response.status\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: error.message || \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAynBsB,8BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/hooks/usePropertyList.js"], "sourcesContent": ["import { useState, useCallback, useMemo, useEffect, useRef } from 'react';\nimport { useToast } from '@/hooks/use-toast';\nimport { useAlert } from '@/contexts/AlertContext';\nimport { useRouter } from 'next/navigation';\nimport { useTranslations } from 'next-intl';\nimport {\n  getPropertyByUser,\n  deletePropertyById,\n  verifyPropertyRemainingTimes,\n  updatePropertyStatus,\n  bulkDeleteProperties,\n  bulkUpdatePropertyHighlight,\n} from '@/app/actions/server/property';\nimport { PropertyStatus } from '@/lib/enum';\n\n// Define filter keys\nconst FilterKeys = {\n  ALL: \"all\",\n  APPROVED: \"Approved\",\n  PENDING_APPROVAL: \"PendingApproval\",\n  REJECTED_BY_ADMIN: \"RejectedByAdmin\",\n  REJECTED_DUE_TO_UNPAID: \"RejectedDueToUnpaid\",\n  WAITING_PAYMENT: \"WaitingPayment\",\n  EXPIRED: \"Expired\",\n  DRAFT: \"Draft\",\n  SOLD: \"Sold\",\n};\n\nexport function usePropertyList(initialData, initialFilterCounts = null) {\n  const [data, setData] = useState(initialData || []);\n  const [loadingId, setLoadingId] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isLoadingFilterCounts, setIsLoadingFilterCounts] = useState(!initialFilterCounts); // Only load if not provided\n  const [isInitialized, setIsInitialized] = useState(!!initialData && !!initialFilterCounts); // Initialize if both are provided\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(\"\");\n  const [activeFilter, setActiveFilter] = useState(FilterKeys.ALL);\n  const [selectedIds, setSelectedIds] = useState(new Set());\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize] = useState(10);\n  const { toast } = useToast();\n  const { showAlert } = useAlert();\n  const router = useRouter();\n  const t = useTranslations(\"PropertyList\");\n  \n  // Refs for performance optimization\n  const searchTimeoutRef = useRef(null);\n  \n  // State for filter counts - use server data if available, otherwise default to 0\n  const [filterCounts, setFilterCounts] = useState(() => {\n    if (initialFilterCounts) {\n      return {\n        [FilterKeys.ALL]: initialFilterCounts.total || 0,\n        [FilterKeys.APPROVED]: initialFilterCounts.approved || 0,\n        [FilterKeys.PENDING_APPROVAL]: initialFilterCounts.pendingApproval || 0,\n        [FilterKeys.REJECTED_BY_ADMIN]: initialFilterCounts.rejectedByAdmin || 0,\n        [FilterKeys.REJECTED_DUE_TO_UNPAID]: initialFilterCounts.rejectedDueToUnpaid || 0,\n        [FilterKeys.WAITING_PAYMENT]: initialFilterCounts.waitingPayment || 0,\n        [FilterKeys.EXPIRED]: initialFilterCounts.expired || 0,\n        [FilterKeys.DRAFT]: initialFilterCounts.draft || 0,\n        [FilterKeys.SOLD]: initialFilterCounts.sold || 0,\n      };\n    }\n    return {\n      [FilterKeys.ALL]: 0,\n      [FilterKeys.APPROVED]: 0,\n      [FilterKeys.PENDING_APPROVAL]: 0,\n      [FilterKeys.REJECTED_BY_ADMIN]: 0,\n      [FilterKeys.REJECTED_DUE_TO_UNPAID]: 0,\n      [FilterKeys.WAITING_PAYMENT]: 0,\n      [FilterKeys.EXPIRED]: 0,\n      [FilterKeys.DRAFT]: 0,\n      [FilterKeys.SOLD]: 0,\n    };\n  });\n\n  // Debounce search input\n  useEffect(() => {\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n    \n    searchTimeoutRef.current = setTimeout(() => {\n      setDebouncedSearchTerm(searchTerm);\n    }, 300); // 300ms debounce\n    \n    return () => {\n      if (searchTimeoutRef.current) {\n        clearTimeout(searchTimeoutRef.current);\n      }\n    };\n  }, [searchTerm]);\n\n  // Fetch filter counts from server\n  const fetchFilterCounts = useCallback(async () => {\n    setIsLoadingFilterCounts(true);\n    try {\n      const result = await getPropertyByUser(\"counts\");\n      if (result && result?.success && result?.data) {\n        setFilterCounts({\n          [FilterKeys.ALL]: result.data.total || 0,\n          [FilterKeys.APPROVED]: result.data.approved || 0,\n          [FilterKeys.PENDING_APPROVAL]: result.data.pendingApproval || 0,\n          [FilterKeys.REJECTED_BY_ADMIN]: result.data.rejectedByAdmin || 0,\n          [FilterKeys.REJECTED_DUE_TO_UNPAID]: result.data.rejectedDueToUnpaid || 0,\n          [FilterKeys.WAITING_PAYMENT]: result.data.waitingPayment || 0,\n          [FilterKeys.EXPIRED]: result.data.expired || 0,\n          [FilterKeys.DRAFT]: result.data.draft || 0,\n          [FilterKeys.SOLD]: result.data.sold || 0,\n        });\n      }\n    } catch (error) {\n      console.error(\"Error fetching filter counts:\", error);\n      toast({\n        description: \"Failed to fetch property statistics\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsLoadingFilterCounts(false);\n    }\n  }, [toast]);\n\n  // Data fetching logic\n  const fetchProperties = useCallback(\n    async (status = null, page = 1, size = pageSize) => {\n      setIsLoading(true);\n      try {\n        const result = await getPropertyByUser(status, page, size);\n\n        if (result.success && result.data) {\n          const propertyData = result.data.items || [];\n          setData(propertyData);\n          setCurrentPage(result.data.currentPage || page);\n          fetchFilterCounts();\n        } else {\n          console.error(\"API returned error:\", result);\n          toast({\n            description: result.message || \"Failed to fetch properties\",\n            variant: \"destructive\",\n          });\n        }\n      } catch (error) {\n        console.error(\"Error fetching properties:\", error);\n        toast({\n          description: \"An unexpected error occurred\",\n          variant: \"destructive\",\n        });\n      } finally {\n        setIsLoading(false);\n      }\n    },\n    [pageSize, toast, fetchFilterCounts]\n  );\n\n  // Initialize component - only fetch what we don't have from server\n  useEffect(() => {\n    const initializeComponent = async () => {\n      // Only fetch filter counts if not provided from server\n      if (!initialFilterCounts) {\n        await fetchFilterCounts();\n      }\n\n      // Handle initial data or fetch properties\n      if (initialData && Array.isArray(initialData) && initialData.length > 0 && activeFilter === FilterKeys.ALL) {\n        setData(initialData);\n      } else {\n        const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;\n        await fetchProperties(statusParam, 1, pageSize);\n      }\n\n      setIsInitialized(true);\n    };\n\n    // If we have both initial data and filter counts, we're already initialized\n    if (initialData && initialFilterCounts && activeFilter === FilterKeys.ALL) {\n      setIsInitialized(true);\n      return;\n    }\n\n    initializeComponent();\n  }, []); // Only run once on mount\n\n  // Handle filter changes after initialization\n  useEffect(() => {\n    if (!isInitialized) return;\n    \n    const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;\n    fetchProperties(statusParam, 1, pageSize);\n  }, [activeFilter, isInitialized, fetchProperties, pageSize]);\n\n  // Filtering logic for search - optimized with debounced search\n  const filteredData = useMemo(() => {\n    if (!Array.isArray(data)) {\n      console.error(\"Data is not an array:\", data);\n      return [];\n    }\n\n    if (!debouncedSearchTerm.trim()) {\n      return data;\n    }\n\n    const searchLower = debouncedSearchTerm.toLowerCase();\n    return data.filter((property) => {\n      return property && (\n        (property.name && property.name.toLowerCase().includes(searchLower)) ||\n        (property.address && property.address.toLowerCase().includes(searchLower)) ||\n        (property.addressSelected && property.addressSelected.toLowerCase().includes(searchLower))\n      );\n    });\n  }, [data, debouncedSearchTerm]);\n\n  // Memoized computed values for better performance\n  const computedValues = useMemo(() => {\n    const isOverallLoading = isLoading || isLoadingFilterCounts || !isInitialized;\n    const isAllSelected = filteredData.length > 0 && selectedIds.size === filteredData.length;\n    const isIndeterminate = selectedIds.size > 0 && selectedIds.size < filteredData.length;\n    const isEmptyDatabase = !isOverallLoading && filterCounts[FilterKeys.ALL] === 0;\n    const isEmptySearchResults = !isOverallLoading && filterCounts[FilterKeys.ALL] > 0 && (!filteredData || filteredData.length === 0);\n    const hasResults = !isOverallLoading && filteredData && filteredData.length > 0;\n    \n    return {\n      isOverallLoading,\n      isAllSelected,\n      isIndeterminate,\n      isEmptyDatabase,\n      isEmptySearchResults,\n      hasResults,\n    };\n  }, [isLoading, isLoadingFilterCounts, isInitialized, filteredData, selectedIds.size, filterCounts]);\n\n  // Handler functions\n  const handleEdit = useCallback(\n    (propertyId) => {\n      router.push(`/user/bds/${propertyId}`);\n    },\n    [router]\n  );\n\n  const handleDelete = useCallback(\n    async (propertyId) => {\n      showAlert({\n        title: t(\"deleteConfirmTitle\"),\n        message: t(\"deleteConfirmMessage\"),\n        onConfirm: async () => {\n          setLoadingId(propertyId);\n          try {\n            const result = await deletePropertyById(propertyId);\n            if (result.success) {\n              setSelectedIds((prev) => {\n                const newSet = new Set(prev);\n                newSet.delete(propertyId);\n                return newSet;\n              });\n\n              toast({\n                description: t(\"deleteSuccessToast\"),\n                className: \"bg-teal-600 text-white\",\n              });\n\n              fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);\n            } else {\n              toast({\n                description: result.message || t(\"deleteErrorToast\"),\n                variant: \"destructive\",\n              });\n            }\n          } catch (error) {\n            toast({\n              description: t(\"deleteErrorToast\"),\n              variant: \"destructive\",\n            });\n          } finally {\n            setLoadingId(null);\n          }\n        },\n      });\n    },\n    [showAlert, toast, t, fetchProperties, activeFilter, pageSize]\n  );\n\n  const handleSendToReviewRequest = useCallback(\n    async (propertyId) => {\n      setLoadingId(propertyId);\n      try {\n        const remainingTimes = await verifyPropertyRemainingTimes(propertyId);\n        if (remainingTimes.success) {\n          showAlert({\n            title: t(\"verifyConfirmTitle\"),\n            message: t(\"verifyConfirmMessage\", { remainingTimes: remainingTimes.data }),\n            onConfirm: async () => {\n              try {\n                setLoadingId(propertyId);\n                const formData = new FormData();\n                formData.append(\"propertyId\", propertyId);\n                formData.append(\"status\", PropertyStatus.PENDING_APPROVAL);\n\n                const result = await updatePropertyStatus(formData);\n                if (result.success) {\n                  toast({\n                    description: t(\"verifySuccessToast\"),\n                    className: \"bg-teal-600 text-white\",\n                  });\n\n                  fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);\n                } else {\n                  toast({\n                    description: result?.message || t(\"verifyErrorToast\"),\n                    variant: \"destructive\",\n                  });\n                }\n              } catch (error) {\n                console.error(\"Error sending verification request:\", error);\n                toast({\n                  description: t(\"verifyGenericErrorToast\"),\n                  variant: \"destructive\",\n                });\n              } finally {\n                setLoadingId(null);\n              }\n            },\n            hasCancel: true,\n            onCancel: () => setLoadingId(null),\n          });\n        } else {\n          setLoadingId(null);\n          toast({\n            description: remainingTimes?.message || t(\"verifyCheckErrorToast\"),\n            className: \"bg-red-600 text-white\",\n          });\n        }\n      } catch (error) {\n        setLoadingId(null);\n        console.error(\"Error checking remaining verification times:\", error);\n        toast({\n          description: t(\"verifyGenericErrorToast\"),\n          className: \"bg-red-600 text-white\",\n        });\n      }\n    },\n    [showAlert, toast, t, fetchProperties, activeFilter, pageSize]\n  );\n\n  const handleBulkHighlight = useCallback(() => {\n    if (selectedIds.size === 0) return;\n\n    showAlert({\n      title: t(\"bulkHighlightConfirmTitle\", { count: selectedIds.size }),\n      message: t(\"bulkHighlightConfirmMessage\"),\n      onConfirm: async () => {\n        setLoadingId(\"bulk-highlight\");\n        try {\n          const result = await bulkUpdatePropertyHighlight(Array.from(selectedIds), true);\n\n          // Handle the new API response structure\n          if (result.success) {\n            const successCount = result.successfullyHighlighted || 0;\n            const alreadyHighlighted = result.alreadyHighlighted || 0;\n            const failed = result.failed || 0;\n            const totalCost = result.totalCost || 0;\n\n            // Show detailed success message\n            let successMessage = \"\";\n            if (successCount > 0) {\n              successMessage += t(\"bulkHighlightSuccessToast\", { count: successCount });\n              if (totalCost > 0) {\n                successMessage += ` (${totalCost.toLocaleString('vi-VN')} VND)`;\n              }\n            }\n            if (alreadyHighlighted > 0) {\n              if (successMessage) successMessage += \". \";\n              successMessage += t(\"bulkHighlightAlreadyHighlighted\", { count: alreadyHighlighted });\n            }\n            if (failed > 0) {\n              if (successMessage) successMessage += \". \";\n              successMessage += t(\"bulkHighlightFailed\", { count: failed });\n            }\n\n            toast({\n              description: successMessage,\n              className: \"bg-teal-600 text-white\",\n            });\n\n            // Clear selection and refresh properties\n            setSelectedIds(new Set());\n            fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);\n          } else {\n            // Handle wallet insufficient balance and other errors\n            const errorMessage = result.message || t(\"bulkHighlightErrorToast\");\n\n            // Check if it's a wallet balance error\n            if (errorMessage.includes(\"Số dư ví không đủ\") || errorMessage.includes(\"wallet balance\")) {\n              toast({\n                description: errorMessage,\n                variant: \"destructive\",\n                duration: 8000, // Show longer for wallet errors\n              });\n            } else {\n              toast({\n                description: errorMessage,\n                variant: \"destructive\",\n              });\n            }\n          }\n        } catch (error) {\n          console.error(\"Error highlighting properties:\", error);\n\n          // Handle network errors or API errors\n          let errorMessage = t(\"bulkHighlightGenericErrorToast\");\n          if (error.response?.data?.message) {\n            errorMessage = error.response.data.message;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n\n          toast({\n            description: errorMessage,\n            variant: \"destructive\",\n          });\n        } finally {\n          setLoadingId(null);\n        }\n      },\n    });\n  }, [selectedIds, showAlert, toast, fetchProperties, activeFilter, pageSize, t, setSelectedIds]);\n\n  const handleBulkRenew = useCallback(() => {\n    if (selectedIds.size === 0) return;\n\n    // For now, just show a message since renew functionality is not implemented in the API\n    toast({\n      description: t(\"bulkRenewFeatureInDevelopment\"),\n      variant: \"default\",\n    });\n\n    // When API is available, implement similar to handleBulkHighlight\n    // using the appropriate API endpoint\n  }, [selectedIds, toast, t]);\n\n  const handleBulkDelete = useCallback(() => {\n    if (selectedIds.size === 0) return;\n\n    showAlert({\n      title: t(\"bulkDeleteConfirmTitle\", { count: selectedIds.size }),\n      message: t(\"bulkDeleteConfirmMessage\"),\n      onConfirm: async () => {\n        setLoadingId(\"bulk-delete\");\n        try {\n          const result = await bulkDeleteProperties(Array.from(selectedIds));\n\n          if (result.success) {\n            setSelectedIds(new Set());\n\n            toast({\n              description: t(\"bulkDeleteSuccessToast\", { count: selectedIds.size }),\n              className: \"bg-teal-600 text-white\",\n            });\n\n            fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);\n          } else {\n            toast({\n              description: result.message || t(\"bulkDeleteErrorToast\"),\n              variant: \"destructive\",\n            });\n          }\n        } catch (error) {\n          console.error(\"Error deleting properties:\", error);\n          toast({\n            description: t(\"bulkDeleteGenericErrorToast\"),\n            variant: \"destructive\",\n          });\n        } finally {\n          setLoadingId(null);\n        }\n      },\n    });\n  }, [selectedIds, showAlert, toast, fetchProperties, activeFilter, pageSize, t]);\n\n  return {\n    // State\n    data,\n    filteredData,\n    loadingId,\n    setLoadingId,\n    searchTerm,\n    setSearchTerm,\n    activeFilter,\n    setActiveFilter,\n    selectedIds,\n    setSelectedIds,\n    currentPage,\n    pageSize,\n    filterCounts,\n\n    // Computed values\n    ...computedValues,\n\n    // Functions\n    fetchProperties,\n    fetchFilterCounts,\n    handleEdit,\n    handleDelete,\n    handleSendToReviewRequest,\n    handleBulkHighlight,\n    handleBulkRenew,\n    handleBulkDelete,\n\n    // Constants\n    FilterKeys,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;;;;;;;AAEA,qBAAqB;AACrB,MAAM,aAAa;IACjB,KAAK;IACL,UAAU;IACV,kBAAkB;IAClB,mBAAmB;IACnB,wBAAwB;IACxB,iBAAiB;IACjB,SAAS;IACT,OAAO;IACP,MAAM;AACR;AAEO,SAAS,gBAAgB,WAAW,EAAE,sBAAsB,IAAI;;IACrE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,EAAE;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,sBAAsB,4BAA4B;IACtH,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,sBAAsB,kCAAkC;IAC9H,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,GAAG;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,oCAAoC;IACpC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,iFAAiF;IACjF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;oCAAE;YAC/C,IAAI,qBAAqB;gBACvB,OAAO;oBACL,CAAC,WAAW,GAAG,CAAC,EAAE,oBAAoB,KAAK,IAAI;oBAC/C,CAAC,WAAW,QAAQ,CAAC,EAAE,oBAAoB,QAAQ,IAAI;oBACvD,CAAC,WAAW,gBAAgB,CAAC,EAAE,oBAAoB,eAAe,IAAI;oBACtE,CAAC,WAAW,iBAAiB,CAAC,EAAE,oBAAoB,eAAe,IAAI;oBACvE,CAAC,WAAW,sBAAsB,CAAC,EAAE,oBAAoB,mBAAmB,IAAI;oBAChF,CAAC,WAAW,eAAe,CAAC,EAAE,oBAAoB,cAAc,IAAI;oBACpE,CAAC,WAAW,OAAO,CAAC,EAAE,oBAAoB,OAAO,IAAI;oBACrD,CAAC,WAAW,KAAK,CAAC,EAAE,oBAAoB,KAAK,IAAI;oBACjD,CAAC,WAAW,IAAI,CAAC,EAAE,oBAAoB,IAAI,IAAI;gBACjD;YACF;YACA,OAAO;gBACL,CAAC,WAAW,GAAG,CAAC,EAAE;gBAClB,CAAC,WAAW,QAAQ,CAAC,EAAE;gBACvB,CAAC,WAAW,gBAAgB,CAAC,EAAE;gBAC/B,CAAC,WAAW,iBAAiB,CAAC,EAAE;gBAChC,CAAC,WAAW,sBAAsB,CAAC,EAAE;gBACrC,CAAC,WAAW,eAAe,CAAC,EAAE;gBAC9B,CAAC,WAAW,OAAO,CAAC,EAAE;gBACtB,CAAC,WAAW,KAAK,CAAC,EAAE;gBACpB,CAAC,WAAW,IAAI,CAAC,EAAE;YACrB;QACF;;IAEA,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,iBAAiB,OAAO,GAAG;6CAAW;oBACpC,uBAAuB;gBACzB;4CAAG,MAAM,iBAAiB;YAE1B;6CAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;oCAAG;QAAC;KAAW;IAEf,kCAAkC;IAClC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACpC,yBAAyB;YACzB,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,oBAAiB,AAAD,EAAE;gBACvC,IAAI,UAAU,QAAQ,WAAW,QAAQ,MAAM;oBAC7C,gBAAgB;wBACd,CAAC,WAAW,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,IAAI;wBACvC,CAAC,WAAW,QAAQ,CAAC,EAAE,OAAO,IAAI,CAAC,QAAQ,IAAI;wBAC/C,CAAC,WAAW,gBAAgB,CAAC,EAAE,OAAO,IAAI,CAAC,eAAe,IAAI;wBAC9D,CAAC,WAAW,iBAAiB,CAAC,EAAE,OAAO,IAAI,CAAC,eAAe,IAAI;wBAC/D,CAAC,WAAW,sBAAsB,CAAC,EAAE,OAAO,IAAI,CAAC,mBAAmB,IAAI;wBACxE,CAAC,WAAW,eAAe,CAAC,EAAE,OAAO,IAAI,CAAC,cAAc,IAAI;wBAC5D,CAAC,WAAW,OAAO,CAAC,EAAE,OAAO,IAAI,CAAC,OAAO,IAAI;wBAC7C,CAAC,WAAW,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,IAAI;wBACzC,CAAC,WAAW,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,IAAI,IAAI;oBACzC;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM;oBACJ,aAAa;oBACb,SAAS;gBACX;YACF,SAAU;gBACR,yBAAyB;YAC3B;QACF;yDAAG;QAAC;KAAM;IAEV,sBAAsB;IACtB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAChC,OAAO,SAAS,IAAI,EAAE,OAAO,CAAC,EAAE,OAAO,QAAQ;YAC7C,aAAa;YACb,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,MAAM;gBAErD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,MAAM,eAAe,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE;oBAC5C,QAAQ;oBACR,eAAe,OAAO,IAAI,CAAC,WAAW,IAAI;oBAC1C;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,MAAM;wBACJ,aAAa,OAAO,OAAO,IAAI;wBAC/B,SAAS;oBACX;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,MAAM;oBACJ,aAAa;oBACb,SAAS;gBACX;YACF,SAAU;gBACR,aAAa;YACf;QACF;uDACA;QAAC;QAAU;QAAO;KAAkB;IAGtC,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;iEAAsB;oBAC1B,uDAAuD;oBACvD,IAAI,CAAC,qBAAqB;wBACxB,MAAM;oBACR;oBAEA,0CAA0C;oBAC1C,IAAI,eAAe,MAAM,OAAO,CAAC,gBAAgB,YAAY,MAAM,GAAG,KAAK,iBAAiB,WAAW,GAAG,EAAE;wBAC1G,QAAQ;oBACV,OAAO;wBACL,MAAM,cAAc,iBAAiB,WAAW,GAAG,GAAG,eAAe;wBACrE,MAAM,gBAAgB,aAAa,GAAG;oBACxC;oBAEA,iBAAiB;gBACnB;;YAEA,4EAA4E;YAC5E,IAAI,eAAe,uBAAuB,iBAAiB,WAAW,GAAG,EAAE;gBACzE,iBAAiB;gBACjB;YACF;YAEA;QACF;oCAAG,EAAE,GAAG,yBAAyB;IAEjC,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,eAAe;YAEpB,MAAM,cAAc,iBAAiB,WAAW,GAAG,GAAG,eAAe;YACrE,gBAAgB,aAAa,GAAG;QAClC;oCAAG;QAAC;QAAc;QAAe;QAAiB;KAAS;IAE3D,+DAA+D;IAC/D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAC3B,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;gBACxB,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,OAAO,EAAE;YACX;YAEA,IAAI,CAAC,oBAAoB,IAAI,IAAI;gBAC/B,OAAO;YACT;YAEA,MAAM,cAAc,oBAAoB,WAAW;YACnD,OAAO,KAAK,MAAM;yDAAC,CAAC;oBAClB,OAAO,YAAY,CACjB,AAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACtD,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC5D,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,YAC/E;gBACF;;QACF;gDAAG;QAAC;QAAM;KAAoB;IAE9B,kDAAkD;IAClD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE;YAC7B,MAAM,mBAAmB,aAAa,yBAAyB,CAAC;YAChE,MAAM,gBAAgB,aAAa,MAAM,GAAG,KAAK,YAAY,IAAI,KAAK,aAAa,MAAM;YACzF,MAAM,kBAAkB,YAAY,IAAI,GAAG,KAAK,YAAY,IAAI,GAAG,aAAa,MAAM;YACtF,MAAM,kBAAkB,CAAC,oBAAoB,YAAY,CAAC,WAAW,GAAG,CAAC,KAAK;YAC9E,MAAM,uBAAuB,CAAC,oBAAoB,YAAY,CAAC,WAAW,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,gBAAgB,aAAa,MAAM,KAAK,CAAC;YACjI,MAAM,aAAa,CAAC,oBAAoB,gBAAgB,aAAa,MAAM,GAAG;YAE9E,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;kDAAG;QAAC;QAAW;QAAuB;QAAe;QAAc,YAAY,IAAI;QAAE;KAAa;IAElG,oBAAoB;IACpB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAC3B,CAAC;YACC,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,YAAY;QACvC;kDACA;QAAC;KAAO;IAGV,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC7B,OAAO;YACL,UAAU;gBACR,OAAO,EAAE;gBACT,SAAS,EAAE;gBACX,SAAS;iEAAE;wBACT,aAAa;wBACb,IAAI;4BACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,qBAAkB,AAAD,EAAE;4BACxC,IAAI,OAAO,OAAO,EAAE;gCAClB;iFAAe,CAAC;wCACd,MAAM,SAAS,IAAI,IAAI;wCACvB,OAAO,MAAM,CAAC;wCACd,OAAO;oCACT;;gCAEA,MAAM;oCACJ,aAAa,EAAE;oCACf,WAAW;gCACb;gCAEA,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,GAAG;4BAC5E,OAAO;gCACL,MAAM;oCACJ,aAAa,OAAO,OAAO,IAAI,EAAE;oCACjC,SAAS;gCACX;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,MAAM;gCACJ,aAAa,EAAE;gCACf,SAAS;4BACX;wBACF,SAAU;4BACR,aAAa;wBACf;oBACF;;YACF;QACF;oDACA;QAAC;QAAW;QAAO;QAAG;QAAiB;QAAc;KAAS;IAGhE,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAC1C,OAAO;YACL,aAAa;YACb,IAAI;gBACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,mKAAA,CAAA,+BAA4B,AAAD,EAAE;gBAC1D,IAAI,eAAe,OAAO,EAAE;oBAC1B,UAAU;wBACR,OAAO,EAAE;wBACT,SAAS,EAAE,wBAAwB;4BAAE,gBAAgB,eAAe,IAAI;wBAAC;wBACzE,SAAS;sFAAE;gCACT,IAAI;oCACF,aAAa;oCACb,MAAM,WAAW,IAAI;oCACrB,SAAS,MAAM,CAAC,cAAc;oCAC9B,SAAS,MAAM,CAAC,UAAU,8GAAA,CAAA,iBAAc,CAAC,gBAAgB;oCAEzD,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,uBAAoB,AAAD,EAAE;oCAC1C,IAAI,OAAO,OAAO,EAAE;wCAClB,MAAM;4CACJ,aAAa,EAAE;4CACf,WAAW;wCACb;wCAEA,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,GAAG;oCAC5E,OAAO;wCACL,MAAM;4CACJ,aAAa,QAAQ,WAAW,EAAE;4CAClC,SAAS;wCACX;oCACF;gCACF,EAAE,OAAO,OAAO;oCACd,QAAQ,KAAK,CAAC,uCAAuC;oCACrD,MAAM;wCACJ,aAAa,EAAE;wCACf,SAAS;oCACX;gCACF,SAAU;oCACR,aAAa;gCACf;4BACF;;wBACA,WAAW;wBACX,QAAQ;sFAAE,IAAM,aAAa;;oBAC/B;gBACF,OAAO;oBACL,aAAa;oBACb,MAAM;wBACJ,aAAa,gBAAgB,WAAW,EAAE;wBAC1C,WAAW;oBACb;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,aAAa;gBACb,QAAQ,KAAK,CAAC,gDAAgD;gBAC9D,MAAM;oBACJ,aAAa,EAAE;oBACf,WAAW;gBACb;YACF;QACF;iEACA;QAAC;QAAW;QAAO;QAAG;QAAiB;QAAc;KAAS;IAGhE,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACtC,IAAI,YAAY,IAAI,KAAK,GAAG;YAE5B,UAAU;gBACR,OAAO,EAAE,6BAA6B;oBAAE,OAAO,YAAY,IAAI;gBAAC;gBAChE,SAAS,EAAE;gBACX,SAAS;wEAAE;wBACT,aAAa;wBACb,IAAI;4BACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,8BAA2B,AAAD,EAAE,MAAM,IAAI,CAAC,cAAc;4BAE1E,wCAAwC;4BACxC,IAAI,OAAO,OAAO,EAAE;gCAClB,MAAM,eAAe,OAAO,uBAAuB,IAAI;gCACvD,MAAM,qBAAqB,OAAO,kBAAkB,IAAI;gCACxD,MAAM,SAAS,OAAO,MAAM,IAAI;gCAChC,MAAM,YAAY,OAAO,SAAS,IAAI;gCAEtC,gCAAgC;gCAChC,IAAI,iBAAiB;gCACrB,IAAI,eAAe,GAAG;oCACpB,kBAAkB,EAAE,6BAA6B;wCAAE,OAAO;oCAAa;oCACvE,IAAI,YAAY,GAAG;wCACjB,kBAAkB,CAAC,EAAE,EAAE,UAAU,cAAc,CAAC,SAAS,KAAK,CAAC;oCACjE;gCACF;gCACA,IAAI,qBAAqB,GAAG;oCAC1B,IAAI,gBAAgB,kBAAkB;oCACtC,kBAAkB,EAAE,mCAAmC;wCAAE,OAAO;oCAAmB;gCACrF;gCACA,IAAI,SAAS,GAAG;oCACd,IAAI,gBAAgB,kBAAkB;oCACtC,kBAAkB,EAAE,uBAAuB;wCAAE,OAAO;oCAAO;gCAC7D;gCAEA,MAAM;oCACJ,aAAa;oCACb,WAAW;gCACb;gCAEA,yCAAyC;gCACzC,eAAe,IAAI;gCACnB,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,GAAG;4BAC5E,OAAO;gCACL,sDAAsD;gCACtD,MAAM,eAAe,OAAO,OAAO,IAAI,EAAE;gCAEzC,uCAAuC;gCACvC,IAAI,aAAa,QAAQ,CAAC,wBAAwB,aAAa,QAAQ,CAAC,mBAAmB;oCACzF,MAAM;wCACJ,aAAa;wCACb,SAAS;wCACT,UAAU;oCACZ;gCACF,OAAO;oCACL,MAAM;wCACJ,aAAa;wCACb,SAAS;oCACX;gCACF;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,kCAAkC;4BAEhD,sCAAsC;4BACtC,IAAI,eAAe,EAAE;4BACrB,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gCACjC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;4BAC5C,OAAO,IAAI,MAAM,OAAO,EAAE;gCACxB,eAAe,MAAM,OAAO;4BAC9B;4BAEA,MAAM;gCACJ,aAAa;gCACb,SAAS;4BACX;wBACF,SAAU;4BACR,aAAa;wBACf;oBACF;;YACF;QACF;2DAAG;QAAC;QAAa;QAAW;QAAO;QAAiB;QAAc;QAAU;QAAG;KAAe;IAE9F,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAClC,IAAI,YAAY,IAAI,KAAK,GAAG;YAE5B,uFAAuF;YACvF,MAAM;gBACJ,aAAa,EAAE;gBACf,SAAS;YACX;QAEA,kEAAkE;QAClE,qCAAqC;QACvC;uDAAG;QAAC;QAAa;QAAO;KAAE;IAE1B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACnC,IAAI,YAAY,IAAI,KAAK,GAAG;YAE5B,UAAU;gBACR,OAAO,EAAE,0BAA0B;oBAAE,OAAO,YAAY,IAAI;gBAAC;gBAC7D,SAAS,EAAE;gBACX,SAAS;qEAAE;wBACT,aAAa;wBACb,IAAI;4BACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,IAAI,CAAC;4BAErD,IAAI,OAAO,OAAO,EAAE;gCAClB,eAAe,IAAI;gCAEnB,MAAM;oCACJ,aAAa,EAAE,0BAA0B;wCAAE,OAAO,YAAY,IAAI;oCAAC;oCACnE,WAAW;gCACb;gCAEA,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,GAAG;4BAC5E,OAAO;gCACL,MAAM;oCACJ,aAAa,OAAO,OAAO,IAAI,EAAE;oCACjC,SAAS;gCACX;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,8BAA8B;4BAC5C,MAAM;gCACJ,aAAa,EAAE;gCACf,SAAS;4BACX;wBACF,SAAU;4BACR,aAAa;wBACf;oBACF;;YACF;QACF;wDAAG;QAAC;QAAa;QAAW;QAAO;QAAiB;QAAc;QAAU;KAAE;IAE9E,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,kBAAkB;QAClB,GAAG,cAAc;QAEjB,YAAY;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,YAAY;QACZ;IACF;AACF;GAjegB;;QAYI,wHAAA,CAAA,WAAQ;QACJ,4HAAA,CAAA,WAAQ;QACf,qIAAA,CAAA,YAAS;QACd,yMAAA,CAAA,kBAAe", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties with payment processing\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with detailed success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n\r\n    // The new API returns detailed result information\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      return {\r\n        success: data.success,\r\n        message: data.message,\r\n        totalProcessed: data.totalProcessed,\r\n        successfullyHighlighted: data.successfullyHighlighted,\r\n        alreadyHighlighted: data.alreadyHighlighted,\r\n        failed: data.failed,\r\n        totalCost: data.totalCost,\r\n        propertyResults: data.propertyResults\r\n      };\r\n    } else {\r\n      // Handle HTTP error responses\r\n      const errorData = await response.json().catch(() => ({}));\r\n      return {\r\n        success: false,\r\n        message: errorData.message || `HTTP Error: ${response.status}`,\r\n        errorType: \"api_error\",\r\n        statusCode: response.status\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: error.message || \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA8lBsB,0BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/dialog.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Dialog as DialogPrimitive } from \"radix-ui\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      {children}\r\n      <DialogPrimitive.Close\r\n        className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)}\r\n    {...props} />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className)}\r\n    {...props} />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,uMAAA,CAAA,SAAe,CAAC,IAAI;AAEnC,MAAM,gBAAgB,uMAAA,CAAA,SAAe,CAAC,OAAO;AAE7C,MAAM,eAAe,uMAAA,CAAA,SAAe,CAAC,MAAM;AAE3C,MAAM,cAAc,uMAAA,CAAA,SAAe,CAAC,KAAK;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC/D,6LAAC,uMAAA,CAAA,SAAe,CAAC,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAPP;AASN,cAAc,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,uMAAA,CAAA,SAAe,CAAC,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBACR;kCACD,6LAAC,uMAAA,CAAA,SAAe,CAAC,KAAK;wBACpB,WAAU;;0CACV,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;MANP;AAQN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;;;;;;MANP;AAQN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,6LAAC,uMAAA,CAAA,SAAe,CAAC,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;;AAEb,YAAY,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACnE,6LAAC,uMAAA,CAAA,SAAe,CAAC,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAEb,kBAAkB,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,WAAW,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 915, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/ShareModal.jsx"], "sourcesContent": ["\"use client\";\nimport { useState } from \"react\"; \nimport { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Copy, Check } from \"lucide-react\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { useTranslations } from \"next-intl\";\nimport Image from \"next/image\";\n\n// Import next-share components\nimport {\n  FacebookShareButton,\n  FacebookIcon,\n  TwitterShareButton,\n  TwitterIcon,\n  LinkedinShareButton,\n  LinkedinIcon,\n  TelegramShareButton,\n  TelegramIcon,\n  WhatsappShareButton,\n  WhatsappIcon,\n  RedditShareButton,\n  RedditIcon,\n  EmailShareButton,\n  EmailIcon,\n  PinterestShareButton,\n  PinterestIcon,\n  ViberShareButton,\n  ViberIcon,\n} from \"next-share\";\n\nconst ShareModal = ({ open, onClose, property }) => {\n  const { toast } = useToast();\n  const [copied, setCopied] = useState(false);\n  const t = useTranslations(\"PropertyShare\");\n\n  // Create a shareable URL for the property\n  const shareUrl = typeof window !== \"undefined\" ? `${window.location.origin}/bds/${property.id}` : `/bds/${property.id}`;\n\n  // Handle copy to clipboard\n  const handleCopyLink = () => {\n    navigator.clipboard\n      .writeText(shareUrl)\n      .then(() => {\n        setCopied(true);\n        toast({\n          description: t(\"copySuccess\"),\n          className: \"bg-teal-600 text-white\",\n        });\n        setTimeout(() => setCopied(false), 2000);\n      })\n      .catch((err) => {\n        console.error(\"Could not copy text: \", err);\n        toast({\n          description: t(\"copyError\"),\n          variant: \"destructive\",\n        });\n      });\n  };\n\n  // Share title and description\n  const shareTitle = `${property.name} | YEZ Home`;\n  const shareDescription = `${t(\"viewProperty\")}: ${property.name} - ${JSON.parse(property?.placeData || \"{}\")?.result?.formatted_address || \"\"}`;\n  console.log(property);\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle>{t(\"title\")}</DialogTitle>\n          <DialogDescription>{t(\"description\")}</DialogDescription>\n        </DialogHeader>\n\n        {/* Property info */}\n        <div className=\"p-4 border rounded-md mb-4\">\n          <div className=\"flex items-center gap-2\">\n            <Image\n              src={property.propertyMedia?.[0]?.mediaURL || \"/placeholder.svg\"}\n              alt={property.name}\n              width={100}\n              height={100}\n              className=\"w-full object-cover rounded-md\"\n              loading=\"lazy\"\n            />\n            <div className=\"flex flex-col items-center gap-2\">\n              <h3 className=\"font-medium text-sm mb-1\">{property.name}</h3>\n\n              <p className=\"text-lg font-bold text-teal-600 mb-2\">\n                {property.price.toLocaleString(\"vi-VN\")} VNĐ\n                {property.postType === \"rent\" && `/${t(\"month\")}`}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Social sharing buttons */}\n        <div className=\"flex flex-wrap gap-3 justify-center mb-4\">\n          <FacebookShareButton url={shareUrl} quote={shareDescription} className=\"mx-1\">\n            <FacebookIcon size={40} round />\n          </FacebookShareButton>\n\n          <TwitterShareButton url={shareUrl} title={shareTitle} className=\"mx-1\">\n            <TwitterIcon size={40} round />\n          </TwitterShareButton>\n\n          <LinkedinShareButton url={shareUrl} title={shareTitle} className=\"mx-1\">\n            <LinkedinIcon size={40} round />\n          </LinkedinShareButton>\n\n          <TelegramShareButton url={shareUrl} title={shareTitle} className=\"mx-1\">\n            <TelegramIcon size={40} round />\n          </TelegramShareButton>\n\n          <WhatsappShareButton url={shareUrl} title={shareTitle} separator=\":: \" className=\"mx-1\">\n            <WhatsappIcon size={40} round />\n          </WhatsappShareButton>\n\n          <PinterestShareButton url={shareUrl} media={property.propertyMedia?.[0]?.mediaURL || \"\"} description={shareDescription} className=\"mx-1\">\n            <PinterestIcon size={40} round />\n          </PinterestShareButton>\n\n          <RedditShareButton url={shareUrl} title={shareTitle} className=\"mx-1\">\n            <RedditIcon size={40} round />\n          </RedditShareButton>\n\n          <ViberShareButton url={shareUrl} title={shareTitle} className=\"mx-1\">\n            <ViberIcon size={40} round />\n          </ViberShareButton>\n\n          <EmailShareButton url={shareUrl} subject={shareTitle} body={shareDescription} className=\"mx-1\">\n            <EmailIcon size={40} round />\n          </EmailShareButton>\n        </div>\n\n        {/* Copy link section */}\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"grid flex-1 gap-2\">\n            <Input value={shareUrl} readOnly className=\"h-9\" />\n          </div>\n          <Button size=\"sm\" className=\"px-3 h-9\" onClick={handleCopyLink}>\n            {copied ? <Check className=\"h-4 w-4\" /> : <Copy className=\"h-4 w-4\" />}\n            <span className=\"sr-only\">{t(\"copy\")}</span>\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\nexport default ShareModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA,+BAA+B;AAC/B;;;AAXA;;;;;;;;;;AAgCA,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE;;IAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,0CAA0C;IAC1C,MAAM,WAAW,uCAAgC,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;IAE/F,2BAA2B;IAC3B,MAAM,iBAAiB;QACrB,UAAU,SAAS,CAChB,SAAS,CAAC,UACV,IAAI,CAAC;YACJ,UAAU;YACV,MAAM;gBACJ,aAAa,EAAE;gBACf,WAAW;YACb;YACA,WAAW,IAAM,UAAU,QAAQ;QACrC,GACC,KAAK,CAAC,CAAC;YACN,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;gBACJ,aAAa,EAAE;gBACf,SAAS;YACX;QACF;IACJ;IAEA,8BAA8B;IAC9B,MAAM,aAAa,GAAG,SAAS,IAAI,CAAC,WAAW,CAAC;IAChD,MAAM,mBAAmB,GAAG,EAAE,gBAAgB,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,UAAU,aAAa,OAAO,QAAQ,qBAAqB,IAAI;IAC/I,QAAQ,GAAG,CAAC;IAEZ,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;sCAAE,EAAE;;;;;;sCAChB,6LAAC,8HAAA,CAAA,oBAAiB;sCAAE,EAAE;;;;;;;;;;;;8BAIxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,SAAS,aAAa,EAAE,CAAC,EAAE,EAAE,YAAY;gCAC9C,KAAK,SAAS,IAAI;gCAClB,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,SAAQ;;;;;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4B,SAAS,IAAI;;;;;;kDAEvD,6LAAC;wCAAE,WAAU;;4CACV,SAAS,KAAK,CAAC,cAAc,CAAC;4CAAS;4CACvC,SAAS,QAAQ,KAAK,UAAU,CAAC,CAAC,EAAE,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;8BAOzD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,sBAAmB;4BAAC,KAAK;4BAAU,OAAO;4BAAkB,WAAU;sCACrE,cAAA,6LAAC,+JAAA,CAAA,eAAY;gCAAC,MAAM;gCAAI,KAAK;;;;;;;;;;;sCAG/B,6LAAC,+JAAA,CAAA,qBAAkB;4BAAC,KAAK;4BAAU,OAAO;4BAAY,WAAU;sCAC9D,cAAA,6LAAC,+JAAA,CAAA,cAAW;gCAAC,MAAM;gCAAI,KAAK;;;;;;;;;;;sCAG9B,6LAAC,+JAAA,CAAA,sBAAmB;4BAAC,KAAK;4BAAU,OAAO;4BAAY,WAAU;sCAC/D,cAAA,6LAAC,+JAAA,CAAA,eAAY;gCAAC,MAAM;gCAAI,KAAK;;;;;;;;;;;sCAG/B,6LAAC,+JAAA,CAAA,sBAAmB;4BAAC,KAAK;4BAAU,OAAO;4BAAY,WAAU;sCAC/D,cAAA,6LAAC,+JAAA,CAAA,eAAY;gCAAC,MAAM;gCAAI,KAAK;;;;;;;;;;;sCAG/B,6LAAC,+JAAA,CAAA,sBAAmB;4BAAC,KAAK;4BAAU,OAAO;4BAAY,WAAU;4BAAM,WAAU;sCAC/E,cAAA,6LAAC,+JAAA,CAAA,eAAY;gCAAC,MAAM;gCAAI,KAAK;;;;;;;;;;;sCAG/B,6LAAC,+JAAA,CAAA,uBAAoB;4BAAC,KAAK;4BAAU,OAAO,SAAS,aAAa,EAAE,CAAC,EAAE,EAAE,YAAY;4BAAI,aAAa;4BAAkB,WAAU;sCAChI,cAAA,6LAAC,+JAAA,CAAA,gBAAa;gCAAC,MAAM;gCAAI,KAAK;;;;;;;;;;;sCAGhC,6LAAC,+JAAA,CAAA,oBAAiB;4BAAC,KAAK;4BAAU,OAAO;4BAAY,WAAU;sCAC7D,cAAA,6LAAC,+JAAA,CAAA,aAAU;gCAAC,MAAM;gCAAI,KAAK;;;;;;;;;;;sCAG7B,6LAAC,+JAAA,CAAA,mBAAgB;4BAAC,KAAK;4BAAU,OAAO;4BAAY,WAAU;sCAC5D,cAAA,6LAAC,+JAAA,CAAA,YAAS;gCAAC,MAAM;gCAAI,KAAK;;;;;;;;;;;sCAG5B,6LAAC,+JAAA,CAAA,mBAAgB;4BAAC,KAAK;4BAAU,SAAS;4BAAY,MAAM;4BAAkB,WAAU;sCACtF,cAAA,6LAAC,+JAAA,CAAA,YAAS;gCAAC,MAAM;gCAAI,KAAK;;;;;;;;;;;;;;;;;8BAK9B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;gCAAC,OAAO;gCAAU,QAAQ;gCAAC,WAAU;;;;;;;;;;;sCAE7C,6LAAC,8HAAA,CAAA,SAAM;4BAAC,MAAK;4BAAK,WAAU;4BAAW,SAAS;;gCAC7C,uBAAS,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAC1D,6LAAC;oCAAK,WAAU;8CAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GApHM;;QACc,wHAAA,CAAA,WAAQ;QAEhB,yMAAA,CAAA,kBAAe;;;KAHrB;uCAsHS", "debugId": null}}, {"offset": {"line": 1308, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/HighlightConfirmDialog.jsx"], "sourcesContent": ["\"use client\";\nimport { useState } from \"react\";\nimport { useTranslations } from \"next-intl\";\nimport { useAuth } from \"@/contexts/AuthContext\";\nimport { useRouter } from \"next/navigation\";\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { AlertTriangle } from \"lucide-react\";\nimport { formatCurrency } from \"@/lib/utils\";\nimport { MemberRank, highlightPrices } from \"@/lib/enum\";\nimport { getMemberRankTranslationKey } from \"@/lib/memberRankUtils\";\n\nconst HighlightConfirmDialog = ({ open, onClose, onConfirm }) => {\n  const t = useTranslations(\"PropertyCard\");\n  const tCommon = useTranslations(\"Common\");\n  const { profile } = useAuth();\n  const walletInfo = profile?.user?.wallet;\n  const router = useRouter();\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  // Get highlight fee based on member rank\n  const getHighlightFee = () => {\n    if (!profile || !profile?.memberRank) {\n      // Default values if profile or memberRank is not available\n      return {\n        fee: highlightPrices[MemberRank.DEFAULT],\n        rankName: tCommon(getMemberRankTranslationKey(MemberRank.DEFAULT)),\n      };\n    }\n\n    // Map the member rank to the corresponding highlight fee\n    const memberRankEnum = profile?.memberRank.toLowerCase();\n    const fee = highlightPrices[memberRankEnum] || highlightPrices[MemberRank.DEFAULT];\n\n    return {\n      fee: fee,\n      rankName: tCommon(getMemberRankTranslationKey(memberRankEnum)),\n    };\n  };\n\n  const highlightFee = getHighlightFee();\n  const hasEnoughBalance = walletInfo && walletInfo.balance >= highlightFee.fee;\n\n  const handleConfirm = async () => {\n    if (!hasEnoughBalance) {\n      return;\n    }\n\n    setIsProcessing(true);\n    try {\n      await onConfirm();\n    } finally {\n      setIsProcessing(false);\n      onClose();\n    }\n  };\n\n  const handleTopUp = () => {\n    onClose();\n    router.push(\"/user/wallet\");\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle>{t(\"highlightConfirmTitle\")}</DialogTitle>\n          <DialogDescription className=\"text-sm text-gray-600\">\n            <span className=\"mb-4 text-sm text-gray-600\">\n              {t(\"highlightConfirmMessage\", {\n                amount: formatCurrency(highlightFee.fee),\n                memberRank: highlightFee.rankName,\n              })}\n            </span>\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"pb-4\">\n          {!hasEnoughBalance && (\n            <div className=\"flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-md mb-4\">\n              <AlertTriangle className=\"h-5 w-5 text-amber-500 mt-0.5 shrink-0\" />\n              <div>\n                <p className=\"text-sm font-medium text-amber-800\">{t(\"insufficientBalanceTitle\")}</p>\n                <p className=\"text-sm text-amber-700 mt-1\">{t(\"insufficientBalanceMessage\")}</p>\n              </div>\n            </div>\n          )}\n\n          <div className=\"flex flex-col gap-2 bg-gray-50 p-3 rounded-md\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">{t(\"currentBalance\")}:</span>\n              <span className=\"text-sm font-medium\">{formatCurrency(walletInfo?.balance || 0)}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">{t(\"highlightFee\")}:</span>\n              <span className=\"text-sm font-medium\">{formatCurrency(highlightFee.fee)}</span>\n            </div>\n            <div className=\"border-t border-gray-200 my-1\"></div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">{t(\"balanceAfter\")}:</span>\n              <span className={`text-sm font-medium ${!hasEnoughBalance ? \"text-red-600\" : \"\"}`}>\n                {formatCurrency((walletInfo?.balance || 0) - highlightFee.fee)}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <DialogFooter className=\"flex flex-col sm:flex-row gap-2\">\n          {!hasEnoughBalance ? (\n            <Button onClick={handleTopUp} className=\"w-full sm:w-auto\">\n              {t(\"topUpWallet\")}\n            </Button>\n          ) : (\n            <Button onClick={handleConfirm} disabled={isProcessing} className=\"w-full sm:w-auto\">\n              {isProcessing ? tCommon(\"processing\") : t(\"confirmHighlight\")}\n            </Button>\n          )}\n          <Button variant=\"outline\" onClick={onClose} className=\"w-full sm:w-auto\">\n            {t(\"cancel\")}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\nexport default HighlightConfirmDialog;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;;AAYA,MAAM,yBAAyB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE;;IAC1D,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,aAAa,SAAS,MAAM;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,yCAAyC;IACzC,MAAM,kBAAkB;QACtB,IAAI,CAAC,WAAW,CAAC,SAAS,YAAY;YACpC,2DAA2D;YAC3D,OAAO;gBACL,KAAK,8GAAA,CAAA,kBAAe,CAAC,8GAAA,CAAA,aAAU,CAAC,OAAO,CAAC;gBACxC,UAAU,QAAQ,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD,EAAE,8GAAA,CAAA,aAAU,CAAC,OAAO;YAClE;QACF;QAEA,yDAAyD;QACzD,MAAM,iBAAiB,SAAS,WAAW;QAC3C,MAAM,MAAM,8GAAA,CAAA,kBAAe,CAAC,eAAe,IAAI,8GAAA,CAAA,kBAAe,CAAC,8GAAA,CAAA,aAAU,CAAC,OAAO,CAAC;QAElF,OAAO;YACL,KAAK;YACL,UAAU,QAAQ,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD,EAAE;QAChD;IACF;IAEA,MAAM,eAAe;IACrB,MAAM,mBAAmB,cAAc,WAAW,OAAO,IAAI,aAAa,GAAG;IAE7E,MAAM,gBAAgB;QACpB,IAAI,CAAC,kBAAkB;YACrB;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM;QACR,SAAU;YACR,gBAAgB;YAChB;QACF;IACF;IAEA,MAAM,cAAc;QAClB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;sCAAE,EAAE;;;;;;sCAChB,6LAAC,8HAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAC3B,cAAA,6LAAC;gCAAK,WAAU;0CACb,EAAE,2BAA2B;oCAC5B,QAAQ,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,GAAG;oCACvC,YAAY,aAAa,QAAQ;gCACnC;;;;;;;;;;;;;;;;;8BAKN,6LAAC;oBAAI,WAAU;;wBACZ,CAAC,kCACA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAsC,EAAE;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAA+B,EAAE;;;;;;;;;;;;;;;;;;sCAKpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;gDAAyB,EAAE;gDAAkB;;;;;;;sDAC7D,6LAAC;4CAAK,WAAU;sDAAuB,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,WAAW;;;;;;;;;;;;8CAE/E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;gDAAyB,EAAE;gDAAgB;;;;;;;sDAC3D,6LAAC;4CAAK,WAAU;sDAAuB,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,GAAG;;;;;;;;;;;;8CAExE,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;gDAAyB,EAAE;gDAAgB;;;;;;;sDAC3D,6LAAC;4CAAK,WAAW,CAAC,oBAAoB,EAAE,CAAC,mBAAmB,iBAAiB,IAAI;sDAC9E,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,YAAY,WAAW,CAAC,IAAI,aAAa,GAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAMrE,6LAAC,8HAAA,CAAA,eAAY;oBAAC,WAAU;;wBACrB,CAAC,iCACA,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAa,WAAU;sCACrC,EAAE;;;;;iDAGL,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAe,UAAU;4BAAc,WAAU;sCAC/D,eAAe,QAAQ,gBAAgB,EAAE;;;;;;sCAG9C,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,WAAU;sCACnD,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf;GAhHM;;QACM,yMAAA,CAAA,kBAAe;QACT,yMAAA,CAAA,kBAAe;QACX,2HAAA,CAAA,UAAO;QAEZ,qIAAA,CAAA,YAAS;;;KALpB;uCAkHS", "debugId": null}}, {"offset": {"line": 1636, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/badge.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 h-5 ml-0 mt-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground shadow-sm\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground shadow-sm\",\r\n        outline: \"text-foreground\",\r\n        ghost: \"\",\r\n        primary: \"bg-primary text-primary-foreground\",\r\n      },\r\n      rounded: {\r\n        default: \"rounded-md\",\r\n        full: \"rounded-full\",\r\n      },\r\n      height: {\r\n        default: \"h-5\",\r\n        sm: \"h-4\",\r\n        fit: \"h-fit\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      rounded: \"default\",\r\n      height: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  rounded,\r\n  height,\r\n  ...props\r\n}) {\r\n  return (<div className={cn(badgeVariants({ variant, rounded, height }), className)} {...props} />);\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,OAAO;YACP,SAAS;QACX;QACA,SAAS;YACP,SAAS;YACT,MAAM;QACR;QACA,QAAQ;YACN,SAAS;YACT,IAAI;YACJ,KAAK;QACP;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;QACT,QAAQ;IACV;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,OAAO,EACP,MAAM,EACN,GAAG,OACJ;IACC,qBAAQ,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;YAAS;QAAO,IAAI;QAAa,GAAG,KAAK;;;;;;AAC/F;KARS", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/BadgeStatus.jsx"], "sourcesContent": ["import { PropertyStatus } from \"@/lib/enum\";\r\nimport { cn, formatStatusText } from \"@/lib/utils\";\r\nimport { Badge } from \"../ui/badge\";\r\n\r\nconst STATUS_STYLES = {\r\n  [PropertyStatus.DRAFT]: \"bg-gray-100 text-gray-700\",\r\n  [PropertyStatus.SOLD]: \"bg-sky-100 text-sky-700\",\r\n  [PropertyStatus.EXPIRED]: \"bg-amber-100 text-amber-700\",\r\n  [PropertyStatus.APPROVED]: \"bg-emerald-100 text-emerald-700\",\r\n  [PropertyStatus.PENDING_APPROVAL]: \"bg-yellow-100 text-yellow-700\",\r\n  [PropertyStatus.REJECTED_BY_ADMIN]: \"bg-rose-100 text-rose-700\",\r\n  [PropertyStatus.REJECTED_DUE_TO_UNPAID]: \"bg-red-100 text-red-700\",\r\n  [PropertyStatus.WAITING_PAYMENT]: \"bg-orange-100 text-orange-700\",\r\n};\r\n\r\nexport default function BadgeStatus({ status, statusText, className }) {\r\n  const style = status ? STATUS_STYLES[status] : \"bg-yellow-500 text-white\";\r\n  return (\r\n    <Badge rounded=\"full\" variant=\"ghost\" className={cn(`inline-flex items-center justify-center text-sm font-medium px-2`, style, className)}>\r\n      {statusText || formatStatusText(status)}\r\n    </Badge>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB;IACpB,CAAC,8GAAA,CAAA,iBAAc,CAAC,KAAK,CAAC,EAAE;IACxB,CAAC,8GAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,EAAE;IACvB,CAAC,8GAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,EAAE;IAC1B,CAAC,8GAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,EAAE;IAC3B,CAAC,8GAAA,CAAA,iBAAc,CAAC,gBAAgB,CAAC,EAAE;IACnC,CAAC,8GAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC,EAAE;IACpC,CAAC,8GAAA,CAAA,iBAAc,CAAC,sBAAsB,CAAC,EAAE;IACzC,CAAC,8GAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,EAAE;AACpC;AAEe,SAAS,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE;IACnE,MAAM,QAAQ,SAAS,aAAa,CAAC,OAAO,GAAG;IAC/C,qBACE,6LAAC,6HAAA,CAAA,QAAK;QAAC,SAAQ;QAAO,SAAQ;QAAQ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,CAAC,gEAAgE,CAAC,EAAE,OAAO;kBAC5H,cAAc,CAAA,GAAA,+GAAA,CAAA,mBAAgB,AAAD,EAAE;;;;;;AAGtC;KAPwB", "debugId": null}}, {"offset": {"line": 1747, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties with payment processing\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with detailed success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n\r\n    // The new API returns detailed result information\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      return {\r\n        success: data.success,\r\n        message: data.message,\r\n        totalProcessed: data.totalProcessed,\r\n        successfullyHighlighted: data.successfullyHighlighted,\r\n        alreadyHighlighted: data.alreadyHighlighted,\r\n        failed: data.failed,\r\n        totalCost: data.totalCost,\r\n        propertyResults: data.propertyResults\r\n      };\r\n    } else {\r\n      // Handle HTTP error responses\r\n      const errorData = await response.json().catch(() => ({}));\r\n      return {\r\n        success: false,\r\n        message: errorData.message || `HTTP Error: ${response.status}`,\r\n        errorType: \"api_error\",\r\n        statusCode: response.status\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: error.message || \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA6bsB,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1763, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/PropertyReportCard.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, memo } from \"react\";\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Eye, BarChart, Heart } from \"lucide-react\";\r\nimport { getPropertyReportById } from \"@/app/actions/server/property\"; // Import the server action\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { CardContent } from \"@/components/ui/card\"; // Use CardContent for consistent styling\r\n\r\n// Cache for report data to avoid duplicate API calls\r\nconst reportCache = new Map();\r\n\r\nconst PropertyReportCard = memo(function PropertyReportCard({ propertyId, reportData: preloadedReportData }) {\r\n  const [reportData, setReportData] = useState(preloadedReportData || null);\r\n  const [isLoading, setIsLoading] = useState(!preloadedReportData);\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    // If we already have preloaded data, don't fetch\r\n    if (preloadedReportData) {\r\n      setReportData(preloadedReportData);\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    // Check cache first\r\n    if (reportCache.has(propertyId)) {\r\n      const cachedData = reportCache.get(propertyId);\r\n      setReportData(cachedData);\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    async function fetchReportData() {\r\n      if (!propertyId) return;\r\n\r\n      setIsLoading(true);\r\n      setError(null);\r\n      try {\r\n        const result = await getPropertyReportById(propertyId);\r\n        if (result && result?.success) {\r\n          const data = result?.data;\r\n          setReportData(data);\r\n          // Cache the result\r\n          reportCache.set(propertyId, data);\r\n        } else {\r\n          setError(result?.message || \"Lỗi tải báo cáo.\");\r\n          console.error(\"Error fetching report:\", result?.message);\r\n        }\r\n      } catch (err) {\r\n        setError(\"Lỗi kết nối.\");\r\n        console.error(\"Network or other error fetching report:\", err);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    }\r\n\r\n    fetchReportData();\r\n  }, [propertyId, preloadedReportData]); // Re-fetch if propertyId changes\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <CardContent className=\"flex items-center justify-center p-4 text-xs text-gray-500 w-full md:w-1/4 md:shrink-0 border-b md:border-b-0 md:border-r min-h-[150px]\">\r\n        <LoaderCircle className=\"animate-spin h-5 w-5 mr-2\" />\r\n        Đang tải báo cáo...\r\n      </CardContent>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <CardContent className=\"flex items-center justify-center p-4 text-xs text-red-600 w-full md:w-1/4 md:shrink-0 border-b md:border-b-0 md:border-r min-h-[150px]\">\r\n        {error}\r\n      </CardContent>\r\n    );\r\n  }\r\n\r\n  if (!reportData) {\r\n    return (\r\n      <CardContent className=\"flex items-center justify-center p-4 text-xs text-gray-500 w-full md:w-1/4 md:shrink-0 border-b md:border-b-0 md:border-r min-h-[150px]\">\r\n        Không có dữ liệu báo cáo.\r\n      </CardContent>\r\n    );\r\n  }\r\n\r\n  // Render the full report details\r\n  return (\r\n    <div className=\"grid gap-1 p-4 text-xs text-gray-600 w-full md:w-1/4 md:shrink-0 border-b md:border-b-0 md:border-r\">\r\n      {/* Stats */}\r\n      <div className=\"grid grid-cols-3 gap-x-3 text-gray-800\">\r\n        <div className=\"flex items-center gap-1\">\r\n          <Eye className=\"w-4 h-4\" />\r\n          {reportData.views}\r\n        </div>\r\n        <div className=\"flex items-center gap-1\">\r\n          <BarChart className=\"w-4 h-4\" />\r\n          {reportData.impressions}\r\n        </div>\r\n        <div className=\"flex items-center gap-1\">\r\n          <Heart className=\"w-4 h-4\" />\r\n          {reportData.cartAdds ?? \"N/A\"}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Divider */}\r\n      <hr className=\"my-2\" />\r\n\r\n      {/* Costs */}\r\n      <div className=\"grid grid-rows-5 gap-y-1 text-gray-800 font-medium\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <span>Đăng bài</span>\r\n          {formatCurrency(reportData.postCost ?? 0)}\r\n        </div>\r\n        <div className=\"flex items-center justify-between\">\r\n          <span>Highlight ({reportData.highlightsCount ?? \"0\"})</span>\r\n          {formatCurrency(reportData.highlightCost ?? 0)}\r\n        </div>\r\n        <div className=\"flex items-center justify-between\">\r\n          <span>Gia hạn ({reportData.renewalsCount ?? \"0\"})</span>\r\n          {formatCurrency(reportData.renewalCost ?? 0)}\r\n        </div>\r\n        <hr className=\"my-1\" />\r\n        {/* Total */}\r\n        <div className=\"flex justify-between items-center text-gray-900 font-semibold\">\r\n          <span>Tổng cộng</span>\r\n          {formatCurrency(reportData.totalCost ?? 0)}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\nexport default PropertyReportCard;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA,2RAAuE,2BAA2B;AAClG;AACA,0NAAoD,yCAAyC;;;AAN7F;;;;;;AAQA,qDAAqD;AACrD,MAAM,cAAc,IAAI;AAExB,MAAM,mCAAqB,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,UAAE,SAAS,mBAAmB,EAAE,UAAU,EAAE,YAAY,mBAAmB,EAAE;;IACzG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2DAAE;YACR,iDAAiD;YACjD,IAAI,qBAAqB;gBACvB,cAAc;gBACd,aAAa;gBACb;YACF;YAEA,oBAAoB;YACpB,IAAI,YAAY,GAAG,CAAC,aAAa;gBAC/B,MAAM,aAAa,YAAY,GAAG,CAAC;gBACnC,cAAc;gBACd,aAAa;gBACb;YACF;YAEA,eAAe;gBACb,IAAI,CAAC,YAAY;gBAEjB,aAAa;gBACb,SAAS;gBACT,IAAI;oBACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,wBAAqB,AAAD,EAAE;oBAC3C,IAAI,UAAU,QAAQ,SAAS;wBAC7B,MAAM,OAAO,QAAQ;wBACrB,cAAc;wBACd,mBAAmB;wBACnB,YAAY,GAAG,CAAC,YAAY;oBAC9B,OAAO;wBACL,SAAS,QAAQ,WAAW;wBAC5B,QAAQ,KAAK,CAAC,0BAA0B,QAAQ;oBAClD;gBACF,EAAE,OAAO,KAAK;oBACZ,SAAS;oBACT,QAAQ,KAAK,CAAC,2CAA2C;gBAC3D,SAAU;oBACR,aAAa;gBACf;YACF;YAEA;QACF;0DAAG;QAAC;QAAY;KAAoB,GAAG,iCAAiC;IAExE,IAAI,WAAW;QACb,qBACE,6LAAC,4HAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;gBAA8B;;;;;;;IAI5D;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC,4HAAA,CAAA,cAAW;YAAC,WAAU;sBACpB;;;;;;IAGP;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC,4HAAA,CAAA,cAAW;YAAC,WAAU;sBAA0I;;;;;;IAIrK;IAEA,iCAAiC;IACjC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BACd,WAAW,KAAK;;;;;;;kCAEnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gPAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,WAAW,WAAW;;;;;;;kCAEzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAChB,WAAW,QAAQ,IAAI;;;;;;;;;;;;;0BAK5B,6LAAC;gBAAG,WAAU;;;;;;0BAGd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;4BACL,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,QAAQ,IAAI;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAK;oCAAY,WAAW,eAAe,IAAI;oCAAI;;;;;;;4BACnD,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,aAAa,IAAI;;;;;;;kCAE9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAK;oCAAU,WAAW,aAAa,IAAI;oCAAI;;;;;;;4BAC/C,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,WAAW,IAAI;;;;;;;kCAE5C,6LAAC;wBAAG,WAAU;;;;;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;4BACL,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,SAAS,IAAI;;;;;;;;;;;;;;;;;;;AAKlD;;uCAEe", "debugId": null}}, {"offset": {"line": 2056, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/PropertyCard.jsx"], "sourcesContent": ["\"use client\";\r\nimport { memo, useState } from \"react\";\r\nimport {\r\n  Building2,\r\n  Clock,\r\n  Edit2,\r\n  LoaderCircle,\r\n  Menu,\r\n  Phone,\r\n  Stamp,\r\n  Trash2,\r\n  CalendarDays,\r\n  Zap,\r\n  Share2,\r\n  RefreshCcw,\r\n} from \"lucide-react\";\r\nimport { updatePropertyHighlight } from \"@/app/actions/server/property\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nimport ShareModal from \"./ShareModal\";\r\nimport HighlightConfirmDialog from \"./HighlightConfirmDialog\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { Card, CardContent, CardFooter } from \"@/components/ui/card\";\r\nimport BadgeStatus from \"@/components/layout/BadgeStatus\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { cn, formatCurrency, formatDate } from \"@/lib/utils\";\r\nimport PropertyReportCard from \"./PropertyReportCard\";\r\nimport { CAN_NOT_SEND_TO_REVIEW_STATUS, PropertyStatus } from \"@/lib/enum\";\r\n\r\n// Helper function to calculate remaining days\r\nconst calculateRemainingDays = (expiresAt) => {\r\n  if (!expiresAt) return null;\r\n  const today = new Date();\r\n  const expiryDate = new Date(expiresAt);\r\n  const diffTime = expiryDate - today;\r\n  if (diffTime <= 0) return 0; // Expired\r\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n  return diffDays;\r\n};\r\n\r\nconst PropertyCard = memo(\r\n  ({\r\n    property,\r\n    onEdit,\r\n    onDelete,\r\n    onSendToReview,\r\n    onShowContacts,\r\n    onShowHistory,\r\n    loadingId,\r\n    isSelected,\r\n    onCheckboxChange,\r\n    canNotSendToReviewStatus = CAN_NOT_SEND_TO_REVIEW_STATUS,\r\n  }) => {\r\n    const placeData = JSON.parse(property?.placeData || \"{}\");\r\n    const remainingDays = calculateRemainingDays(property.expiresAt);\r\n\r\n    // Handlers for property actions\r\n    const { toast } = useToast();\r\n    const [isHighlightLoading, setIsHighlightLoading] = useState(false);\r\n    const [isShareModalOpen, setIsShareModalOpen] = useState(false);\r\n    const [isHighlightDialogOpen, setIsHighlightDialogOpen] = useState(false);\r\n\r\n    // Get translations from hooks\r\n    const t  = useTranslations(\"PropertyCard\");\r\n    const tCommon = useTranslations(\"Common\");\r\n    const status = property?.status || PropertyStatus.DRAFT;\r\n    const statusText = property?.status ? tCommon(`propertyStatus_${property?.status}`) : tCommon(`propertyStatus_${PropertyStatus.DRAFT}`);\r\n\r\n    // Open highlight confirmation dialog\r\n    const handleHighlightClick = () => {\r\n      // Only allow highlighting if the property is not already highlighted\r\n      if (!property.isHighlighted) {\r\n        setIsHighlightDialogOpen(true);\r\n      }\r\n    };\r\n\r\n    // Handle highlight action after confirmation\r\n    const handleHighlightConfirm = async () => {\r\n      setIsHighlightLoading(true);\r\n      try {\r\n        // Always set isHighlighted to true - we don't support unhighlighting\r\n        const result = await updatePropertyHighlight(property.id, true);\r\n        if (result.success) {\r\n          toast({\r\n            description: t(\"highlightAddSuccess\"),\r\n            className: \"bg-teal-600 text-white\",\r\n          });\r\n          // Refresh the page to show updated property status\r\n          window.location.reload();\r\n        } else {\r\n          toast({\r\n            description: result.message || t(\"highlightUpdateError\"),\r\n            variant: \"destructive\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error updating highlight status:\", error);\r\n        toast({\r\n          description: t(\"highlightGenericError\"),\r\n          variant: \"destructive\",\r\n        });\r\n      } finally {\r\n        setIsHighlightLoading(false);\r\n      }\r\n    };\r\n\r\n    // Handle renew action - currently just a placeholder\r\n    const handleRenew = () => {\r\n      toast({\r\n        description: t(\"renewFeatureInDevelopment\"),\r\n        variant: \"default\",\r\n      });\r\n    };\r\n\r\n    // Handle share action\r\n    const handleShare = () => {\r\n      setIsShareModalOpen(true);\r\n    };\r\n\r\n    return (\r\n      <>\r\n        {/* Share Modal */}\r\n        {isShareModalOpen && (\r\n          <ShareModal\r\n            open={isShareModalOpen}\r\n            onClose={() => setIsShareModalOpen(false)}\r\n            property={property}\r\n          />\r\n        )}\r\n\r\n        {/* Highlight Confirmation Dialog */}\r\n        <HighlightConfirmDialog\r\n          open={isHighlightDialogOpen}\r\n          onClose={() => setIsHighlightDialogOpen(false)}\r\n          onConfirm={handleHighlightConfirm}\r\n        />\r\n\r\n        <Card className=\"overflow-hidden flex flex-col md:flex-row w-full\">\r\n          {/* Checkbox + Image Section */}\r\n          <div className=\"flex items-center p-2 md:p-4 border-b md:border-b-0 md:border-r\">\r\n            <Checkbox\r\n              id={`checkbox-${property.id}`}\r\n              checked={isSelected}\r\n              onCheckedChange={(checked) => onCheckboxChange(property.id, checked)}\r\n              aria-label={`Select property ${property.name}`}\r\n              className=\"mr-4\"\r\n            />\r\n            <div className=\"flex flex-col\">\r\n              <div className=\"h-24 w-24 md:h-32 md:w-32 relative overflow-hidden rounded-md\">\r\n                <img\r\n                  src={\r\n                    property.propertyMedia?.[0]?.mediaURL || \"/placeholder.svg?height=100&width=100\"\r\n                  }\r\n                  alt={property.name}\r\n                  className=\"h-full w-full object-cover rounded\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Info + Report Container - Stacks vertically on mobile, row on md+ */}\r\n          <div className=\"flex flex-col md:flex-row grow\">\r\n            {/* Left Section: Property Info */}\r\n            <CardContent className=\"grid gap-1 p-4 grow border-b md:border-r md:border-b-0\">\r\n              <div className=\"flex\">\r\n                <BadgeStatus\r\n                  rounded=\"full\"\r\n                  status={status}\r\n                  statusText={statusText}\r\n                />\r\n                {property.isHighlighted && <BadgeStatus statusText={tCommon(\"highlight_status\")} className=\"ml-3\" />}\r\n              </div>\r\n\r\n              <h4 className=\"line-clamp-2 font-semibold text-base md:text-lg text-charcoal\">\r\n                {property.name}\r\n              </h4>\r\n              {property.price && (\r\n                <p className=\"font-medium text-red-600\">{formatCurrency(property.price)}</p>\r\n              )}\r\n              {placeData?.result?.formatted_address && (\r\n                <div className=\"flex items-start gap-1 text-xs text-gray-500\">\r\n                  <Building2 className=\"mt-0.5 h-3 w-3 shrink-0\" />\r\n                  <span className=\"line-clamp-1\">{placeData.result.formatted_address}</span>\r\n                </div>\r\n              )}\r\n              <div className=\"flex items-center gap-1 text-xs text-gray-500\">\r\n                <CalendarDays className=\"h-3 w-3 shrink-0\" />\r\n                <span>{t(\"postDate\")}: {formatDate(property.createdAt)}</span>\r\n              </div>\r\n              <div className=\"flex items-center gap-1 text-xs text-gray-500\">\r\n                <CalendarDays className=\"h-3 w-3 shrink-0\" />\r\n                <span>{t(\"expiryDate\")}: {formatDate(property.expiresAt)}</span>  \r\n              </div>\r\n              {remainingDays !== null && (\r\n                <div\r\n                  className={`flex items-center gap-1 text-xs ${\r\n                    remainingDays <= 7 ? \"text-orange-600 font-medium\" : \"text-gray-500\"\r\n                  }`}\r\n                >\r\n                  <Clock className=\"h-3 w-3 shrink-0\" />\r\n                  <span>{remainingDays > 0 ? t(\"daysRemaining\", { days: remainingDays }) : t(\"expired\")}</span>\r\n                </div>\r\n              )}\r\n            </CardContent>\r\n\r\n            {/* Right Section: Report Info - Use the new async component */}\r\n            <PropertyReportCard propertyId={property.id} />\r\n          </div>\r\n\r\n          {/* Actions Section */}\r\n          <CardFooter className=\"flex flex-col md:flex-col items-stretch md:items-center justify-center p-2 md:p-4 gap-2 border-t md:border-t-0 md:border-l\">\r\n            {/* Button group 1 - Stretches on mobile */}\r\n            <div className=\"flex gap-2 justify-stretch w-full md:flex-col md:justify-center\">\r\n              {!property.isHighlighted ? (\r\n                <Button\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  className=\"flex-1 gap-1 text-xs px-2 py-1 h-auto\"\r\n                  onClick={handleHighlightClick}\r\n                  disabled={isHighlightLoading || loadingId === property.id}\r\n                >\r\n                  {isHighlightLoading ? (\r\n                    <LoaderCircle className=\"animate-spin h-3 w-3 mr-1\" />\r\n                  ) : (\r\n                    <Zap className=\"h-3 w-3 text-yellow-500\" />\r\n                  )}\r\n                  {tCommon(\"highlight_status\")}\r\n                </Button>\r\n              ) : (\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        className=\"flex-1 gap-1 text-xs px-2 py-1 h-auto\"\r\n                        disabled={true}\r\n                      >\r\n                        <Zap className=\"h-3 w-3 text-yellow-500\" />\r\n                        {tCommon(\"highlight_status\")}\r\n                      </Button>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>{t(\"alreadyHighlighted\")}</p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              )}\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"flex-1 gap-1 text-xs px-2 py-1 h-auto\"\r\n                onClick={handleRenew}\r\n              >\r\n                <RefreshCcw className=\"h-3 w-3 text-blue-500\" /> {t(\"renew\")}\r\n              </Button>\r\n            </div>\r\n            {/* Button group 2 - Stretches on mobile */}\r\n            <div className=\"flex gap-2 justify-stretch w-full md:flex-col md:justify-center\">\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"flex-1 gap-1 text-xs px-2 py-1 h-auto\"\r\n                onClick={() => onEdit(property.id)}\r\n              >\r\n                <Edit2 className=\"h-3 w-3\" /> {t(\"edit\")}\r\n              </Button>\r\n            </div>\r\n            {/* Button group 3 - Stretches on mobile */}\r\n            <div className=\"flex gap-2 justify-stretch w-full md:flex-col md:justify-center\">\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"flex-1 gap-1 text-xs px-2 py-1 h-auto\"\r\n                onClick={handleShare}\r\n              >\r\n                <Share2 className=\"h-3 w-3 text-green-500\" /> {t(\"share\")}\r\n              </Button>\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"outline\"\r\n                    className=\"flex-1 gap-1 text-xs px-2 py-1 h-auto\"\r\n                    aria-label=\"More actions\"\r\n                  >\r\n                    <Menu className=\"h-3 w-3\" /> {t(\"more\")}\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent>\r\n                  {/* Existing actions */}\r\n                  <DropdownMenuItem\r\n                    onClick={() => onSendToReview(property.id)}\r\n                    disabled={\r\n                      loadingId === property.id ||\r\n                      canNotSendToReviewStatus.includes(property.status)\r\n                    }\r\n                    className={cn(\r\n                      canNotSendToReviewStatus.includes(property.status) &&\r\n                        \"cursor-not-allowed opacity-50\"\r\n                    )}\r\n                  >\r\n                    {loadingId === property.id ? (\r\n                      <span className=\"flex items-center\">\r\n                        <LoaderCircle className=\"animate-spin h-4 w-4 mr-2\"></LoaderCircle>\r\n                        {tCommon(\"loading\")}\r\n                      </span>\r\n                    ) : (\r\n                      <>\r\n                        <Stamp className=\"h-4 w-4 mr-2\" />\r\n                        {canNotSendToReviewStatus.includes(property.status)\r\n                          ? t(\"cannotRequestVerification\")\r\n                          : t(\"requestVerification\")}\r\n                      </>\r\n                    )}\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => onShowContacts(property.id)}>\r\n                    <Phone className=\"h-4 w-4 mr-2\" />\r\n                    {t(\"requestContact\")}\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem onClick={() => onShowHistory(property.id)}>\r\n                    <Clock className=\"h-4 w-4 mr-2\" /> {t(\"activityHistory\")}\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem\r\n                    className=\"text-red-700\"\r\n                    onClick={() => onDelete(property.id)}\r\n                    disabled={loadingId === property.id}\r\n                  >\r\n                    {loadingId === property.id ? (\r\n                      <span className=\"flex items-center\">\r\n                        <LoaderCircle className=\"animate-spin h-4 w-4 mr-2\"></LoaderCircle>\r\n                        {t(\"deleting\")}\r\n                      </span>\r\n                    ) : (\r\n                      <>\r\n                        <Trash2 className=\"h-4 w-4 mr-2\" /> {t(\"delete\")}\r\n                      </>\r\n                    )}\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            </div>\r\n          </CardFooter>\r\n        </Card>\r\n      </>\r\n    );\r\n  }\r\n);\r\n\r\nPropertyCard.displayName = \"PropertyCard\";\r\n\r\nexport default PropertyCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AAEA;AACA;AACA;AAMA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;;;AAzCA;;;;;;;;;;;;;;;;;AA2CA,8CAA8C;AAC9C,MAAM,yBAAyB,CAAC;IAC9B,IAAI,CAAC,WAAW,OAAO;IACvB,MAAM,QAAQ,IAAI;IAClB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,WAAW,aAAa;IAC9B,IAAI,YAAY,GAAG,OAAO,GAAG,UAAU;IACvC,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO;AACT;AAEA,MAAM,6BAAe,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,UACtB,CAAC,EACC,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,cAAc,EACd,cAAc,EACd,aAAa,EACb,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,2BAA2B,8GAAA,CAAA,gCAA6B,EACzD;;IACC,MAAM,YAAY,KAAK,KAAK,CAAC,UAAU,aAAa;IACpD,MAAM,gBAAgB,uBAAuB,SAAS,SAAS;IAE/D,gCAAgC;IAChC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,8BAA8B;IAC9B,MAAM,IAAK,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC3B,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,SAAS,UAAU,UAAU,8GAAA,CAAA,iBAAc,CAAC,KAAK;IACvD,MAAM,aAAa,UAAU,SAAS,QAAQ,CAAC,eAAe,EAAE,UAAU,QAAQ,IAAI,QAAQ,CAAC,eAAe,EAAE,8GAAA,CAAA,iBAAc,CAAC,KAAK,EAAE;IAEtI,qCAAqC;IACrC,MAAM,uBAAuB;QAC3B,qEAAqE;QACrE,IAAI,CAAC,SAAS,aAAa,EAAE;YAC3B,yBAAyB;QAC3B;IACF;IAEA,6CAA6C;IAC7C,MAAM,yBAAyB;QAC7B,sBAAsB;QACtB,IAAI;YACF,qEAAqE;YACrE,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,EAAE,EAAE;YAC1D,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;oBACJ,aAAa,EAAE;oBACf,WAAW;gBACb;gBACA,mDAAmD;gBACnD,OAAO,QAAQ,CAAC,MAAM;YACxB,OAAO;gBACL,MAAM;oBACJ,aAAa,OAAO,OAAO,IAAI,EAAE;oBACjC,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;gBACJ,aAAa,EAAE;gBACf,SAAS;YACX;QACF,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,qDAAqD;IACrD,MAAM,cAAc;QAClB,MAAM;YACJ,aAAa,EAAE;YACf,SAAS;QACX;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAc;QAClB,oBAAoB;IACtB;IAEA,qBACE;;YAEG,kCACC,6LAAC,gJAAA,CAAA,UAAU;gBACT,MAAM;gBACN,SAAS,IAAM,oBAAoB;gBACnC,UAAU;;;;;;0BAKd,6LAAC,4JAAA,CAAA,UAAsB;gBACrB,MAAM;gBACN,SAAS,IAAM,yBAAyB;gBACxC,WAAW;;;;;;0BAGb,6LAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,WAAQ;gCACP,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;gCAC7B,SAAS;gCACT,iBAAiB,CAAC,UAAY,iBAAiB,SAAS,EAAE,EAAE;gCAC5D,cAAY,CAAC,gBAAgB,EAAE,SAAS,IAAI,EAAE;gCAC9C,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,KACE,SAAS,aAAa,EAAE,CAAC,EAAE,EAAE,YAAY;wCAE3C,KAAK,SAAS,IAAI;wCAClB,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAOlB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,4HAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,UAAW;gDACV,SAAQ;gDACR,QAAQ;gDACR,YAAY;;;;;;4CAEb,SAAS,aAAa,kBAAI,6LAAC,uIAAA,CAAA,UAAW;gDAAC,YAAY,QAAQ;gDAAqB,WAAU;;;;;;;;;;;;kDAG7F,6LAAC;wCAAG,WAAU;kDACX,SAAS,IAAI;;;;;;oCAEf,SAAS,KAAK,kBACb,6LAAC;wCAAE,WAAU;kDAA4B,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,KAAK;;;;;;oCAEvE,WAAW,QAAQ,mCAClB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAgB,UAAU,MAAM,CAAC,iBAAiB;;;;;;;;;;;;kDAGtE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;;oDAAM,EAAE;oDAAY;oDAAG,CAAA,GAAA,+GAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS;;;;;;;;;;;;;kDAEvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;;oDAAM,EAAE;oDAAc;oDAAG,CAAA,GAAA,+GAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS;;;;;;;;;;;;;oCAExD,kBAAkB,sBACjB,6LAAC;wCACC,WAAW,CAAC,gCAAgC,EAC1C,iBAAiB,IAAI,gCAAgC,iBACrD;;0DAEF,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAM,gBAAgB,IAAI,EAAE,iBAAiB;oDAAE,MAAM;gDAAc,KAAK,EAAE;;;;;;;;;;;;;;;;;;0CAMjF,6LAAC,wJAAA,CAAA,UAAkB;gCAAC,YAAY,SAAS,EAAE;;;;;;;;;;;;kCAI7C,6LAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CAEpB,6LAAC;gCAAI,WAAU;;oCACZ,CAAC,SAAS,aAAa,iBACtB,6LAAC,8HAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;wCACT,UAAU,sBAAsB,cAAc,SAAS,EAAE;;4CAExD,mCACC,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;qEAExB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAEhB,QAAQ;;;;;;6DAGX,6LAAC,+HAAA,CAAA,kBAAe;kDACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;8DACN,6LAAC,+HAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAU;wDACV,UAAU;;0EAEV,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DACd,QAAQ;;;;;;;;;;;;8DAGb,6LAAC,+HAAA,CAAA,iBAAc;8DACb,cAAA,6LAAC;kEAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;kDAKd,6LAAC,8HAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;;0DAET,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAA0B;4CAAE,EAAE;;;;;;;;;;;;;0CAIxD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,SAAS,EAAE;;sDAEjC,6LAAC,qMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;wCAAE,EAAE;;;;;;;;;;;;0CAIrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;;0DAET,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAA2B;4CAAE,EAAE;;;;;;;kDAEnD,6LAAC,wIAAA,CAAA,eAAY;;0DACX,6LAAC,wIAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,WAAU;oDACV,cAAW;;sEAEX,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;wDAAE,EAAE;;;;;;;;;;;;0DAGpC,6LAAC,wIAAA,CAAA,sBAAmB;;kEAElB,6LAAC,wIAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,eAAe,SAAS,EAAE;wDACzC,UACE,cAAc,SAAS,EAAE,IACzB,yBAAyB,QAAQ,CAAC,SAAS,MAAM;wDAEnD,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yBAAyB,QAAQ,CAAC,SAAS,MAAM,KAC/C;kEAGH,cAAc,SAAS,EAAE,iBACxB,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEACvB,QAAQ;;;;;;iFAGX;;8EACE,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,yBAAyB,QAAQ,CAAC,SAAS,MAAM,IAC9C,EAAE,+BACF,EAAE;;;;;;;;kEAIZ,6LAAC,wIAAA,CAAA,mBAAgB;wDAAC,SAAS,IAAM,eAAe,SAAS,EAAE;;0EACzD,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,EAAE;;;;;;;kEAEL,6LAAC,wIAAA,CAAA,mBAAgB;wDAAC,SAAS,IAAM,cAAc,SAAS,EAAE;;0EACxD,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;4DAAE,EAAE;;;;;;;kEAExC,6LAAC,wIAAA,CAAA,wBAAqB;;;;;kEACtB,6LAAC,wIAAA,CAAA,mBAAgB;wDACf,WAAU;wDACV,SAAS,IAAM,SAAS,SAAS,EAAE;wDACnC,UAAU,cAAc,SAAS,EAAE;kEAElC,cAAc,SAAS,EAAE,iBACxB,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEACvB,EAAE;;;;;;iFAGL;;8EACE,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;gEAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW7D;;QAlSoB,wHAAA,CAAA,WAAQ;QAMf,yMAAA,CAAA,kBAAe;QACV,yMAAA,CAAA,kBAAe;;;;QAPb,wHAAA,CAAA,WAAQ;QAMf,yMAAA,CAAA,kBAAe;QACV,yMAAA,CAAA,kBAAe;;;;AA8RnC,aAAa,WAAW,GAAG;uCAEZ", "debugId": null}}, {"offset": {"line": 2785, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/bds/PropertyList.jsx"], "sourcesContent": ["\"use client\";\r\nimport { memo, useState, useCallback } from \"react\";\r\nimport { <PERSON>aderCircle, Trash2, Search, Zap, RefreshCcw } from \"lucide-react\";\r\nimport dynamic from \"next/dynamic\"; \r\nimport { But<PERSON> } from \"@/components/ui/button\"; \r\nimport { useTranslations } from \"next-intl\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { LoadingSpinner } from \"@/components/ui/loading-spinner\";\r\nimport { usePropertyList } from \"@/hooks/usePropertyList\";\r\nimport PropertyCard from \"@/components/user-property/PropertyCard\";\r\n\r\n// Lazy load heavy components\r\nconst AlertPopup = dynamic(() => import(\"@/components/layout/AlertPopup\"), {\r\n  ssr: false,\r\n});\r\nconst NoData = dynamic(() => import(\"@/components/layout/NoData\"));\r\nconst ContactRequestModal = dynamic(() => import(\"@/components/user-property/ContactRequestModal\"), {\r\n  ssr: false,\r\n});\r\nconst HistoryModal = dynamic(() => import(\"@/components/user-property/HistoryModal\"), {\r\n  ssr: false,\r\n});\r\n\r\nfunction PropertyList({ initialData, initialFilterCounts }) {\r\n  // Use the custom hook for state management and performance optimizations\r\n  const {\r\n    data,\r\n    filteredData,\r\n    loadingId,\r\n    setLoadingId,\r\n    searchTerm,\r\n    setSearchTerm,\r\n    activeFilter,\r\n    setActiveFilter,\r\n    selectedIds,\r\n    setSelectedIds,\r\n    currentPage,\r\n    pageSize,\r\n    filterCounts,\r\n    isOverallLoading,\r\n    isAllSelected,\r\n    isIndeterminate,\r\n    isEmptyDatabase,\r\n    isEmptySearchResults,\r\n    hasResults,\r\n    fetchProperties,\r\n    handleEdit,\r\n    handleDelete,\r\n    handleSendToReviewRequest,\r\n    handleBulkHighlight,\r\n    handleBulkRenew,\r\n    handleBulkDelete,\r\n    FilterKeys,\r\n  } = usePropertyList(initialData, initialFilterCounts);\r\n\r\n  const [selectedPropertyId, setSelectedPropertyId] = useState(null);\r\n  const [isContactModalOpen, setIsContactModalOpen] = useState(false);\r\n  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);\r\n  const tCommon = useTranslations(\"Common\");\r\n  const t = useTranslations(\"PropertyList\");\r\n\r\n  // Remaining handlers not in the hook\r\n  const handleCheckboxChange = useCallback((propertyId, checked) => {\r\n    setSelectedIds((prev) => {\r\n      const newSet = new Set(prev);\r\n      if (checked) {\r\n        newSet.add(propertyId);\r\n      } else {\r\n        newSet.delete(propertyId);\r\n      }\r\n      return newSet;\r\n    });\r\n  }, [setSelectedIds]);\r\n\r\n  const handleSelectAllChange = useCallback(\r\n    (checked) => {\r\n      if (checked) {\r\n        const allIds = filteredData.map((p) => p.id);\r\n        setSelectedIds(new Set(allIds));\r\n      } else {\r\n        setSelectedIds(new Set());\r\n      }\r\n    },\r\n    [filteredData, setSelectedIds]\r\n  );\r\n\r\n  const handleShowContacts = useCallback((propertyId) => {\r\n    setSelectedPropertyId(propertyId);\r\n    setIsContactModalOpen(true);\r\n  }, []); // State setters are stable, so empty deps are fine\r\n\r\n  const handleShowHistory = useCallback((propertyId) => {\r\n    setSelectedPropertyId(propertyId);\r\n    setIsHistoryModalOpen(true);\r\n  }, []); // State setters are stable, so empty deps are fine\r\n\r\n  // --- Render ---\r\n  return (\r\n    <>\r\n      <AlertPopup />\r\n      \r\n      {/* Search Input - Always visible, disabled during loading */}\r\n      <div className=\"mb-4 flex flex-col sm:flex-row gap-4\">\r\n        <div className=\"relative grow\">\r\n          <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500\" />\r\n          <Input\r\n            type=\"search\"\r\n            placeholder={t(\"searchPlaceholder\")}\r\n            className=\"pl-8 w-full\"\r\n            value={searchTerm}\r\n            onChange={(e) => setSearchTerm(e.target.value)}\r\n            disabled={isOverallLoading}\r\n          />\r\n        </div>  \r\n      </div>\r\n\r\n      {/* Filter Buttons - Always visible, disabled during loading */}\r\n      <div className=\"mb-4 flex flex-wrap gap-2 border-b relative\">\r\n        <Button\r\n          variant={activeFilter === FilterKeys.ALL ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.ALL)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"all\")} ({filterCounts[FilterKeys.ALL]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.APPROVED ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.APPROVED)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"approved\")} ({filterCounts[FilterKeys.APPROVED]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.PENDING_APPROVAL ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.PENDING_APPROVAL)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"pendingApproval\")} ({filterCounts[FilterKeys.PENDING_APPROVAL]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.REJECTED_BY_ADMIN ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.REJECTED_BY_ADMIN)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"rejectedByAdmin\")} ({filterCounts[FilterKeys.REJECTED_BY_ADMIN]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.REJECTED_DUE_TO_UNPAID ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.REJECTED_DUE_TO_UNPAID)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"rejectedDueToUnpaid\")} ({filterCounts[FilterKeys.REJECTED_DUE_TO_UNPAID]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.WAITING_PAYMENT ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.WAITING_PAYMENT)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"waitingPayment\")} ({filterCounts[FilterKeys.WAITING_PAYMENT]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.EXPIRED ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.EXPIRED)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"expired\")} ({filterCounts[FilterKeys.EXPIRED]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.DRAFT ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.DRAFT)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"draft\")} ({filterCounts[FilterKeys.DRAFT]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.SOLD ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.SOLD)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"sold\")} ({filterCounts[FilterKeys.SOLD]})\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Bulk Actions - Always visible, disabled during loading */}\r\n      <div className=\"mb-4 flex items-center gap-4 min-h-[40px]\">\r\n        <Checkbox\r\n          id=\"select-all\"\r\n          checked={isAllSelected}\r\n          onCheckedChange={handleSelectAllChange}\r\n          aria-label=\"Select all properties on this page\"\r\n          data-state={isIndeterminate ? \"indeterminate\" : isAllSelected ? \"checked\" : \"unchecked\"}\r\n          disabled={isOverallLoading}\r\n        />\r\n        <label htmlFor=\"select-all\" className={`text-sm font-medium ${isOverallLoading ? 'text-gray-400' : ''}`}>\r\n          {t(\"selectAll\")}\r\n        </label>\r\n        {selectedIds.size > 0 && (\r\n          <div className=\"flex gap-2 ml-auto\">\r\n            <Button\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n              onClick={handleBulkHighlight}\r\n              disabled={isOverallLoading || loadingId === \"bulk-delete\" || loadingId === \"bulk-highlight\"}\r\n            >\r\n              <Zap className=\"h-4 w-4 mr-1\" /> {tCommon(\"highlight_status\")} ({selectedIds.size})\r\n            </Button>\r\n            <Button\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n              onClick={handleBulkRenew}\r\n              disabled={isOverallLoading || loadingId === \"bulk-delete\" || loadingId === \"bulk-highlight\"}\r\n            >\r\n              <RefreshCcw className=\"h-4 w-4 mr-1\" /> {t(\"renew\")} ({selectedIds.size})\r\n            </Button>\r\n            <Button\r\n              size=\"sm\"\r\n              variant=\"destructive\"\r\n              onClick={handleBulkDelete}\r\n              disabled={isOverallLoading || loadingId === \"bulk-delete\" || loadingId === \"bulk-highlight\"}\r\n            >\r\n              {loadingId === \"bulk-delete\" || loadingId === \"bulk-highlight\" ? (\r\n                <LoaderCircle className=\"animate-spin h-4 w-4 mr-1\" />\r\n              ) : (\r\n                <Trash2 className=\"h-4 w-4 mr-1\" />\r\n              )}\r\n              {t(\"delete\")} ({selectedIds.size})\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Content Area - Conditional rendering based on loading and data states */}\r\n      {isOverallLoading ? (\r\n        <LoadingSpinner />\r\n      ) : isEmptyDatabase ? (\r\n        <NoData\r\n          hasCreateButton={true}\r\n          createMessage={t(\"noProperty\")}\r\n          createPageRoute=\"/user/bds/new\"\r\n          createButtonTitle={t(\"createProperty\")}\r\n        />\r\n      ) : isEmptySearchResults ? (\r\n        <NoData message={t(\"noResults\")} />\r\n      ) : hasResults ? (\r\n        <>\r\n          <div className=\"grid gap-4\">\r\n            {filteredData.map((property) => {\r\n              const isSelected = selectedIds.has(property.id);\r\n              return (\r\n                <PropertyCard\r\n                  key={property.id}\r\n                  property={property}\r\n                  onEdit={handleEdit}\r\n                  onDelete={handleDelete}\r\n                  onSendToReview={handleSendToReviewRequest}\r\n                  onShowContacts={handleShowContacts}\r\n                  onShowHistory={handleShowHistory}\r\n                  loadingId={loadingId}\r\n                  isSelected={isSelected}\r\n                  onCheckboxChange={handleCheckboxChange}\r\n                />\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* Pagination Controls */}\r\n          {data.length > pageSize && (\r\n            <div className=\"mt-6 flex justify-center items-center gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, Math.max(1, currentPage - 1), pageSize)}\r\n                disabled={currentPage <= 1 || isOverallLoading}\r\n              >\r\n                {t(\"previous\")}\r\n              </Button>\r\n              <span className=\"text-sm\">\r\n                {t(\"page\")} {currentPage}\r\n              </span>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, currentPage + 1, pageSize)}\r\n                disabled={filteredData.length < pageSize || isOverallLoading}\r\n              >\r\n                {t(\"next\")}\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </>\r\n      ) : null}\r\n\r\n      {isContactModalOpen && (\r\n        <ContactRequestModal propertyId={selectedPropertyId} open={isContactModalOpen} onClose={() => setIsContactModalOpen(false)} />\r\n      )}\r\n\r\n      {isHistoryModalOpen && <HistoryModal propertyId={selectedPropertyId} open={isHistoryModalOpen} onClose={() => setIsHistoryModalOpen(false)} />}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default memo(PropertyList);\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAVA;;;;;;;;;;;AAYA,6BAA6B;AAC7B,MAAM,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACzB,KAAK;;KADD;AAGN,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;MAAjB;AACN,MAAM,sBAAsB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAClC,KAAK;;MADD;AAGN,MAAM,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAC3B,KAAK;;MADD;AAIN,SAAS,aAAa,EAAE,WAAW,EAAE,mBAAmB,EAAE;;IACxD,yEAAyE;IACzE,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,YAAY,EACZ,eAAe,EACf,WAAW,EACX,cAAc,EACd,WAAW,EACX,QAAQ,EACR,YAAY,EACZ,gBAAgB,EAChB,aAAa,EACb,eAAe,EACf,eAAe,EACf,oBAAoB,EACpB,UAAU,EACV,eAAe,EACf,UAAU,EACV,YAAY,EACZ,yBAAyB,EACzB,mBAAmB,EACnB,eAAe,EACf,gBAAgB,EAChB,UAAU,EACX,GAAG,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE,aAAa;IAEjC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qCAAqC;IACrC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC,YAAY;YACpD;kEAAe,CAAC;oBACd,MAAM,SAAS,IAAI,IAAI;oBACvB,IAAI,SAAS;wBACX,OAAO,GAAG,CAAC;oBACb,OAAO;wBACL,OAAO,MAAM,CAAC;oBAChB;oBACA,OAAO;gBACT;;QACF;yDAAG;QAAC;KAAe;IAEnB,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DACtC,CAAC;YACC,IAAI,SAAS;gBACX,MAAM,SAAS,aAAa,GAAG;8EAAC,CAAC,IAAM,EAAE,EAAE;;gBAC3C,eAAe,IAAI,IAAI;YACzB,OAAO;gBACL,eAAe,IAAI;YACrB;QACF;0DACA;QAAC;QAAc;KAAe;IAGhC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACtC,sBAAsB;YACtB,sBAAsB;QACxB;uDAAG,EAAE,GAAG,mDAAmD;IAE3D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACrC,sBAAsB;YACtB,sBAAsB;QACxB;sDAAG,EAAE,GAAG,mDAAmD;IAE3D,iBAAiB;IACjB,qBACE;;0BACE,6LAAC;;;;;0BAGD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC,6HAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,aAAa,EAAE;4BACf,WAAU;4BACV,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,UAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,GAAG,GAAG,UAAU;wBACrD,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,GAAG;wBAC7C,UAAU;;4BAET,EAAE;4BAAO;4BAAG,YAAY,CAAC,WAAW,GAAG,CAAC;4BAAC;;;;;;;kCAE5C,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,QAAQ,GAAG,UAAU;wBAC1D,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,QAAQ;wBAClD,UAAU;;4BAET,EAAE;4BAAY;4BAAG,YAAY,CAAC,WAAW,QAAQ,CAAC;4BAAC;;;;;;;kCAEtD,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,gBAAgB,GAAG,UAAU;wBAClE,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,gBAAgB;wBAC1D,UAAU;;4BAET,EAAE;4BAAmB;4BAAG,YAAY,CAAC,WAAW,gBAAgB,CAAC;4BAAC;;;;;;;kCAErE,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,iBAAiB,GAAG,UAAU;wBACnE,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,iBAAiB;wBAC3D,UAAU;;4BAET,EAAE;4BAAmB;4BAAG,YAAY,CAAC,WAAW,iBAAiB,CAAC;4BAAC;;;;;;;kCAEtE,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,sBAAsB,GAAG,UAAU;wBACxE,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,sBAAsB;wBAChE,UAAU;;4BAET,EAAE;4BAAuB;4BAAG,YAAY,CAAC,WAAW,sBAAsB,CAAC;4BAAC;;;;;;;kCAE/E,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,eAAe,GAAG,UAAU;wBACjE,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,eAAe;wBACzD,UAAU;;4BAET,EAAE;4BAAkB;4BAAG,YAAY,CAAC,WAAW,eAAe,CAAC;4BAAC;;;;;;;kCAEnE,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,OAAO,GAAG,UAAU;wBACzD,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,OAAO;wBACjD,UAAU;;4BAET,EAAE;4BAAW;4BAAG,YAAY,CAAC,WAAW,OAAO,CAAC;4BAAC;;;;;;;kCAEpD,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,KAAK,GAAG,UAAU;wBACvD,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,KAAK;wBAC/C,UAAU;;4BAET,EAAE;4BAAS;4BAAG,YAAY,CAAC,WAAW,KAAK,CAAC;4BAAC;;;;;;;kCAEhD,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,IAAI,GAAG,UAAU;wBACtD,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,IAAI;wBAC9C,UAAU;;4BAET,EAAE;4BAAQ;4BAAG,YAAY,CAAC,WAAW,IAAI,CAAC;4BAAC;;;;;;;;;;;;;0BAKhD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,WAAQ;wBACP,IAAG;wBACH,SAAS;wBACT,iBAAiB;wBACjB,cAAW;wBACX,cAAY,kBAAkB,kBAAkB,gBAAgB,YAAY;wBAC5E,UAAU;;;;;;kCAEZ,6LAAC;wBAAM,SAAQ;wBAAa,WAAW,CAAC,oBAAoB,EAAE,mBAAmB,kBAAkB,IAAI;kCACpG,EAAE;;;;;;oBAEJ,YAAY,IAAI,GAAG,mBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,oBAAoB,cAAc,iBAAiB,cAAc;;kDAE3E,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;oCAAE,QAAQ;oCAAoB;oCAAG,YAAY,IAAI;oCAAC;;;;;;;0CAEpF,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,oBAAoB,cAAc,iBAAiB,cAAc;;kDAE3E,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;oCAAE,EAAE;oCAAS;oCAAG,YAAY,IAAI;oCAAC;;;;;;;0CAE1E,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,oBAAoB,cAAc,iBAAiB,cAAc;;oCAE1E,cAAc,iBAAiB,cAAc,iCAC5C,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAExB,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAEnB,EAAE;oCAAU;oCAAG,YAAY,IAAI;oCAAC;;;;;;;;;;;;;;;;;;;YAOxC,iCACC,6LAAC,0IAAA,CAAA,iBAAc;;;;uBACb,gCACF,6LAAC;gBACC,iBAAiB;gBACjB,eAAe,EAAE;gBACjB,iBAAgB;gBAChB,mBAAmB,EAAE;;;;;uBAErB,qCACF,6LAAC;gBAAO,SAAS,EAAE;;;;;uBACjB,2BACF;;kCACE,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC;4BACjB,MAAM,aAAa,YAAY,GAAG,CAAC,SAAS,EAAE;4BAC9C,qBACE,6LAAC,kJAAA,CAAA,UAAY;gCAEX,UAAU;gCACV,QAAQ;gCACR,UAAU;gCACV,gBAAgB;gCAChB,gBAAgB;gCAChB,eAAe;gCACf,WAAW;gCACX,YAAY;gCACZ,kBAAkB;+BATb,SAAS,EAAE;;;;;wBAYtB;;;;;;oBAID,KAAK,MAAM,GAAG,0BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,KAAK,GAAG,CAAC,GAAG,cAAc,IAAI;gCACpH,UAAU,eAAe,KAAK;0CAE7B,EAAE;;;;;;0CAEL,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAQ;oCAAE;;;;;;;0CAEf,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,cAAc,GAAG;gCACvG,UAAU,aAAa,MAAM,GAAG,YAAY;0CAE3C,EAAE;;;;;;;;;;;;;+BAKT;YAEH,oCACC,6LAAC;gBAAoB,YAAY;gBAAoB,MAAM;gBAAoB,SAAS,IAAM,sBAAsB;;;;;;YAGrH,oCAAsB,6LAAC;gBAAa,YAAY;gBAAoB,MAAM;gBAAoB,SAAS,IAAM,sBAAsB;;;;;;;;AAG1I;GAtSS;;QA8BH,2HAAA,CAAA,kBAAe;QAKH,yMAAA,CAAA,kBAAe;QACrB,yMAAA,CAAA,kBAAe;;;MApClB;2DAwSM,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}]}