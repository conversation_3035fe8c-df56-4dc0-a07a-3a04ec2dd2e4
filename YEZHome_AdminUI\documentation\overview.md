# YEZHome Admin UI - Project Overview

## Project Description

YEZHome Admin UI is a React-based web application built with modern frontend technologies. It serves as an administrative interface, likely for managing YEZ Home services or products. The project is set up with a robust tech stack focused on developer experience and performance.

## Technology Stack

### Core Technologies
- **React 19**: Latest version of the React library for building user interfaces
- **TypeScript**: For type-safe JavaScript development
- **Vite**: Modern frontend build tool and development server
- **TailwindCSS 4**: Utility-first CSS framework for styling

### Key Libraries and Frameworks
- **TanStack Router**: For application routing with type safety
- **TanStack Query**: For data fetching, caching, and state management
- **TanStack Table**: For building powerful data tables with sorting, filtering, and pagination
- **Zustand**: Always use Zustand for managing client state (UI state) and non-server data (e.g., modal open/close state, unsubmitted form input values).
- **Axios**: Always use Axios for HTTP requests.
- **Shadcn UI**: Component library with a "New York" style theme  

## Project Structure

```
YEZHome_AdminUI/
├── src/                   # Source code
|   ├── documentation/     # Documentation for the project (implementation plan, task, etc)
│   ├── components/        # Reusable UI components
│   ├── data/              # Mock data and data utilities
│   ├── integrations/      # Integration with external libraries
│   │   └── tanstack-query/# TanStack Query configuration
│   ├── lib/               # Utility functions and helpers
│   ├── routes/            # Application routes/pages
│   ├── App.tsx            # Main application component
│   └── main.tsx           # Application entry point
├── public/                # Static assets
└── components.json        # Shadcn UI configuration
```

## Features and Functionality

The application currently includes:

1. **Navigation System**: A header component with navigation links to different sections
2. **TanStack Query Demo**: Demonstrates data fetching and state management
3. **TanStack Table Demo**: Shows a powerful data table with:
   - Fuzzy searching and filtering
   - Sorting capabilities
   - Pagination controls
   - Large dataset handling (5,000+ records)


## Client State Management - Zustand

Use Zustand: Always use Zustand for managing client state (UI state) and non-server data (e.g., modal open/close state, unsubmitted form input values).
Domain-based Slicing: Define each client state slice in a separate store file within the store/ directory.
Encapsulation: Each Zustand store should encapsulate its own state and state-modifying functions.
Use persist Middleware: For client state that needs to persist across sessions (e.g., theme preferences), use Zustand’s persist middleware.
Loading & Error States (UI): Use commonStore for global UI loading states or success/error notifications unrelated to server state (handled by TanStack Query).


## Server State Management - TanStack Query

Mandatory use of TanStack Query: Use TanStack Query for all fetching, caching, synchronizing, and updating of API data.
QueryClientProvider: Ensure QueryClientProvider is wrapped at the top level of the application (in App.tsx or providers/QueryProvider.tsx).
Custom Hooks for Queries/Mutations (/hooks):
Create custom hooks (e.g., useGetUsers, useCreateUser) in /hooks/.
These hooks encapsulate useQuery (for GET) and useMutation (for POST/PUT/DELETE) logic.
Pass API call functions from services/ to queryFn or mutationFn in TanStack Query.


Query Keys: Define clear and consistent query keys that reflect data structure and parameters (e.g., ['users', { page, limit }], ['property', propertyId]).
Invalidation: Use queryClient.invalidateQueries() in onSuccess of useMutation to automatically refetch related data after server changes.
Optimistic Updates: Consider implementing optimistic updates with useMutation to enhance user experience.
Handle Loading/Error/Data: Always use values returned from useQuery and useMutation (isLoading, isError, data, error, isFetching, isPending) to display appropriate UI states.


## Routing - TanStack Router

Mandatory use of TanStack Router: Implement routing with TanStack Router for type-safety and route-level data fetching integration.
Router Tree (lib/router.ts):
Define the main router tree in lib/router.ts.
Aggregate child routes from features/<domain>/routes.tsx.


Route Loaders: Use TanStack Router’s loader to fetch data for routes, ensuring data is ready before components render and leveraging TanStack Query caching.
Loaders should call functions from services/ or TanStack Query custom hooks.


Type Safety: Leverage TanStack Router’s type inference for params, search, and loaderData to ensure type safety.
Nested Routes: Use TanStack Router’s nested routes for complex layouts and data sharing between child routes.
Authentication (Protected Routes): Implement route protection logic within TanStack Router’s route definitions or loader to check authentication before access.


## Development Environment

The project uses:
- **ESLint**: For code linting
- **Prettier**: For code formatting
- **Vitest**: For testing
- **Development Server**: Running on port 3003

## Build and Deployment

The application can be:
- Developed locally with `npm run dev` or `npm start`
- Built for production with `npm run build`
- Previewed with `npm run serve`

## Future Development Considerations

The project appears to be set up with a scalable architecture that would support:
- Adding more admin features and pages
- Integrating with backend APIs
- Implementing authentication and authorization
- Expanding the component library with Shadcn UI components 