import { Link } from '@tanstack/react-router';
import { isAuthenticated } from '@/lib/auth';
import authService from '@/services/auth-service';
import { useNavigate } from '@tanstack/react-router';

export default function Header() {
  const navigate = useNavigate();
  const authenticated = isAuthenticated();

  const handleLogout = () => {
    authService.logout();
    navigate({ to: '/login' });
  };

  return (
    <header className="p-2 flex gap-2 bg-white text-black justify-between">
      <nav className="flex flex-row">
        <div className="px-2 font-bold">
          <Link to="/">Trang chủ</Link>
        </div>

        {authenticated && (
          <>
            <div className="px-2 font-bold">
              <Link to="/demo/tanstack-query">TanStack Query</Link>
            </div>

            <div className="px-2 font-bold">
              <Link to="/demo/table">TanStack Table</Link>
            </div>
          </>
        )}
      </nav>
      <div className="px-2 font-bold">
        {authenticated ? (
          <button onClick={handleLogout} className="text-blue-500 hover:text-blue-700">
            Đăng xuất
          </button>
        ) : (
          <Link to="/login">Đăng nhập</Link>
        )}
      </div>
    </header>
  );
} 