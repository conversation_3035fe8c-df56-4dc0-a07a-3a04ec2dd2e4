using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.Entities;
using RealEstate.Infrastructure;
using Microsoft.Extensions.Caching.Memory;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AddressController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AddressController> _logger;
        private readonly IMemoryCache _memoryCache;

        public AddressController(ApplicationDbContext context, ILogger<AddressController> logger, IMemoryCache memoryCache)
        {
            _context = context;
            _logger = logger;
            _memoryCache = memoryCache;
        }

        [HttpGet("cities")]
        public async Task<ActionResult<IEnumerable<City>>> GetCitiesAsync()
        {
            try
            {
                _logger.LogInformation("Retrieving all cities from IP {IpAddress}", HttpContext.Connection.RemoteIpAddress?.ToString());

                const string cacheKey = "CitiesList";
                if (_memoryCache.TryGetValue(cacheKey, out IEnumerable<City> cities))
                {
                    _logger.LogInformation("Successfully retrieved {Count} cities from cache", ((List<City>)cities).Count);
                    return Ok(cities);
                }

                cities = await _context.City.OrderBy(x => x.Id).ToListAsync();
                if (cities == null || !cities.Any())
                {
                    _logger.LogWarning("No cities found");
                    return NotFound(new { Message = "No cities found" });
                }

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                    .SetAbsoluteExpiration(TimeSpan.FromHours(1));

                _memoryCache.Set(cacheKey, cities, cacheEntryOptions);

                _logger.LogInformation("Successfully retrieved {Count} cities from database and cached them", ((List<City>)cities).Count);
                return Ok(cities);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cities");
                return StatusCode(500, new { Message = "An error occurred while retrieving cities. Please try again later." });
            }
        }

        [HttpGet("cities/{cityId}/districts")]
        public async Task<ActionResult<IEnumerable<District>>> GetDistrictByCityAsync(int cityId)
        {
            try
            {
                _logger.LogInformation("Retrieving districts for city {CityId} from IP {IpAddress}",
                    cityId, HttpContext.Connection.RemoteIpAddress?.ToString());

                string cacheKey = $"DistrictsForCity_{cityId}";
                if (_memoryCache.TryGetValue(cacheKey, out IEnumerable<District> districts))
                {
                    _logger.LogInformation("Successfully retrieved {Count} districts for city {CityId} from cache", ((List<District>)districts).Count, cityId);
                    return Ok(districts);
                }

                districts = await _context.District.Where(d => d.CityId == cityId).OrderBy(x => x.NameWithType).ToListAsync();
                if (districts == null || !districts.Any())
                {
                    _logger.LogWarning("No districts found for city {CityId}", cityId);
                    return NotFound(new { Message = "No districts found for the specified city" });
                }

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                    .SetAbsoluteExpiration(TimeSpan.FromHours(1));

                _memoryCache.Set(cacheKey, districts, cacheEntryOptions);

                _logger.LogInformation("Successfully retrieved {Count} districts for city {CityId} from database and cached them", ((List<District>)districts).Count, cityId);
                return Ok(districts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving districts for city {CityId}", cityId);
                return StatusCode(500, new { Message = "An error occurred while retrieving districts. Please try again later." });
            }
        }

        [HttpGet("districts/{districtId}/wards")]
        public async Task<ActionResult<IEnumerable<Ward>>> GetWardByDistrictAsync(int districtId)
        {
            try
            {
                _logger.LogInformation("Retrieving wards for district {DistrictId} from IP {IpAddress}",
                    districtId, HttpContext.Connection.RemoteIpAddress?.ToString());

                string cacheKey = $"WardsForDistrict_{districtId}";
                if (_memoryCache.TryGetValue(cacheKey, out IEnumerable<Ward> wards))
                {
                    _logger.LogInformation("Successfully retrieved {Count} wards for district {DistrictId} from cache", ((List<Ward>)wards).Count, districtId);
                    return Ok(wards);
                }

                wards = await _context.Ward.Where(d => d.DistrictId == districtId).OrderBy(x => x.NameWithType).ToListAsync();
                if (wards == null || !wards.Any())
                {
                    _logger.LogWarning("No wards found for district {DistrictId}", districtId);
                    return NotFound(new { Message = "No wards found for the specified district" });
                }

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                    .SetAbsoluteExpiration(TimeSpan.FromHours(1));

                _memoryCache.Set(cacheKey, wards, cacheEntryOptions);

                _logger.LogInformation("Successfully retrieved {Count} wards for district {DistrictId} from database and cached them", ((List<Ward>)wards).Count, districtId);
                return Ok(wards);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving wards for district {DistrictId}", districtId);
                return StatusCode(500, new { Message = "An error occurred while retrieving wards. Please try again later." });
            }
        }

        [HttpGet("districts/{districtId}/streets")]
        public async Task<ActionResult<IEnumerable<Street>>> GetStreetByDistrictAsync(int districtId)
        {
            try
            {
                _logger.LogInformation("Retrieving streets for district {DistrictId} from IP {IpAddress}",
                    districtId, HttpContext.Connection.RemoteIpAddress?.ToString());

                string cacheKey = $"StreetsForDistrict_{districtId}";
                if (_memoryCache.TryGetValue(cacheKey, out IEnumerable<Street> streets))
                {
                    _logger.LogInformation("Successfully retrieved {Count} streets for district {DistrictId} from cache", ((List<Street>)streets).Count, districtId);
                    return Ok(streets);
                }

                streets = await _context.Street.Where(s => s.DistrictId == districtId).ToListAsync();
                if (streets == null || !streets.Any())
                {
                    _logger.LogWarning("No streets found for district {DistrictId}", districtId);
                    return NotFound(new { Message = "No streets found for the specified district" });
                }

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                    .SetAbsoluteExpiration(TimeSpan.FromHours(1));

                _memoryCache.Set(cacheKey, streets, cacheEntryOptions);

                _logger.LogInformation("Successfully retrieved {Count} streets for district {DistrictId} from database and cached them", ((List<Street>)streets).Count, districtId);
                return Ok(streets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving streets for district {DistrictId}", districtId);
                return StatusCode(500, new { Message = "An error occurred while retrieving streets. Please try again later." });
            }
        }

        [HttpGet("wards/{wardId}/streets/{streetId}/projects")]
        public async Task<ActionResult<IEnumerable<Project>>> GetProjectsByWardStreetAsync(int wardId, int streetId)
        {
            try
            {
                _logger.LogInformation("Retrieving projects for ward {WardId} and street {StreetId} from IP {IpAddress}",
                    wardId, streetId, HttpContext.Connection.RemoteIpAddress?.ToString());

                string cacheKey = $"ProjectsForWard_{wardId}_Street_{streetId}";
                if (_memoryCache.TryGetValue(cacheKey, out IEnumerable<Project> projects))
                {
                    _logger.LogInformation("Successfully retrieved {Count} projects for ward {WardId} and street {StreetId} from cache", ((List<Project>)projects).Count, wardId, streetId);
                    return Ok(projects);
                }

                projects = await _context.Project.Where(p => p.WardId == wardId && p.StreetId == streetId).ToListAsync();
                if (projects == null || !projects.Any())
                {
                    _logger.LogWarning("No projects found for ward {WardId} and street {StreetId}", wardId, streetId);
                    return NotFound(new { Message = "No projects found for the specified ward and street." });
                }

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                    .SetAbsoluteExpiration(TimeSpan.FromHours(1));

                _memoryCache.Set(cacheKey, projects, cacheEntryOptions);

                _logger.LogInformation("Successfully retrieved {Count} projects for ward {WardId} and street {StreetId} from database and cached them", ((List<Project>)projects).Count, wardId, streetId);
                return Ok(projects);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving projects for ward {WardId} and street {StreetId}", wardId, streetId);
                return StatusCode(500, new { Message = "An error occurred while retrieving projects. Please try again later." });
            }
        }
    }
}
