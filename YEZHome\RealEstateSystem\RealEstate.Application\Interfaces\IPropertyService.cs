﻿using RealEstate.Application.DTO;

namespace RealEstate.Application.Interfaces
{
    public interface IPropertyService
    {
        Task<PropertyDto> GetPropertyByIdAsync(Guid id, bool asNoTracking = true);
        Task<IEnumerable<PropertyDto>> GetAllPropertiesAsync();
        Task<IEnumerable<PropertyDto>> GetPropertyByUserAsync(Guid userId);
        Task<PagedResultDto<PropertyDto>> GetPropertyByUserWithStatusAsync(Guid userId, List<string> statuses, int page = 1, int pageSize = 10);
        Task<PropertyCountStatsDto> GetPropertyCountStatsByUserAsync(Guid userId);
        Task<PropertyDto> CreatePropertyAsync(CreatePropertyDto propertyDto, Guid userId);
        Task<PropertyDto> UpdatePropertyAsync(Guid id, CreatePropertyDto propertyDto, Guid userId);
        Task<bool> DeletePropertyAsync(Guid id, Guid userId);
        Task<bool> DeletePropertiesAsync(List<Guid> ids, Guid userId);
        Task<bool> UpdateStatusAsync(Guid id, Guid userId, UpdateStatusDto updateStatusDto);
        Task<bool> UpdateStatusBulkAsync(List<Guid> ids, Guid userId, UpdateStatusDto updateStatusDto);
        Task<bool> UpdateHighlightAsync(Guid id, Guid userId, bool isHighlighted);
        Task<PropertyHighlightResultDto> UpdateHighlightWithPaymentAsync(Guid propertyId, Guid userId, bool isHighlighted);
        Task<BulkHighlightResultDto> UpdateHighlightBulkWithPaymentAsync(List<Guid> propertyIds, Guid userId, bool isHighlighted);
        Task<IEnumerable<PropertyStatusLogDto>> GetPropertyHistoryStatus(Guid propertyId);
        Task<int> VerifyPropertyRemainingTimes(Guid propertyId);
        Task<PagedResultDto<PropertyDto>> SearchPropertiesAsync(PropertyFilterCriteriaDto filterCriteria);
        Task<bool> UpdatePropertyRenewalAsync(UpdatePropertyRenewalDto updateDto, Guid userId);
    }
}
