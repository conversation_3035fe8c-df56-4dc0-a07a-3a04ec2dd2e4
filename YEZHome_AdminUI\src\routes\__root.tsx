import { Outlet, createRootRouteWithContext, redirect } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'

import { isAuthenticated } from '@/lib/auth'
import AdminLayout from '@/components/AdminLayout'
import TanStackQueryLayout from '../integrations/tanstack-query/layout.tsx'

import type { QueryClient } from '@tanstack/react-query'

interface MyRouterContext {
  queryClient: QueryClient
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  beforeLoad: ({ location }) => {
    // Allow access to login page without authentication
    if (location.pathname === '/login') {
      return
    }

    // If user is not authenticated and trying to access a protected route, redirect to login
    if (!isAuthenticated() && location.pathname !== '/login') {
      throw redirect({
        to: '/login',
      })
    }

    // If user is authenticated and trying to access login page, redirect to home
    if (isAuthenticated() && location.pathname === '/login') {
      throw redirect({
        to: '/',
      })
    }
  },
  component: () => {
    const authenticated = isAuthenticated();
    const isLoginPage = window.location.pathname === '/login';

    return (
      <>
        {authenticated && !isLoginPage ? (
          <AdminLayout>
            <Outlet />
          </AdminLayout>
        ) : (
          <Outlet />
        )}
        <TanStackRouterDevtools />
        <TanStackQueryLayout />
      </>
    );
  },
})
