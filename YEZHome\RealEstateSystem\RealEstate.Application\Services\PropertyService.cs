﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Wallet;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Application.Services
{
    public class PropertyService : IPropertyService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUserDashboardService _userDashboardService;
        private readonly IWalletService _walletService;
        private readonly IInvoiceService _invoiceService;

        public PropertyService(IUnitOfWork unitOfWork, IMapper mapper, IUserDashboardService userDashboardService, IWalletService walletService, IInvoiceService invoiceService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _userDashboardService = userDashboardService;
            _walletService = walletService;
            _invoiceService = invoiceService;
        }

        public async Task<PropertyDto> GetPropertyByIdAsync(Guid id, bool asNoTracking = true)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id, asNoTracking, p => p.PropertyMedia, p => p.PropertyReviews);
            return _mapper.Map<PropertyDto>(property);
        }

        public async Task<IEnumerable<PropertyDto>> GetAllPropertiesAsync()
        {
            var properties = await _unitOfWork.Properties.GetAllAsync(false);
            return _mapper.Map<IEnumerable<PropertyDto>>(properties);
        }

        public async Task<IEnumerable<PropertyDto>> GetPropertyByUserAsync(Guid userId)
        {
            var properties = await _unitOfWork.Properties.FindAsync(p => p.OwnerID == userId);
            return _mapper.Map<IEnumerable<PropertyDto>>(properties);
        }

        public async Task<PagedResultDto<PropertyDto>> GetPropertyByUserWithStatusAsync(Guid userId, List<string> statuses, int page = 1, int pageSize = 10)
        {
            // Start with a query for the user's properties
            var query = _unitOfWork.Properties.GetQueryable()
                .Include(p => p.PropertyMedia)
                .Where(p => p.OwnerID == userId && !p.IsDeleted);

            // Apply status filter if provided
            if (statuses != null && statuses.Any())
            {
                query = query.Where(p => statuses.Contains(p.Status));
            }

            // Count total items before pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var pageCount = (int)Math.Ceiling(totalCount / (double)pageSize);

            // Ensure current page is valid
            if (page < 1)
                page = 1;
            else if (page > pageCount && pageCount > 0)
                page = pageCount;

            // Apply pagination and get items
            var items = await query
                .OrderByDescending(p => p.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Map to DTOs
            var itemDtos = _mapper.Map<List<PropertyDto>>(items);

            // Create and return paged result
            return new PagedResultDto<PropertyDto>
            {
                Items = itemDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = page,
                PageSize = pageSize
            };
        }

        public async Task<PropertyDto> CreatePropertyAsync(CreatePropertyDto propertyDto, Guid userId)
        {
            var property = _mapper.Map<Property>(propertyDto);

            if (propertyDto.OwnerId == null)
            {
                property.OwnerID = userId;
            }
            else
            {
                property.OwnerID = propertyDto.OwnerId.Value;
            }

            property.Status = propertyDto.Status ?? EnumValues.PropertyStatus.PendingApproval.ToString();
            property.CreatedBy = userId;
            property.CreatedAt = DateTime.UtcNow;
            property.GenerateSlugFromName();
            await _unitOfWork.Properties.AddAsync(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = property.Id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = property.Status,
                Comment = "tạo tin đăng thành công",
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);

            // Create NEW_POST invoice for the property
            await CreatePropertyInvoiceAsync(property.Id, propertyDto, userId);

            // Single transaction commit for property, status log, and invoice
            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<PropertyDto>(property);
        }

        public async Task<PropertyDto> UpdatePropertyAsync(Guid id, CreatePropertyDto propertyDto, Guid userId)
        {
            // Validate input
            if (propertyDto == null)
            {
                throw new ArgumentNullException(nameof(propertyDto));
            }

            // Fetch the property to update
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null)
            {
                throw new NotFoundException($"Property with ID {id} not found.");
            }

            // Store original values for comparison
            var originalIsHighlighted = property.IsHighlighted;
            var originalIsAutoRenew = property.IsAutoRenew;
            var originalStatus = property.Status;

            // Validate highlight/autorenew changes based on property status
            if (originalStatus != EnumValues.PropertyStatus.Draft.ToString())
            {
                var hasHighlightChanged = propertyDto.IsHighlighted != originalIsHighlighted;
                var hasAutoRenewChanged = propertyDto.IsAutoRenew != originalIsAutoRenew;

                if (hasHighlightChanged || hasAutoRenewChanged)
                {
                    throw new InvalidOperationException($"Cannot change highlight or auto-renew settings when property status is not Draft. Current status: {originalStatus}");
                }
            }

            _mapper.Map(propertyDto, property);

            property.UpdatedBy = userId;
            property.GenerateSlugFromName();

            _unitOfWork.Properties.Update(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = propertyDto.Status,
                Comment = "update thông tin của tin đăng",
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);

            // Update invoice if status is Draft and highlight/autorenew changed
            if (originalStatus == EnumValues.PropertyStatus.Draft.ToString())
            {
                var hasHighlightChanged = propertyDto.IsHighlighted != originalIsHighlighted;
                var hasAutoRenewChanged = propertyDto.IsAutoRenew != originalIsAutoRenew;

                if (hasHighlightChanged || hasAutoRenewChanged)
                {
                    await UpdatePropertyInvoiceAsync(id, propertyDto, userId);
                }
            }

            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<PropertyDto>(property);
        }

        public async Task<bool> DeletePropertyAsync(Guid id, Guid userId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null) return false;

            property.UpdatedBy = userId;
            _unitOfWork.Properties.Remove(property);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeletePropertiesAsync(List<Guid> ids, Guid userId)
        {
            if (ids == null || !ids.Any())
                return false;

            bool allSuccessful = true;

            foreach (var id in ids)
            {
                var property = await _unitOfWork.Properties.GetByIdAsync(id);
                if (property == null)
                {
                    allSuccessful = false;
                    continue;
                }

                property.UpdatedBy = userId;
                _unitOfWork.Properties.Remove(property);
            }

            await _unitOfWork.SaveChangesAsync();
            return allSuccessful;
        }

        public async Task<bool> UpdateStatusAsync(Guid id, Guid userId, UpdateStatusDto updateStatusDto)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null) return false;

            property.Status = updateStatusDto.Status;
            property.UpdatedBy = userId;
            _unitOfWork.Properties.Update(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = updateStatusDto.Status,
                Comment = updateStatusDto.Status == EnumValues.PropertyStatus.Sold.ToString() ? "Giao dịch thành công" : updateStatusDto.Comment,
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateStatusBulkAsync(List<Guid> ids, Guid userId, UpdateStatusDto updateStatusDto)
        {
            if (ids == null || !ids.Any())
                return false;

            bool allSuccessful = true;

            foreach (var id in ids)
            {
                var property = await _unitOfWork.Properties.GetByIdAsync(id);
                if (property == null)
                {
                    allSuccessful = false;
                    continue;
                }

                property.Status = updateStatusDto.Status;
                property.UpdatedBy = userId;
                _unitOfWork.Properties.Update(property);

                var updateStatusLog = new PropertyStatusLog
                {
                    PropertyID = id,
                    ChangedBy = userId,
                    ChangedAt = DateTime.UtcNow,
                    Status = updateStatusDto.Status,
                    Comment = updateStatusDto.Status == EnumValues.PropertyStatus.Sold.ToString() ? "Giao dịch thành công" : updateStatusDto.Comment,
                };

                await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);
            }

            await _unitOfWork.SaveChangesAsync();
            return allSuccessful;
        }

        public async Task<bool> UpdateHighlightAsync(Guid id, Guid userId, bool isHighlighted)
        {
            // This method is kept for backward compatibility but should not be used for highlighting with payment
            // Use UpdateHighlightWithPaymentAsync instead for proper payment processing
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null) return false;

            property.IsHighlighted = isHighlighted;
            property.UpdatedBy = userId;
            _unitOfWork.Properties.Update(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = property.Status,
                Comment = isHighlighted ? "Đánh dấu nổi bật" : "Bỏ đánh dấu nổi bật",
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Update highlight status for a single property with payment processing and transaction management
        /// </summary>
        /// <param name="propertyId">Property ID to highlight</param>
        /// <param name="userId">User ID performing the operation</param>
        /// <param name="isHighlighted">Whether to highlight or unhighlight the property</param>
        /// <returns>Detailed result of the highlight operation</returns>
        public async Task<PropertyHighlightResultDto> UpdateHighlightWithPaymentAsync(Guid propertyId, Guid userId, bool isHighlighted)
        {
            var result = new PropertyHighlightResultDto
            {
                Success = false,
                PropertyId = propertyId,
                IsHighlighted = false,
                Cost = 0
            };

            try
            {
                // Step 1: Get and validate property
                var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
                if (property == null)
                {
                    result.Message = "Bất động sản không tồn tại";
                    return result;
                }

                result.PropertyTitle = property.Name;

                // Step 2: Check ownership
                if (property.OwnerID != userId)
                {
                    result.Message = "Không có quyền cập nhật bất động sản này";
                    return result;
                }

                // Step 3: Handle highlighting logic
                if (isHighlighted)
                {
                    // Check if already highlighted
                    if (property.IsHighlighted)
                    {
                        result.Message = "Bất động sản đã được đánh dấu nổi bật";
                        result.IsHighlighted = true;
                        result.Property = _mapper.Map<PropertyDto>(property);
                        return result;
                    }

                    // Get highlight fee
                    decimal fee;
                    try
                    {
                        fee = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId);
                        result.Cost = fee;
                    }
                    catch (KeyNotFoundException)
                    {
                        result.Message = "Không thể xác định người dùng";
                        return result;
                    }
                    catch (InvalidOperationException)
                    {
                        result.Message = "Không thể xác định phí đánh dấu nổi bật";
                        return result;
                    }

                    // Check wallet balance
                    var walletBalance = await _walletService.GetWalletBalanceAsync(userId);
                    if (walletBalance.Balance < fee)
                    {
                        result.Message = $"Số dư ví không đủ. Cần {fee:N0} VND để đánh dấu nổi bật.";
                        return result;
                    }

                    // Process payment
                    var spendRequest = new SpendWalletDto
                    {
                        Amount = fee,
                        Description = $"Đánh dấu nổi bật bất động sản #{property.Id}"
                    };

                    var walletTransaction = await _walletService.SpendFromWalletAsync(userId, spendRequest);
                    result.TransactionId = walletTransaction.Id;

                    // Process the transaction immediately
                    var processRequest = new ProcessTransactionDto
                    {
                        TransactionId = walletTransaction.Id,
                        Action = "complete"
                    };

                    await _walletService.ProcessTransactionAsync(walletTransaction.Id, processRequest, userId);

                    // Create HIGHLIGHT invoice for approved properties
                    if (property.Status == EnumValues.PropertyStatus.Approved.ToString())
                    {
                        var invoice = await CreateHighlightInvoiceAsync(propertyId, userId, fee);
                        result.InvoiceId = invoice.Id;
                    }
                }

                // Step 4: Update property highlight status
                property.IsHighlighted = isHighlighted;
                property.UpdatedBy = userId;
                _unitOfWork.Properties.Update(property);

                // Step 5: Create status log
                var updateStatusLog = new PropertyStatusLog
                {
                    PropertyID = propertyId,
                    ChangedBy = userId,
                    ChangedAt = DateTime.UtcNow,
                    Status = property.Status,
                    Comment = isHighlighted ? "Đánh dấu nổi bật" : "Bỏ đánh dấu nổi bật",
                };

                await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);

                // Step 6: Save all changes
                await _unitOfWork.SaveChangesAsync();

                // Step 7: Return success result
                result.Success = true;
                result.IsHighlighted = isHighlighted;
                result.Message = isHighlighted ? "Đã đánh dấu nổi bật thành công" : "Đã bỏ đánh dấu nổi bật thành công";
                result.Property = await GetPropertyByIdAsync(propertyId);

                return result;
            }
            catch (Exception ex)
            {
                // Entity Framework will automatically rollback changes on exception
                result.Message = $"Đã xảy ra lỗi trong quá trình xử lý: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// Update highlight status for multiple properties with payment processing and transaction management
        /// </summary>
        /// <param name="propertyIds">List of property IDs to highlight</param>
        /// <param name="userId">User ID performing the operation</param>
        /// <param name="isHighlighted">Whether to highlight or unhighlight properties</param>
        /// <returns>Detailed result of the bulk operation</returns>
        public async Task<BulkHighlightResultDto> UpdateHighlightBulkWithPaymentAsync(List<Guid> propertyIds, Guid userId, bool isHighlighted)
        {
            var result = new BulkHighlightResultDto
            {
                Success = false,
                Message = "",
                TotalProcessed = propertyIds?.Count ?? 0,
                PropertyResults = new List<PropertyHighlightResultDto>()
            };

            if (propertyIds == null || !propertyIds.Any())
            {
                result.Message = "Không có bất động sản nào được chọn";
                return result;
            }

            // All operations will be handled within a single SaveChangesAsync call for transaction safety
            try
            {
                // Step 1: Get all properties and validate ownership
                var properties = new List<Property>();
                foreach (var propertyId in propertyIds)
                {
                    var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
                    if (property == null)
                    {
                        result.PropertyResults.Add(new PropertyHighlightResultDto
                        {
                            PropertyId = propertyId,
                            Success = false,
                            Status = "failed",
                            ErrorMessage = "Bất động sản không tồn tại"
                        });
                        result.Failed++;
                        continue;
                    }

                    // Check if user owns the property
                    if (property.OwnerID != userId)
                    {
                        result.PropertyResults.Add(new PropertyHighlightResultDto
                        {
                            PropertyId = propertyId,
                            PropertyTitle = property.Name,
                            Success = false,
                            Status = "failed",
                            ErrorMessage = "Không có quyền cập nhật bất động sản này"
                        });
                        result.Failed++;
                        continue;
                    }

                    properties.Add(property);
                }

                if (!properties.Any())
                {
                    result.Message = "Không có bất động sản hợp lệ nào để xử lý";
                    return result;
                }

                // Step 2: Filter properties that need highlighting (skip already highlighted ones)
                var propertiesToHighlight = new List<Property>();
                foreach (var property in properties)
                {
                    if (isHighlighted)
                    {
                        if (property.IsHighlighted)
                        {
                            // Already highlighted, skip
                            result.PropertyResults.Add(new PropertyHighlightResultDto
                            {
                                PropertyId = property.Id,
                                PropertyTitle = property.Name,
                                Success = true,
                                Status = "already_highlighted",
                                ErrorMessage = "Bất động sản đã được đánh dấu nổi bật"
                            });
                            result.AlreadyHighlighted++;
                        }
                        else
                        {
                            propertiesToHighlight.Add(property);
                        }
                    }
                    else
                    {
                        // Unhighlighting - process all properties
                        propertiesToHighlight.Add(property);
                    }
                }

                // Step 3: Calculate total cost for highlighting
                decimal totalCost = 0;
                if (isHighlighted && propertiesToHighlight.Any())
                {
                    var highlightFee = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId);
                    totalCost = highlightFee * propertiesToHighlight.Count;
                    result.TotalCost = totalCost;

                    // Step 4: Check wallet balance
                    var walletBalance = await _walletService.GetWalletBalanceAsync(userId);
                    if (walletBalance.Balance < totalCost)
                    {
                        result.Message = $"Số dư ví không đủ. Cần {totalCost:N0} VND để đánh dấu nổi bật {propertiesToHighlight.Count} bất động sản. Số dư hiện tại: {walletBalance.Balance:N0} VND.";
                        return result;
                    }
                }

                // Step 5: Process each property
                foreach (var property in propertiesToHighlight)
                {
                    try
                    {
                        var propertyResult = new PropertyHighlightResultDto
                        {
                            PropertyId = property.Id,
                            PropertyTitle = property.Name,
                            Success = false,
                            Status = "failed"
                        };

                        if (isHighlighted)
                        {
                            // Process payment for highlighting
                            var highlightFee = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId);
                            propertyResult.Cost = highlightFee;

                            // Create wallet transaction
                            var spendRequest = new SpendWalletDto
                            {
                                Amount = highlightFee,
                                Description = $"Đánh dấu nổi bật bất động sản #{property.Id}"
                            };

                            var walletTransaction = await _walletService.SpendFromWalletAsync(userId, spendRequest);
                            propertyResult.TransactionId = walletTransaction.Id;

                            // Process the transaction immediately
                            var processRequest = new ProcessTransactionDto
                            {
                                TransactionId = walletTransaction.Id,
                                Action = "complete"
                            };

                            await _walletService.ProcessTransactionAsync(walletTransaction.Id, processRequest, userId);

                            // Create HIGHLIGHT invoice for approved properties
                            if (property.Status == EnumValues.PropertyStatus.Approved.ToString())
                            {
                                var invoice = await CreateHighlightInvoiceAsync(property.Id, userId, highlightFee);
                                propertyResult.InvoiceId = invoice.Id;
                            }
                        }

                        // Update property highlight status
                        property.IsHighlighted = isHighlighted;
                        property.UpdatedBy = userId;
                        _unitOfWork.Properties.Update(property);

                        // Create status log
                        var updateStatusLog = new PropertyStatusLog
                        {
                            PropertyID = property.Id,
                            ChangedBy = userId,
                            ChangedAt = DateTime.UtcNow,
                            Status = property.Status,
                            Comment = isHighlighted ? "Đánh dấu nổi bật" : "Bỏ đánh dấu nổi bật",
                        };

                        await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);

                        propertyResult.Success = true;
                        propertyResult.Status = isHighlighted ? "highlighted" : "unhighlighted";
                        result.PropertyResults.Add(propertyResult);
                        result.SuccessfullyHighlighted++;
                    }
                    catch (Exception ex)
                    {
                        result.PropertyResults.Add(new PropertyHighlightResultDto
                        {
                            PropertyId = property.Id,
                            PropertyTitle = property.Name,
                            Success = false,
                            Status = "failed",
                            ErrorMessage = ex.Message
                        });
                        result.Failed++;
                    }
                }

                // Save all changes in a single transaction
                await _unitOfWork.SaveChangesAsync();

                result.Success = result.SuccessfullyHighlighted > 0 || result.AlreadyHighlighted > 0;
                result.Message = result.Success
                    ? $"Đã xử lý thành công {result.SuccessfullyHighlighted} bất động sản, {result.AlreadyHighlighted} đã được đánh dấu từ trước, {result.Failed} thất bại"
                    : "Không có bất động sản nào được xử lý thành công";

                return result;
            }
            catch (Exception ex)
            {
                // Entity Framework will automatically rollback changes on exception
                result.Message = $"Đã xảy ra lỗi trong quá trình xử lý: {ex.Message}";
                return result;
            }
        }

        public async Task<IEnumerable<PropertyStatusLogDto>> GetPropertyHistoryStatus(Guid propertyId)
        {
            var history = await _unitOfWork.PropertyStatusLogs.FindAsync(p => p.PropertyID == propertyId);
            return _mapper.Map<IEnumerable<PropertyStatusLogDto>>(history.OrderByDescending(x => x.ChangedAt));
        }

        public async Task<int> VerifyPropertyRemainingTimes(Guid propertyId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null) throw new NotFoundException("Bất động sản không tồn tại.");

            var remainingTime = property.UpdateRemainingTimes;
            return remainingTime ?? 0;
        }

        public async Task<PropertyCountStatsDto> GetPropertyCountStatsByUserAsync(Guid userId)
        {
            // Get all properties for the user (not deleted)
            var properties = await _unitOfWork.Properties.FindAsync(p => p.OwnerID == userId && !p.IsDeleted);

            // Create the result object
            var result = new PropertyCountStatsDto
            {
                TotalProperties = properties.Count()
            };

            // Initialize the dictionary with all possible status values
            foreach (var status in Enum.GetNames(typeof(EnumValues.PropertyStatus)))
            {
                result.PropertiesByStatus[status] = 0;
            }

            // Count properties by status
            foreach (var property in properties)
            {
                if (result.PropertiesByStatus.ContainsKey(property.Status))
                {
                    result.PropertiesByStatus[property.Status]++;
                }
                else
                {
                    // Handle any status that might not be in the enum (for backward compatibility)
                    result.PropertiesByStatus[property.Status] = 1;
                }
            }

            return result;
        }

        public async Task<PagedResultDto<PropertyDto>> SearchPropertiesAsync(PropertyFilterCriteriaDto filterCriteria)
        {
            // Start with a query that gets all non-deleted properties
            var query = _unitOfWork.Properties.GetQueryable()
                .Include(p => p.PropertyMedia)
                .Where(p => !p.IsDeleted && p.Status == "Approved");

            // Apply filters
            if (filterCriteria.PostTypes != null && filterCriteria.PostTypes.Any())
                query = query.Where(p => filterCriteria.PostTypes.Contains(p.PostType));

            if (filterCriteria.PropertyTypes != null && filterCriteria.PropertyTypes.Any())
                query = query.Where(p => filterCriteria.PropertyTypes.Contains(p.PropertyType));

            //if (!string.IsNullOrEmpty(filterCriteria.CityId))
            //    query = query.Where(p => p.CityId == filterCriteria.CityId);

            //if (!string.IsNullOrEmpty(filterCriteria.DistrictId))
            //    query = query.Where(p => p.DistrictId == filterCriteria.DistrictId);

            if (!string.IsNullOrEmpty(filterCriteria.Address))
                query = query.Where(p => p.Address.Contains(filterCriteria.Address));

            if (filterCriteria.MinPrice.HasValue)
            {
                var minPrice = filterCriteria.MinPrice.Value * 1000000; // Convert to milion
                query = query.Where(p => p.Price >= minPrice);
            }

            if (filterCriteria.MaxPrice.HasValue)
            {
                var maxPrice = filterCriteria.MaxPrice.Value * 1000000; // Convert to milion
                query = query.Where(p => p.Price <= maxPrice);
            }

            if (filterCriteria.MinArea.HasValue)
                query = query.Where(p => p.Area >= filterCriteria.MinArea.Value);

            if (filterCriteria.MaxArea.HasValue)
                query = query.Where(p => p.Area <= filterCriteria.MaxArea.Value);

            if (filterCriteria.MinRooms.HasValue)
                query = query.Where(p => p.Rooms >= filterCriteria.MinRooms.Value);

            if (filterCriteria.MinToilets.HasValue)
                query = query.Where(p => p.Toilets >= filterCriteria.MinToilets.Value);

            if (!string.IsNullOrEmpty(filterCriteria.Direction))
                query = query.Where(p => p.Direction == filterCriteria.Direction);

            if (!string.IsNullOrEmpty(filterCriteria.Legality))
                query = query.Where(p => p.Legality == filterCriteria.Legality);

            if (filterCriteria.MinRoadWidth.HasValue)
                query = query.Where(p => p.RoadWidth >= filterCriteria.MinRoadWidth.Value);

            // Apply geographic filters if provided
            if (filterCriteria.SwLat.HasValue && filterCriteria.SwLng.HasValue && filterCriteria.NeLat.HasValue && filterCriteria.NeLng.HasValue)
            {
                query = query.Where(p =>
                    p.Latitude >= filterCriteria.SwLat.Value &&
                    p.Latitude <= filterCriteria.NeLat.Value &&
                    p.Longitude >= filterCriteria.SwLng.Value &&
                    p.Longitude <= filterCriteria.NeLng.Value);
            }

            //// Proximity search if coordinates and radius are provided
            //if (filterCriteria.Latitude.HasValue && filterCriteria.Longitude.HasValue && filterCriteria.Radius.HasValue)
            //{
            //    double earthRadiusKm = 6371; // Earth's radius in km

            //    // Convert latitude and longitude to radians BEFORE using them in the query
            //    double latRad = filterCriteria.Latitude.Value * Math.PI / 180.0;
            //    double lonRad = filterCriteria.Longitude.Value * Math.PI / 180.0;

            //    query = query.Where(l =>
            //                (earthRadiusKm * Math.Acos(
            //                    Math.Sin(latRad) * Math.Sin(Convert.ToDouble(l.Latitude) * Math.PI / 180.0) +
            //                    Math.Cos(latRad) * Math.Cos(Convert.ToDouble(l.Latitude) * Math.PI / 180.0) *
            //                    Math.Cos((Convert.ToDouble(l.Longitude) * Math.PI / 180.0) - lonRad)
            //                )) <= filterCriteria.Radius);
            //}

            // Count total items before pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var pageSize = filterCriteria.PageSize;
            var pageCount = (int)Math.Ceiling(totalCount / (double)pageSize);
            var currentPage = filterCriteria.Page;

            // Ensure current page is valid
            if (currentPage < 1)
                currentPage = 1;
            else if (currentPage > pageCount && pageCount > 0)
                currentPage = pageCount;

            // Apply pagination and get items
            var items = await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip((currentPage - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Map to DTOs
            var itemDtos = _mapper.Map<List<PropertyDto>>(items);

            // Create and return paged result
            return new PagedResultDto<PropertyDto>
            {
                Items = itemDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = currentPage,
                PageSize = pageSize
            };
        }

        // Helper method for distance calculation (Haversine formula)
        private double CalculateDistance(double lat1, double lon1, decimal lat2Decimal, decimal lon2Decimal)
        {
            double lat2 = Convert.ToDouble(lat2Decimal);
            double lon2 = Convert.ToDouble(lon2Decimal);

            const double EarthRadiusKm = 6371;

            var dLat = DegreesToRadians(lat2 - lat1);
            var dLon = DegreesToRadians(lon2 - lon1);

            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(DegreesToRadians(lat1)) * Math.Cos(DegreesToRadians(lat2)) *
                    Math.Sin(dLon / 2) * Math.Sin(dLon / 2);

            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            return EarthRadiusKm * c;
        }

        private double DegreesToRadians(double degrees)
        {
            return degrees * Math.PI / 180;
        }

        public async Task<bool> UpdatePropertyRenewalAsync(UpdatePropertyRenewalDto updateDto, Guid userId)
        {
            // Validate input
            if (updateDto == null)
            {
                throw new ArgumentNullException(nameof(updateDto));
            }

            // Fetch the property to update
            var property = await _unitOfWork.Properties.GetByIdAsync(updateDto.PropertyId, false);
            if (property == null)
            {
                throw new NotFoundException($"Property with ID {updateDto.PropertyId} not found.");
            }

            // Update expiration date
            property.ExpiresAt = updateDto.ExpiresAt;

            // Increment renewal count if requested
            if (updateDto.IncrementRenewalCount)
            {
                property.RenewalCount = (property.RenewalCount ?? 0) + 1;
            }

            // Update the property
            property.UpdatedBy = userId;
            _unitOfWork.Properties.Update(property);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        #region Private Helper Methods for Invoice Management

        /// <summary>
        /// Create separate HIGHLIGHT invoice for approved properties within the same transaction
        /// </summary>
        private async Task<InvoiceDto> CreateHighlightInvoiceAsync(Guid propertyId, Guid userId, decimal highlightFee)
        {
            // Create invoice items for highlight
            var invoiceItems = new List<CreateInvoiceItemDto>
            {
                new CreateInvoiceItemDto
                {
                    ItemType = EnumValues.InvoiceItemType.HIGHLIGHT,
                    Description = "Phí làm nổi bật tin đăng đã duyệt",
                    Amount = (int)highlightFee
                }
            };

            // Create invoice DTO
            var createInvoiceDto = new CreateInvoiceDto
            {
                PropertyId = propertyId,
                Type = EnumValues.InvoiceType.HIGHLIGHT,
                TotalAmount = (int)highlightFee,
                Note = "Hóa đơn làm nổi bật tin đăng đã được duyệt",
                InvoiceItems = invoiceItems
            };

            // Create the invoice and immediately mark as completed since payment was already processed
            var invoice = await _invoiceService.CreateInvoiceAsync(createInvoiceDto, userId);

            // Mark invoice as completed since wallet transaction was already processed
            await _invoiceService.MarkInvoiceAsPaidAsync(invoice.Id, userId);

            return invoice;
        }

        /// <summary>
        /// Create NEW_POST invoice for property creation within the same transaction
        /// </summary>
        private async Task CreatePropertyInvoiceAsync(Guid propertyId, CreatePropertyDto propertyDto, Guid userId)
        {
            // Calculate total amount based on property settings
            var totalAmount = await CalculatePropertyInvoiceAmountAsync(propertyDto, userId);

            // Create invoice entity
            var invoice = new Invoice
            {
                UserId = userId,
                PropertyId = propertyId,
                Type = FunctionHelper.GetEnumDescription(EnumValues.InvoiceType.NEW_POST),
                TotalAmount = (int)totalAmount,
                Status = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.PENDING),
                Note = $"Hóa đơn tạo tin đăng mới{(propertyDto.IsHighlighted ? " (có nổi bật)" : "")}{(propertyDto.IsAutoRenew ? " (có tự động gia hạn)" : "")}",
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.Invoices.AddAsync(invoice);

            // Create invoice items
            var baseInvoiceItem = new InvoiceItem
            {
                InvoiceId = invoice.Id,
                ItemType = FunctionHelper.GetEnumDescription(EnumValues.InvoiceItemType.NEW_POST),
                Description = "Phí đăng tin 10 ngày",
                Amount = 55000
            };

            await _unitOfWork.InvoiceItems.AddAsync(baseInvoiceItem);

            // Add highlight item if enabled
            if (propertyDto.IsHighlighted)
            {
                var highlightFee = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId);
                var highlightInvoiceItem = new InvoiceItem
                {
                    InvoiceId = invoice.Id,
                    ItemType = FunctionHelper.GetEnumDescription(EnumValues.InvoiceItemType.HIGHLIGHT),
                    Description = "Phí làm nổi bật tin đăng",
                    Amount = (int)highlightFee
                };

                await _unitOfWork.InvoiceItems.AddAsync(highlightInvoiceItem);
            }
        }

        /// <summary>
        /// Calculate total invoice amount based on property settings
        /// </summary>
        private async Task<decimal> CalculatePropertyInvoiceAmountAsync(CreatePropertyDto propertyDto, Guid userId)
        {
            decimal baseAmount = 55000; // Default post price for 10 days
            decimal highlightAmount = 0;

            if (propertyDto.IsHighlighted)
            {
                highlightAmount = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId);
            }

            // AutoRenew doesn't add immediate cost, just enables automatic renewal
            // The cost will be charged when renewal actually happens

            return baseAmount + highlightAmount;
        }

        /// <summary>
        /// Update existing NEW_POST invoice based on property changes within the same transaction
        /// </summary>
        private async Task UpdatePropertyInvoiceAsync(Guid propertyId, CreatePropertyDto propertyDto, Guid userId)
        {
            // Find existing NEW_POST invoice for this property
            var newPostType = FunctionHelper.GetEnumDescription(EnumValues.InvoiceType.NEW_POST);
            var existingInvoice = await _unitOfWork.Invoices.GetQueryable()
                .FirstOrDefaultAsync(i => i.PropertyId == propertyId && i.Type == newPostType);

            if (existingInvoice == null)
            {
                throw new InvalidOperationException($"No NEW_POST invoice found for property {propertyId}");
            }

            // Calculate new total amount
            var newTotalAmount = await CalculatePropertyInvoiceAmountAsync(propertyDto, userId);

            // Update invoice
            existingInvoice.TotalAmount = (int)newTotalAmount;
            existingInvoice.Note = $"Hóa đơn cập nhật tin đăng{(propertyDto.IsHighlighted ? " (có nổi bật)" : "")}{(propertyDto.IsAutoRenew ? " (có tự động gia hạn)" : "")}";

            _unitOfWork.Invoices.Update(existingInvoice);

            // Remove existing invoice items
            var existingItems = await _unitOfWork.InvoiceItems.GetQueryable()
                .Where(item => item.InvoiceId == existingInvoice.Id)
                .ToListAsync();

            foreach (var item in existingItems)
            {
                _unitOfWork.InvoiceItems.Remove(item);
            }

            // Create new invoice items
            var baseInvoiceItem = new InvoiceItem
            {
                InvoiceId = existingInvoice.Id,
                ItemType = FunctionHelper.GetEnumDescription(EnumValues.InvoiceItemType.NEW_POST),
                Description = "Phí đăng tin 10 ngày",
                Amount = 55000
            };

            await _unitOfWork.InvoiceItems.AddAsync(baseInvoiceItem);

            // Add highlight item if enabled
            if (propertyDto.IsHighlighted)
            {
                var highlightFee = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId);
                var highlightInvoiceItem = new InvoiceItem
                {
                    InvoiceId = existingInvoice.Id,
                    ItemType = FunctionHelper.GetEnumDescription(EnumValues.InvoiceItemType.HIGHLIGHT),
                    Description = "Phí làm nổi bật tin đăng",
                    Amount = (int)highlightFee
                };

                await _unitOfWork.InvoiceItems.AddAsync(highlightInvoiceItem);
            }
        }

        #endregion
    }
}
