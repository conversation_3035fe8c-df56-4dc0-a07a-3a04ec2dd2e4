﻿using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.InternalAPI.Controllers
{
    
    /// <summary>
    /// Controller for internal user management operations such as roles, dashboard, reactivation, and invoice info.
    /// </summary>
    [Route("api/[controller]")]
    public class UserController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserDashboardService _dashboardService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, IUserDashboardService dashboardService,
        ILogger<UserController> logger)
        {
            _userService = userService;
            _dashboardService = dashboardService;
            _logger = logger;
        }

        /// <summary>
        /// Adds a role to a user (internal use).
        /// </summary>
        /// <param name="addUserRoleDto">The user role data.</param>
        /// <returns>True if the role was added successfully.</returns>
        [HttpPut("role")]
        public async Task<ActionResult<AddUserRoleDto>> AddUserRole(AddUserRoleDto addUserRoleDto)
        {
            _logger?.LogInformation("[AddUserRole] Adding role {RoleId} to user {UserId}", addUserRoleDto.RoleId, addUserRoleDto.UserId);
            try
            {
                bool isOk = await _userService.AddUserRoleAsync(addUserRoleDto);
                _logger?.LogInformation("[AddUserRole] Result: {Result}", isOk);
                return Ok(isOk);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[AddUserRole] Error adding role {RoleId} to user {UserId}", addUserRoleDto.RoleId, addUserRoleDto.UserId);
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Retrieves user information by user ID (internal use).
        /// </summary>
        /// <param name="id">The user ID.</param>
        /// <returns>User information.</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            _logger?.LogInformation("[GetUser] Retrieving user information for user {UserId}", id);
            try
            {
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    _logger?.LogWarning("[GetUser] User {UserId} not found", id);
                    return NotFound();
                }
                _logger?.LogInformation("[GetUser] Successfully retrieved user information for user {UserId}", id);
                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[GetUser] Error retrieving user {UserId}", id);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Retrieves the dashboard for a user by user ID (internal use).
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>User dashboard data.</returns>
        [HttpGet("dashboard/{userId}")]
        public async Task<ActionResult<UserDashboardDto>> GetUserDashboardById(Guid userId)
        {
            _logger?.LogInformation("[GetUserDashboardById] Retrieving dashboard for user {UserId}", userId);
            try
            {
                var dashboard = await _dashboardService.GetUserDashboardAsync(userId);
                _logger?.LogInformation("[GetUserDashboardById] Successfully retrieved dashboard for user {UserId}", userId);
                return Ok(dashboard);
            }
            catch (KeyNotFoundException ex)
            {
                _logger?.LogWarning("[GetUserDashboardById] Dashboard not found for user {UserId}: {Message}", userId, ex.Message);
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[GetUserDashboardById] Error retrieving dashboard for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        /// <summary>
        /// Reactivates a user account (admin only).
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>Status of the reactivation.</returns>
        [HttpPost("reactivate/{userId}")]
        public async Task<ActionResult> ReactivateAccount(Guid userId)
        {
            _logger?.LogInformation("[ReactivateAccount] Attempting to reactivate account for user {UserId}", userId);
            try
            {
                var result = await _userService.ReactivateUserAsync(userId);
                if (!result)
                {
                    _logger?.LogWarning("[ReactivateAccount] Failed to reactivate account for user {UserId}", userId);
                    return BadRequest("Failed to reactivate account. User not found.");
                }
                _logger?.LogInformation("[ReactivateAccount] Account has been reactivated for user {UserId}", userId);
                return Ok(new { Message = "Account has been reactivated successfully." });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[ReactivateAccount] Error reactivating account for user {UserId}", userId);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }        

        /// <summary>
        /// Retrieves invoice information for a user (admin only).
        /// </summary>
        /// <param name="id">The user ID.</param>
        /// <returns>User invoice information.</returns>
        [HttpGet("{id}/invoice-info")]
        public async Task<ActionResult<UserInvoiceInfoDto>> GetUserInvoiceInfo(Guid id)
        {
            _logger?.LogInformation("[GetUserInvoiceInfo] Retrieving invoice info for user {UserId}", id);
            try
            {
                var invoiceInfo = await _userService.GetUserInvoiceInfoAsync(id);
                if (invoiceInfo == null)
                {
                    _logger?.LogWarning("[GetUserInvoiceInfo] User {UserId} not found", id);
                    return NotFound("User not found");
                }
                _logger?.LogInformation("[GetUserInvoiceInfo] Successfully retrieved invoice info for user {UserId}", id);
                return Ok(invoiceInfo);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[GetUserInvoiceInfo] Error retrieving invoice info for user {UserId}", id);
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
}
