"use client";

import { useState, useRef, useEffect } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";

const CollapseHeader = ({ title, subTitle, children, defaultOpen = true, className }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const isOpenRef = useRef(isOpen);

  const contentRef = useRef(null);
  const [maxHeight, setMaxHeight] = useState("0px");

  // Keep isOpenRef in sync with isOpen
  useEffect(() => {
    isOpenRef.current = isOpen;
  }, [isOpen]);

  // Function to update max height based on content
  const updateMaxHeight = () => {
    if (contentRef.current) {
      setMaxHeight(isOpenRef.current ? `${contentRef.current.scrollHeight}px` : "0px");
    }
  };

  useEffect(() => {
    updateMaxHeight();
  }, [isOpen]);

  // Use ResizeObserver to detect content size changes
  useEffect(() => {
    const contentElement = contentRef.current;
    if (!contentElement) return;

    const resizeObserver = new ResizeObserver((entries) => {
      // Only update if the collapse is open (using ref for latest state)
      if (isOpenRef.current) {
        updateMaxHeight();
      }
    });

    resizeObserver.observe(contentElement);

    // Cleanup observer on unmount
    return () => {
      resizeObserver.unobserve(contentElement);
      resizeObserver.disconnect();
    };
  }, []); // Re-run only on mount and unmount

  return (
    <div className={cn("bg-white rounded-md shadow-xs border mb-4", className)}>
      <button
        type="button"
        className="w-full flex justify-between items-center px-4 py-3 hover:text-teal-600 transition-colors"
        onClick={(e) => {
          e.preventDefault();
          setIsOpen((prev) => !prev);
        }}
        aria-expanded={isOpen}
        aria-controls="collapse-content"
      >
        <h4 className="font-semibold text-gray-800 mb-0">
          {title}
          {subTitle && <span className="text-xs ml-2 text-gray-400">({subTitle})</span>}
        </h4>
        {isOpen ? <ChevronUp className="text-gray-500" size={20} /> : <ChevronDown className="text-gray-500" size={20} />}
      </button>

      <div
        ref={contentRef}
        style={{ maxHeight }}
        className={cn(`overflow-hidden transition-[max-height] duration-300 ease-in-out`, isOpen ? "px-4 pb-4" : "px-4 pb-0")}
      >
        {children}
      </div>
    </div>
  );
};

export default CollapseHeader;
