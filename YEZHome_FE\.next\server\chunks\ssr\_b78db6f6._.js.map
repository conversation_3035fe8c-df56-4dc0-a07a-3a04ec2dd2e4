{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/userFavorite.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/UserFavorites`;\r\n\r\nexport async function addToFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/add`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyId }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"addToFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi thêm vào danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function removeFromFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/remove/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"removeFromFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa khỏi danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function checkFavoriteStatus(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/check`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyIds: Array.isArray(propertyIds) ? propertyIds : [propertyIds] }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkFavoriteStatus\",\r\n      propertyIds,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function getFavoritesCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getFavoritesCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số lượng bất động sản yêu thích\");\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the user's favorite properties\r\n * @returns {Promise<{success: boolean, data: Array<UserFavoriteDto>, message: string}>} Response with array of UserFavoriteDto objects\r\n * @description UserFavoriteDto contains: id, propertyId, createdAt\r\n */\r\nexport async function getUserFavorites() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/favorites`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserFavorites\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích\");\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the user's favorite properties with full property details, pagination and filtering\r\n * @param {Object} filters - Filter options\r\n * @param {number} filters.minPrice - Minimum price filter\r\n * @param {number} filters.maxPrice - Maximum price filter\r\n * @param {string} filters.fromDate - Start date filter (ISO string)\r\n * @param {string} filters.toDate - End date filter (ISO string)\r\n * @param {string} filters.sortBy - Sort field (CreatedAt, Price)\r\n * @param {boolean} filters.sortDescending - Sort direction\r\n * @param {number} filters.page - Page number\r\n * @param {number} filters.pageSize - Items per page\r\n * @returns {Promise<{success: boolean, data: PagedFavoriteResultDto, message: string}>} Response with paginated favorites and property details\r\n */\r\nexport async function getUserFavoritesWithDetails(filters = {}) {\r\n  try {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    if (filters.minPrice !== undefined && filters.minPrice !== null) {\r\n      queryParams.append('minPrice', filters.minPrice.toString());\r\n    }\r\n    if (filters.maxPrice !== undefined && filters.maxPrice !== null) {\r\n      queryParams.append('maxPrice', filters.maxPrice.toString());\r\n    }\r\n    if (filters.fromDate) {\r\n      queryParams.append('fromDate', filters.fromDate);\r\n    }\r\n    if (filters.toDate) {\r\n      queryParams.append('toDate', filters.toDate);\r\n    }\r\n    if (filters.sortBy) {\r\n      queryParams.append('sortBy', filters.sortBy);\r\n    }\r\n    if (filters.sortDescending !== undefined) {\r\n      queryParams.append('sortDescending', filters.sortDescending.toString());\r\n    }\r\n    if (filters.page) {\r\n      queryParams.append('page', filters.page.toString());\r\n    }\r\n    if (filters.pageSize) {\r\n      queryParams.append('pageSize', filters.pageSize.toString());\r\n    }\r\n\r\n    const url = `${API_BASE_URL}/favorites-with-details${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n\r\n    return await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserFavoritesWithDetails\",\r\n      filters,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;;;;;;AAEA,MAAM,eAAe,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC;AAExD,eAAe,eAAe,UAAU;IAC7C,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,IAAI,CAAC,EAAE;YAChD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAW;QACpC;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAEO,eAAe,oBAAoB,UAAU;IAClD,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,QAAQ,EAAE,YAAY,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAEO,eAAe,oBAAoB,WAAW;IACnD,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,MAAM,CAAC,EAAE;YAClD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE,aAAa,MAAM,OAAO,CAAC,eAAe,cAAc;oBAAC;iBAAY;YAAC;QAC/F;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAEO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,MAAM,CAAC,EAAE;YAClD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;QACV;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAOO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,UAAU,CAAC,EAAE;YACtD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;QACV;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAeO,eAAe,4BAA4B,UAAU,CAAC,CAAC;IAC5D,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,QAAQ,KAAK,aAAa,QAAQ,QAAQ,KAAK,MAAM;YAC/D,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;QAC1D;QACA,IAAI,QAAQ,QAAQ,KAAK,aAAa,QAAQ,QAAQ,KAAK,MAAM;YAC/D,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;QAC1D;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjD;QACA,IAAI,QAAQ,MAAM,EAAE;YAClB,YAAY,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC7C;QACA,IAAI,QAAQ,MAAM,EAAE;YAClB,YAAY,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC7C;QACA,IAAI,QAAQ,cAAc,KAAK,WAAW;YACxC,YAAY,MAAM,CAAC,kBAAkB,QAAQ,cAAc,CAAC,QAAQ;QACtE;QACA,IAAI,QAAQ,IAAI,EAAE;YAChB,YAAY,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ;QAClD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;QAC1D;QAEA,MAAM,MAAM,GAAG,aAAa,uBAAuB,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QAEjH,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;;;IAnJsB;IAkBA;IAiBA;IAkBA;IAqBA;IA6BA;;AAvGA,+OAAA;AAkBA,+OAAA;AAiBA,+OAAA;AAkBA,+OAAA;AAqBA,+OAAA;AA6BA,+OAAA", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/notification.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/notifications`;\r\n\r\n/**\r\n * Get notifications with pagination and optional type filtering\r\n * @param {Object} params - Query parameters\r\n * @param {number} params.page - Page number (default: 1)\r\n * @param {number} params.limit - Items per page (default: 10)\r\n * @param {string} params.type - Notification type (news, wallet_update, promotion, customer_message)\r\n * @returns {Promise<Object>} Response with notifications data\r\n */\r\nexport async function getNotifications(params = {}) {\r\n  try {\r\n    const { page = 1, limit = 10, type } = params;\r\n    \r\n    // If type is specified, use the by-type endpoint\r\n    let url = type \r\n      ? `${API_BASE_URL}/by-type/${type}?page=${page}&pageSize=${limit}`\r\n      : `${API_BASE_URL}?page=${page}&pageSize=${limit}`;\r\n    \r\n    return await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getNotifications\",\r\n      params,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông báo\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get unread notification count\r\n * @returns {Promise<Object>} Response with notification count data\r\n */\r\nexport async function getUnreadCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/unread-count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getUnreadCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số thông báo chưa đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Mark notifications as read\r\n * @param {Object} params - Request parameters\r\n * @param {Array<string>} params.ids - Array of notification IDs to mark as read\r\n * @returns {Promise<Object>} Response data\r\n */\r\nexport async function markAsRead(params) {\r\n  try {\r\n    // The API expects marking one notification at a time with a specific endpoint\r\n    if (params.ids && params.ids.length > 0) {\r\n      // Mark the first notification in the array\r\n      const id = params.ids[0];\r\n      return await fetchWithAuth(`${API_BASE_URL}/${id}/mark-as-read`, {\r\n        method: \"PUT\", // Changed from POST to PUT as per API doc\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n    }\r\n    return handleErrorResponse(false, null, \"Không có ID thông báo được cung cấp\");\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"markAsRead\",\r\n      params,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi đánh dấu thông báo đã đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Mark all notifications as read\r\n * @returns {Promise<Object>} Response data\r\n */\r\nexport async function markAllAsRead() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/mark-all-as-read`, {\r\n      method: \"PUT\", // Changed from POST to PUT as per API doc\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"markAllAsRead\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi đánh dấu tất cả thông báo đã đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get latest notifications (for navbar dropdown)\r\n * @param {number} limit - Number of notifications to get\r\n * @returns {Promise<Object>} Response with notifications data\r\n */\r\nexport async function getLatestNotifications(limit = 10) {\r\n  try {\r\n    // Using the default endpoint with a small page size for latest notifications\r\n    return await fetchWithAuth(`${API_BASE_URL}?page=1&pageSize=${limit}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getLatestNotifications\",\r\n      limit,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông báo mới nhất\");\r\n  }\r\n} "], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;;;;;;AAEA,MAAM,eAAe,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC;AAUxD,eAAe,iBAAiB,SAAS,CAAC,CAAC;IAChD,IAAI;QACF,MAAM,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,GAAG;QAEvC,iDAAiD;QACjD,IAAI,MAAM,OACN,GAAG,aAAa,SAAS,EAAE,KAAK,MAAM,EAAE,KAAK,UAAU,EAAE,OAAO,GAChE,GAAG,aAAa,MAAM,EAAE,KAAK,UAAU,EAAE,OAAO;QAEpD,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO;YACrC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAMO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,aAAa,CAAC,EAAE;YACzD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO;YACrC,QAAQ;QACV;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAQO,eAAe,WAAW,MAAM;IACrC,IAAI;QACF,8EAA8E;QAC9E,IAAI,OAAO,GAAG,IAAI,OAAO,GAAG,CAAC,MAAM,GAAG,GAAG;YACvC,2CAA2C;YAC3C,MAAM,KAAK,OAAO,GAAG,CAAC,EAAE;YACxB,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO;YACrC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAMO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,iBAAiB,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO;YACrC,QAAQ;QACV;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAOO,eAAe,uBAAuB,QAAQ,EAAE;IACrD,IAAI;QACF,6EAA6E;QAC7E,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,iBAAiB,EAAE,OAAO,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO;YACrC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;;;IAlHsB;IA4BA;IAsBA;IA2BA;IAqBA;;AAlGA,+OAAA;AA4BA,+OAAA;AAsBA,+OAAA;AA2BA,+OAAA;AAqBA,+OAAA", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function parseEmptyStringsToNull(payload) {\r\n  if (Array.isArray(payload)) {\r\n    return payload.map(item => parseEmptyStringsToNull(item));\r\n  }\r\n\r\n  if (typeof payload === 'object' && payload !== null) {\r\n    const newPayload = { ...payload };\r\n\r\n    Object.keys(newPayload).forEach(key => {\r\n      if (newPayload[key] === '') {\r\n        newPayload[key] = null;\r\n      } else if (typeof newPayload[key] === 'object' && newPayload[key] !== null) {\r\n        newPayload[key] = parseEmptyStringsToNull(newPayload[key]);\r\n      }\r\n    });\r\n\r\n    return newPayload;\r\n  }\r\n\r\n  return payload;\r\n}\r\n\r\nexport function formatCurrency(amount) {\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    style: 'currency',\r\n    currency: 'VND',\r\n    maximumFractionDigits: 0\r\n  }).format(amount);\r\n}\r\n\r\nexport function formatDate(dateString) {\r\n  return new Date(dateString).toLocaleDateString('vi-VN', {\r\n    day: '2-digit',\r\n    month: '2-digit',\r\n    year: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  });\r\n}\r\n\r\nexport const formatPriceShort = (price) => {\r\n  if (price === null || price === undefined) return 'N/A';\r\n  if (price >= 1000000000) {\r\n      // Làm tròn 1 chữ số thập phân, loại bỏ .0 nếu là số nguyên tỷ\r\n      const val = (price / 1000000000).toFixed(1);\r\n      return val.endsWith('.0') ? val.slice(0, -2) + ' Tỷ' : val + ' Tỷ';\r\n  }\r\n  if (price >= 1000000) {\r\n      // Làm tròn 1 chữ số thập phân, loại bỏ .0 nếu là số nguyên triệu\r\n       const val = (price / 1000000).toFixed(1);\r\n      return val.endsWith('.0') ? val.slice(0, -2) + ' Tr' : val + ' Tr';\r\n  }\r\n   // Định dạng số thông thường cho các giá trị nhỏ hơn 1 triệu\r\n   if (typeof price === 'number') {\r\n       return price.toLocaleString('vi-VN');\r\n   }\r\n  return String(price); // Trường hợp khác cố gắng convert sang string\r\n};\r\n\r\nexport function debounce(func, delay) {\r\n  let timeoutId;\r\n  // Hàm debounce trả về một hàm mới\r\n  const debounced = function(...args) {\r\n    const context = this; // Lưu ngữ cảnh 'this'\r\n    clearTimeout(timeoutId); // Xóa timer cũ nếu có\r\n    // Thiết lập timer mới để gọi hàm gốc sau độ trễ\r\n    timeoutId = setTimeout(() => {\r\n      func.apply(context, args); // Gọi hàm gốc với ngữ cảnh và đối số đúng\r\n    }, delay);\r\n  };\r\n\r\n  // Thêm phương thức cancel vào hàm debounced trả về\r\n  debounced.cancel = function() {\r\n    clearTimeout(timeoutId);\r\n  };\r\n\r\n  return debounced; // Trả về hàm đã được debounce\r\n}\r\n\r\nexport function formatStatusText(status) {\r\n  return status\r\n    .replace(/_/g, \" \")\r\n    .toLowerCase()\r\n    .replace(/^\\w/, (c) => c.toUpperCase());\r\n}\r\n\r\n/**\r\n * Format notification time relative to current time\r\n * @param {string} dateString - ISO date string to format\r\n * @param {Function} t - Translation function from useTranslations hook\r\n * @param {string} locale - Current locale (e.g., 'en', 'vi')\r\n * @returns {string} Formatted time string (e.g., \"5 minutes ago\", \"2 hours ago\")\r\n */\r\nexport const formatNotificationTime = (dateString, t, locale) => {\r\n  if (!dateString) return \"\";\r\n  try {\r\n    const date = new Date(dateString);\r\n    const now = new Date();\r\n    const diffMs = now - date;\r\n    const diffSeconds = Math.floor(diffMs / 1000);\r\n    const diffMinutes = Math.floor(diffSeconds / 60);\r\n    const diffHours = Math.floor(diffMinutes / 60);\r\n    const diffDays = Math.floor(diffHours / 24);\r\n\r\n    if (diffMinutes < 60) {\r\n      return t(\"notificationTimeMinutes\", { count: diffMinutes });\r\n    } else if (diffHours < 24) {\r\n      return t(\"notificationTimeHours\", { count: diffHours });\r\n    } else if (diffDays < 7) {\r\n      return t(\"notificationTimeDays\", { count: diffDays });\r\n    } else {\r\n      return date.toLocaleDateString(locale, { day: \"2-digit\", month: \"2-digit\", year: \"numeric\" });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error formatting notification time:\", error);\r\n    return \"\";\r\n  }\r\n};"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,wBAAwB,OAAO;IAC7C,IAAI,MAAM,OAAO,CAAC,UAAU;QAC1B,OAAO,QAAQ,GAAG,CAAC,CAAA,OAAQ,wBAAwB;IACrD;IAEA,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,MAAM,aAAa;YAAE,GAAG,OAAO;QAAC;QAEhC,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC9B,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI;gBAC1B,UAAU,CAAC,IAAI,GAAG;YACpB,OAAO,IAAI,OAAO,UAAU,CAAC,IAAI,KAAK,YAAY,UAAU,CAAC,IAAI,KAAK,MAAM;gBAC1E,UAAU,CAAC,IAAI,GAAG,wBAAwB,UAAU,CAAC,IAAI;YAC3D;QACF;QAEA,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAAS,eAAe,MAAM;IACnC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,UAAU;IACnC,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,IAAI,SAAS,YAAY;QACrB,8DAA8D;QAC9D,MAAM,MAAM,CAAC,QAAQ,UAAU,EAAE,OAAO,CAAC;QACzC,OAAO,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ,MAAM;IACjE;IACA,IAAI,SAAS,SAAS;QAClB,iEAAiE;QAChE,MAAM,MAAM,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC;QACvC,OAAO,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ,MAAM;IACjE;IACC,4DAA4D;IAC5D,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO,MAAM,cAAc,CAAC;IAChC;IACD,OAAO,OAAO,QAAQ,8CAA8C;AACtE;AAEO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,kCAAkC;IAClC,MAAM,YAAY,SAAS,GAAG,IAAI;QAChC,MAAM,UAAU,IAAI,EAAE,sBAAsB;QAC5C,aAAa,YAAY,sBAAsB;QAC/C,gDAAgD;QAChD,YAAY,WAAW;YACrB,KAAK,KAAK,CAAC,SAAS,OAAO,0CAA0C;QACvE,GAAG;IACL;IAEA,mDAAmD;IACnD,UAAU,MAAM,GAAG;QACjB,aAAa;IACf;IAEA,OAAO,WAAW,8BAA8B;AAClD;AAEO,SAAS,iBAAiB,MAAM;IACrC,OAAO,OACJ,OAAO,CAAC,MAAM,KACd,WAAW,GACX,OAAO,CAAC,OAAO,CAAC,IAAM,EAAE,WAAW;AACxC;AASO,MAAM,yBAAyB,CAAC,YAAY,GAAG;IACpD,IAAI,CAAC,YAAY,OAAO;IACxB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM;QACrB,MAAM,cAAc,KAAK,KAAK,CAAC,SAAS;QACxC,MAAM,cAAc,KAAK,KAAK,CAAC,cAAc;QAC7C,MAAM,YAAY,KAAK,KAAK,CAAC,cAAc;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;QAExC,IAAI,cAAc,IAAI;YACpB,OAAO,EAAE,2BAA2B;gBAAE,OAAO;YAAY;QAC3D,OAAO,IAAI,YAAY,IAAI;YACzB,OAAO,EAAE,yBAAyB;gBAAE,OAAO;YAAU;QACvD,OAAO,IAAI,WAAW,GAAG;YACvB,OAAO,EAAE,wBAAwB;gBAAE,OAAO;YAAS;QACrD,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,QAAQ;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAU;QAC7F;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;;;;;;;AAEA,MAAM,eAAe,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;AAC1D,MAAM,kCAAkC,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,sBAAsB,CAAC;AAG/E,eAAe,iBAAiB,cAAc;IACnD,IAAI;QACF,8CAA8C;QAC9C,MAAM,cAAc,IAAI;QAExB,8BAA8B;QAC9B,IAAI,eAAe,eAAe,IAAI,eAAe,eAAe,CAAC,MAAM,GAAG,GAAG;YAC/E,eAAe,eAAe,CAAC,OAAO,CAAC,CAAA;gBACrC,YAAY,MAAM,CAAC,YAAY;YACjC;QACF;QAEA,gBAAgB;QAChB,IAAI,eAAe,YAAY,IAAI,eAAe,YAAY,CAAC,MAAM,GAAG,GAAG;YACzE,eAAe,YAAY,CAAC,OAAO,CAAC,CAAA;gBAClC,YAAY,MAAM,CAAC,gBAAgB;YACrC;QACF;QAEA,WAAW;QACX,IAAI,eAAe,QAAQ,EAAE;YAC3B,IAAI,eAAe,QAAQ,CAAC,QAAQ,EAAE;gBACpC,YAAY,MAAM,CAAC,UAAU,eAAe,QAAQ,CAAC,QAAQ;YAC/D;YACA,IAAI,eAAe,QAAQ,CAAC,QAAQ,EAAE;gBACpC,YAAY,MAAM,CAAC,cAAc,eAAe,QAAQ,CAAC,QAAQ;YACnE;YACA,IAAI,eAAe,QAAQ,CAAC,OAAO,EAAE;gBACnC,YAAY,MAAM,CAAC,WAAW,eAAe,QAAQ,CAAC,OAAO;YAC/D;QACF;QAEA,cAAc;QACd,IAAI,eAAe,UAAU,EAAE;YAC7B,IAAI,eAAe,UAAU,CAAC,GAAG,EAAE;gBACjC,YAAY,MAAM,CAAC,YAAY,eAAe,UAAU,CAAC,GAAG;YAC9D;YACA,IAAI,eAAe,UAAU,CAAC,GAAG,EAAE;gBACjC,YAAY,MAAM,CAAC,YAAY,eAAe,UAAU,CAAC,GAAG;YAC9D;QACF;QAEA,aAAa;QACb,IAAI,eAAe,SAAS,EAAE;YAC5B,IAAI,eAAe,SAAS,CAAC,GAAG,EAAE;gBAChC,YAAY,MAAM,CAAC,WAAW,eAAe,SAAS,CAAC,GAAG;YAC5D;YACA,IAAI,eAAe,SAAS,CAAC,GAAG,EAAE;gBAChC,YAAY,MAAM,CAAC,WAAW,eAAe,SAAS,CAAC,GAAG;YAC5D;QACF;QAEA,WAAW;QACX,IAAI,eAAe,QAAQ,EAAE;YAC3B,YAAY,MAAM,CAAC,YAAY,eAAe,QAAQ;QACxD;QAEA,YAAY;QACZ,IAAI,eAAe,SAAS,EAAE;YAC5B,YAAY,MAAM,CAAC,cAAc,eAAe,SAAS;QAC3D;QAEA,YAAY;QACZ,IAAI,eAAe,SAAS,EAAE;YAC5B,YAAY,MAAM,CAAC,aAAa,eAAe,SAAS;QAC1D;QAEA,eAAe;QACf,IAAI,eAAe,WAAW,EAAE;YAC9B,YAAY,MAAM,CAAC,YAAY,eAAe,WAAW;QAC3D;QAEA,aAAa;QACb,IAAI,eAAe,SAAS,EAAE;YAC5B,YAAY,MAAM,CAAC,gBAAgB,eAAe,SAAS;QAC7D;QAEA,qCAAqC;QACrC,IAAI,eAAe,MAAM,IAAI,eAAe,MAAM,IAAI,eAAe,MAAM,IAAI,eAAe,MAAM,EAAE;YACpG,YAAY,MAAM,CAAC,SAAS,eAAe,MAAM;YACjD,YAAY,MAAM,CAAC,SAAS,eAAe,MAAM;YACjD,YAAY,MAAM,CAAC,SAAS,eAAe,MAAM;YACjD,YAAY,MAAM,CAAC,SAAS,eAAe,MAAM;QAEnD;QAEA,sCAAsC;QACtC,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,YAAY,QAAQ,IAAI;QAE9D,2CAA2C;QAC3C,MAAM,WAAW,MAAM,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;YAC3C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,UAAU,gEAAgE;IAEnF,EAAE,OAAO,OAAO;QACd,yCAAyC;QACzC,QAAQ,KAAK,CAAC,CAAC,kDAAkD,CAAC,EAAE;QACpE,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAEO,eAAe,gBAAgB,UAAU;IAC9C,MAAM,MAAM,GAAG,aAAa,CAAC,EAAE,YAAY;IAC3C,IAAI;QACF,2CAA2C;QAC3C,MAAM,WAAW,MAAM,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;YAC3C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,UAAU,6FAA6F;IAEhH,EAAE,OAAO,OAAO;QACd,0FAA0F;QAC1F,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,CAAC,EAAE;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAEO,eAAe,mBAAmB,SAAS,EAAE,QAAQ;IAC1D,IAAI;QACF,0BAA0B;QAC1B,MAAM,aAAa,SAAS,GAAG,CAAC;QAEhC,yDAAyD;QACzD,MAAM,iBAAiB,OAAO,WAAW,CAAC,SAAS,OAAO;QAC1D,MAAM,UAAU;YACd,GAAG,cAAc;QACnB;QAEA,MAAM,cAAc,MAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE;QACrC,IAAI,aAAa;YACf,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,QAAQ,OAAO,GAAG,KAAK,EAAE;QAC3B;QAEA,2DAA2D;QAC3D,IAAI,eAAe,aAAa,EAAE;YAChC,QAAQ,aAAa,GAAG,KAAK,KAAK,CAAC,eAAe,aAAa;QACjE;QAEA,QAAQ,aAAa,GAAG,eAAe,aAAa,KAAK,SAAS,OAAO;QACzE,QAAQ,WAAW,GAAG,eAAe,WAAW,KAAK,SAAS,OAAO;QAErE,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,CAAC,EAAE,YAAY,EAAE;YAC1D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE;YAC7C,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB,OAAO;YACjC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAEO,eAAe,mBAAmB,UAAU;IACjD,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,CAAC,EAAE,YAAY,EAAE;YAC1D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAEO,eAAe;IACpB,IAAI;QACF,2CAA2C;QAC3C,MAAM,WAAW,MAAM,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,cAAc;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,UAAU,gEAAgE;IAEnF,EAAE,OAAO,OAAO;QACd,yCAAyC;QACzC,QAAQ,KAAK,CAAC,CAAC,kDAAkD,CAAC,EAAE;QACpE,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAEO,eAAe,eAAe,SAAS,EAAE,QAAQ;IACtD,IAAI;QACF,yDAAyD;QACzD,MAAM,iBAAiB,OAAO,WAAW,CAAC,SAAS,OAAO;QAE1D,MAAM,UAAU;YACd,GAAG,cAAc;QACnB;QAEA,MAAM,cAAc,MAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE;QACrC,IAAI,aAAa;YACf,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,QAAQ,OAAO,GAAG,KAAK,EAAE;QAC3B;QAEA,2DAA2D;QAC3D,IAAI,eAAe,aAAa,EAAE;YAChC,QAAQ,aAAa,GAAG,KAAK,KAAK,CAAC,eAAe,aAAa;QACjE;QAEA,QAAQ,aAAa,GAAG,eAAe,aAAa,KAAK,SAAS,OAAO;QACzE,QAAQ,WAAW,GAAG,eAAe,WAAW,KAAK,SAAS,OAAO;QAErE,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,cAAc;YACvC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE;YAC7C,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB,OAAO;YACjC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAEO,eAAe,qBAAqB,QAAQ;IACjD,IAAI;QACF,MAAM,aAAa,SAAS,GAAG,CAAC;QAEhC,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,CAAC,EAAE,WAAW,OAAO,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,QAAQ,SAAS,GAAG,CAAC;gBACrB,SAAS,SAAS,GAAG,CAAC,cAAc;YACtC;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB,OAAO;YACjC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAEO,eAAe,kBAAkB,MAAM,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE;IACrE,IAAI;QACF,yDAAyD;QACzD,IAAI,WAAW,UAAU;YACvB,OAAO,MAAM;QACf;QAEA,IAAI,MAAM,GAAG,aAAa,GAAG,CAAC;QAC9B,MAAM,cAAc,IAAI;QAExB,6BAA6B;QAC7B,IAAI,QAAQ;YACV,YAAY,MAAM,CAAC,UAAU;QAC/B;QAEA,YAAY,MAAM,CAAC,QAAQ;QAC3B,YAAY,MAAM,CAAC,YAAY;QAE/B,8CAA8C;QAC9C,IAAI,YAAY,QAAQ,IAAI;YAC1B,OAAO,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI;QACrC;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;YACxC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sDAAsD;QACpE,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,MAAM,CAAC,EAAE;YAC5D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,SAAS,OAAO,EAAE;YACpB,iEAAiE;YACjE,MAAM,YAAY,SAAS,IAAI;YAC/B,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,OAAO,UAAU,eAAe,IAAI;oBACpC,UAAU,UAAU,kBAAkB,EAAE,YAAY;oBACpD,iBAAiB,UAAU,kBAAkB,EAAE,mBAAmB;oBAClE,iBAAiB,UAAU,kBAAkB,EAAE,mBAAmB;oBAClE,qBAAqB,UAAU,kBAAkB,EAAE,uBAAuB;oBAC1E,gBAAgB,UAAU,kBAAkB,EAAE,kBAAkB;oBAChE,SAAS,UAAU,kBAAkB,EAAE,WAAW;oBAClD,OAAO,UAAU,kBAAkB,EAAE,SAAS;oBAC9C,MAAM,UAAU,kBAAkB,EAAE,QAAQ;gBAC9C;YACF;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAEO,eAAe,qBAAqB,SAAS,EAAE,QAAQ;IAC5D,IAAI;QACF,MAAM,QAAQ,SAAS,MAAM,CAAC,UAAU,8BAA8B;QACtE,MAAM,aAAa,SAAS,GAAG,CAAC;QAEhC,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;QAC1C;QAEA,MAAM,iBAAiB,IAAI;QAC3B,MAAM,OAAO,CAAC,CAAC,OAAS,eAAe,MAAM,CAAC,SAAS;QACvD,eAAe,MAAM,CAAC,cAAc;QAEpC,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,cAAc,CAAC,EAAE;YAC1D,QAAQ;YACR,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB,OAAO;YAAE,QAAQ;YAAwB;QAAS;QAC9E,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAEO,eAAe,6BAA6B,UAAU;IAC3D,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,gBAAgB,EAAE,YAAY,EAAE;YACzE,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAEO,eAAe,yBAAyB,UAAU;IACvD,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,gCAAgC,gBAAgB,EAAE,YAAY,EAAE;YAC5F,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAEO,eAAe,oBAAoB,QAAQ,EAAE,SAAS,EAAE,SAAS,IAAI;IAC1E,IAAI;QACF,2CAA2C;QAC3C,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAC1B,GAAG,aAAa,iBAAiB,EAAE,SAAS,WAAW,EAAE,UAAU,QAAQ,EAAE,QAAQ,EAAE;YACrF,QAAQ;QACV;IAEJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YACH,SAAS;YACT,SAAS;YACT,WAAW;QACf;IACF;AACF;AAGO,eAAe,sBAAsB,UAAU;IAEpD,IAAI;QACF,gEAAgE;QAChE,MAAM,WAAW,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,gCAAgC,UAAU,EAAE,WAAW,QAAQ,CAAC;QAExG,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC,EAAE;YACzD,OAAO;gBACL,SAAS;gBACT,SAAS,SAAS,OAAO,IAAI;gBAC7B,WAAW,SAAS,SAAS,IAAI;YACnC;QACF;QAEA,8DAA8D;QAC9D,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI;QACrB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,CAAC,EAAE;QACrE,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAGO,eAAe,2BAA2B,OAAO,EAAE,OAAO;IAC/D,IAAI;QACF,MAAM,UAAU;YACd,IAAI;YACJ,SAAS;QACX;QAEA,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE;YACxE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;YACrB,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB,OAAO;YACjC,QAAQ;YACR;YACA;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAGO,eAAe,4BAA4B,OAAO,EAAE,QAAQ;IACjE,IAAI;QACF,MAAM,UAAU;YACd,IAAI;YACJ,UAAU;QACZ;QAEA,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,uBAAuB,CAAC,EAAE;YAC1E,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;YACrB,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB,OAAO;YACjC,QAAQ;YACR;YACA;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAGO,eAAe,oBAAoB,OAAO;IAC/C,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB,OAAO;YACjC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAOO,eAAe,qBAAqB,WAAW;IACpD,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,KAAK,CAAC,EAAE;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,aAAa;YACf;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AASO,eAAe,yBAAyB,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE;IAC9E,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,YAAY,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,aAAa;gBACb,QAAQ;gBACR,SAAS;YACX;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mDAAmD;QACjE,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAQO,eAAe,wBAAwB,UAAU,EAAE,aAAa;IACrE,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,eAAe;YACjB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yDAAyD;QACvE,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;AAQO,eAAe,4BAA4B,WAAW,EAAE,aAAa;IAC1E,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,eAAe,CAAC,EAAE;YAC3D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,aAAa;gBACb,eAAe;YACjB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+DAA+D;QAC7E,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW;QACb;IACF;AACF;;;IAnoBsB;IA8GA;IAwBA;IA0CA;IAkBA;IAuBA;IAuCA;IAuBA;IA6CA;IAuCA;IAuBA;IAeA;IAeA;IAmBA;IAgCA;IAyBA;IAyBA;IAsBA;IA4BA;IA6BA;IA2BA;;AA/mBA,+OAAA;AA8GA,+OAAA;AAwBA,+OAAA;AA0CA,+OAAA;AAkBA,+OAAA;AAuBA,+OAAA;AAuCA,+OAAA;AAuBA,+OAAA;AA6CA,+OAAA;AAuCA,+OAAA;AAuBA,+OAAA;AAeA,+OAAA;AAeA,+OAAA;AAmBA,+OAAA;AAgCA,+OAAA;AAyBA,+OAAA;AAyBA,+OAAA;AAsBA,+OAAA;AA4BA,+OAAA;AA6BA,+OAAA;AA2BA,+OAAA", "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/contactRequest.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, fetchWithoutAuth, getSession } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/ContactRequest`;\r\n\r\n/**\r\n * Get all contact requests for a specific property\r\n * @param {string} propertyId - The ID of the property\r\n */\r\nexport async function getContactRequestsByPropertyId(propertyId) {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/property/${propertyId}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n  } catch (error) {\r\n    logError(\"ContactRequestService\", error, {\r\n      action: \"getContactRequestsByPropertyId\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách yêu cầu liên hệ\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get a specific contact request by ID\r\n * @param {string} id - The ID of the contact request\r\n */\r\nexport async function getContactRequestById(id) {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/${id}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (!response.success) {\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Không thể lấy thông tin yêu cầu liên hệ\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n  } catch (error) {\r\n    logError(\"ContactRequestService\", error, {\r\n      action: \"getContactRequestById\",\r\n      id,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông tin yêu cầu liên hệ\");\r\n  }\r\n}\r\n\r\n/**\r\n * Create a new contact request\r\n * @param {Object} prevState - Previous state\r\n * @param {FormData} formData - Form data containing contact request details\r\n */\r\nexport async function createContactRequest(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    // Get the current user's ID if they're logged in\r\n    const userSession = await getSession(\"User\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.userId = user.id;\r\n    }\r\n\r\n    return await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"ContactRequestService\", error, {\r\n      action: \"createContactRequest\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo yêu cầu liên hệ\");\r\n  }\r\n}\r\n\r\n/**\r\n * Update a contact request\r\n * @param {Object} prevState - Previous state\r\n * @param {FormData} formData - Form data containing updated contact request details\r\n */\r\nexport async function updateContactRequest(prevState, formData) {\r\n  try {\r\n    const id = formData.get(\"id\");\r\n    if (!id) {\r\n      return handleErrorResponse(false, null, \"ID yêu cầu liên hệ không hợp lệ\");\r\n    }\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${id}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"ContactRequestService\", error, {\r\n      action: \"updateContactRequest\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật yêu cầu liên hệ\");\r\n  }\r\n}\r\n\r\n/**\r\n * Delete a contact request\r\n * @param {string} id - The ID of the contact request to delete\r\n */\r\nexport async function deleteContactRequest(id) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${id}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"ContactRequestService\", error, {\r\n      action: \"deleteContactRequest\",\r\n      id,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa yêu cầu liên hệ\");\r\n  }\r\n} "], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;;;;;;;AAEA,MAAM,eAAe,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC;AAMzD,eAAe,+BAA+B,UAAU;IAC7D,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,UAAU,EAAE,YAAY,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI;QACrB;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,yBAAyB,OAAO;YACvC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAMO,eAAe,sBAAsB,EAAE;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,CAAC,EAAE,IAAI,EAAE;YAC5D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,OAAO;gBACL,SAAS;gBACT,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI;QACrB;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,yBAAyB,OAAO;YACvC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAOO,eAAe,qBAAqB,SAAS,EAAE,QAAQ;IAC5D,IAAI;QACF,yDAAyD;QACzD,MAAM,iBAAiB,OAAO,WAAW,CAAC,SAAS,OAAO;QAE1D,MAAM,UAAU;YACd,GAAG,cAAc;QACnB;QAEA,iDAAiD;QACjD,MAAM,cAAc,MAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE;QACrC,IAAI,aAAa;YACf,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,QAAQ,MAAM,GAAG,KAAK,EAAE;QAC1B;QAEA,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,cAAc;YAC1C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE;YAC7C,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,yBAAyB,OAAO;YACvC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAOO,eAAe,qBAAqB,SAAS,EAAE,QAAQ;IAC5D,IAAI;QACF,MAAM,KAAK,SAAS,GAAG,CAAC;QACxB,IAAI,CAAC,IAAI;YACP,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;QAC1C;QAEA,yDAAyD;QACzD,MAAM,iBAAiB,OAAO,WAAW,CAAC,SAAS,OAAO;QAE1D,MAAM,UAAU;YACd,GAAG,cAAc;QACnB;QAEA,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,CAAC,EAAE,IAAI,EAAE;YAClD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE;YAC7C,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,yBAAyB,OAAO;YACvC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAMO,eAAe,qBAAqB,EAAE;IAC3C,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,CAAC,EAAE,IAAI,EAAE;YAClD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,yBAAyB,OAAO;YACvC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;;;IAlJsB;IA0BA;IAkCA;IAqCA;IAkCA;;AAnIA,+OAAA;AA0BA,+OAAA;AAkCA,+OAAA;AAqCA,+OAAA;AAkCA,+OAAA", "debugId": null}}, {"offset": {"line": 1214, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/.next-internal/server/app/%5Blocale%5D/%28protected%29/user/bds/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {validateTokenServer as '001de94ec731220815d4fe6ce2d548b202bd052ff3'} from 'ACTIONS_MODULE0'\nexport {validateTokenDirectlyFromAPIServer as '0034f2076260b358ea3dfc1c99fa419e3287163fe8'} from 'ACTIONS_MODULE0'\nexport {logout as '007be0fe05adcc7d0c069ca539eb0ff1cb6fd0e443'} from 'ACTIONS_MODULE0'\nexport {getUserProfile as '00e7e2892a7c5df6d1d5fa13dd13a750332361b7bf'} from 'ACTIONS_MODULE0'\nexport {forgotPassword as '605ee68581d93fd51fe0565806b8059b6a037fc225'} from 'ACTIONS_MODULE0'\nexport {registerUser as '6074658acb00601d2549775ad0d80ebfad3207beb6'} from 'ACTIONS_MODULE0'\nexport {loginUser as '6095e1a16a36fae9f991406ee5d3ae93ce05419f13'} from 'ACTIONS_MODULE0'\nexport {changePassword as '60ddee3a1e9f4d6efc9c1cece9c322d5fbc2422f7f'} from 'ACTIONS_MODULE0'\nexport {getJwtInfo as '008dfdacd08dee8b2631add445c74492baff98a2ad'} from 'ACTIONS_MODULE1'\nexport {clearSessionAndBackToLogin as '00e6dbd535aa6e9ad1aa89e2cac9bd8d5cc801465a'} from 'ACTIONS_MODULE1'\nexport {getSession as '4001fad38119db8542322dccd0617b3df1d830a26c'} from 'ACTIONS_MODULE1'\nexport {deleteSession as '403e60a2cf4748152b9343ec01a868c4669796cd15'} from 'ACTIONS_MODULE1'\nexport {verifyJwtToken as '4096ae64ac4ea3209d6dc5820144fc5deef2f95a15'} from 'ACTIONS_MODULE1'\nexport {fetchWithAuth as '60a89ef542525d5dfde77653987c6ed3b387c5216e'} from 'ACTIONS_MODULE1'\nexport {fetchWithoutAuth as '60f988a13a61f71753d0e8e0e1219596262b22d654'} from 'ACTIONS_MODULE1'\nexport {createSession as '70c1d52c2370d1547b5942fa95004975d259c404e8'} from 'ACTIONS_MODULE1'\nexport {getFavoritesCount as '004337d8d5eb6ed7e2919a0aeefac685ea3d2d1941'} from 'ACTIONS_MODULE2'\nexport {getLatestNotifications as '40208af54e01b051461b63d477eaaaa55f04d9b278'} from 'ACTIONS_MODULE3'\nexport {getUnreadCount as '00bbe381627ea72a4cce4f9c30bb837f34cc1bd027'} from 'ACTIONS_MODULE3'\nexport {markAsRead as '40ae052a3c4bb7eb61dbfdc1c4f786ce752c93bed7'} from 'ACTIONS_MODULE3'\nexport {getPropertyStats as '00119bbc209304daaae56f240d2039a6a43dcb0320'} from 'ACTIONS_MODULE4'\nexport {getAllProperties as '00988f8ed4a3cfa99fb8476b3194891831467e44d7'} from 'ACTIONS_MODULE4'\nexport {deletePropertyById as '401a97280d5ce9565943fdcbe639ca98ed3df6816d'} from 'ACTIONS_MODULE4'\nexport {deletePropertyMedia as '4028a95d00369331b3bf78498e966fc69039c396d5'} from 'ACTIONS_MODULE4'\nexport {updatePropertyStatus as '40349d8aaa75adc55fddf542d24addefd0ff4b906e'} from 'ACTIONS_MODULE4'\nexport {bulkDeleteProperties as '409ad784b92a1a313639c04f81ca2c94d075b8cf47'} from 'ACTIONS_MODULE4'\nexport {searchProperties as '40d4df3ad783630ce7b196706828eecdfd7c2d2e76'} from 'ACTIONS_MODULE4'\nexport {getPropertyById as '40dc8e5d0cd6942972a005ff59fb5313d50c976a55'} from 'ACTIONS_MODULE4'\nexport {verifyPropertyRemainingTimes as '40e516e88dde32b6d807dd4efdd3a65accb46d6fe9'} from 'ACTIONS_MODULE4'\nexport {getPropertyReportById as '40f96f59a10ba7f88d0643fabe18bd637d34fed74a'} from 'ACTIONS_MODULE4'\nexport {getPropertyStatusHistory as '40fccd474b4ea91169f388ffb2b5596f86061fa12c'} from 'ACTIONS_MODULE4'\nexport {uploadPropertyImages as '60132b3bf5fb49b2ec0328a6e8371a7e0f39f45431'} from 'ACTIONS_MODULE4'\nexport {updatePropertyMediaCaption as '601a34545aea8e8cc50a9d61816b2f06952d565a6a'} from 'ACTIONS_MODULE4'\nexport {updatePropertyMediaIsAvatar as '602db38a384954084ad869270e409d791ecc061b65'} from 'ACTIONS_MODULE4'\nexport {updatePropertyById as '6052a8d78b815c6399a4eeca17f65535c07e14995d'} from 'ACTIONS_MODULE4'\nexport {bulkUpdatePropertyHighlight as '605e9ce34b71779c72cc0fd79fadaa516765e289db'} from 'ACTIONS_MODULE4'\nexport {updatePropertyHighlight as '60784b5203110f1c09e12e8495bffd4450889b78b9'} from 'ACTIONS_MODULE4'\nexport {createProperty as '60eb601005c54d544b3062e77d4c5ae627e94fd88b'} from 'ACTIONS_MODULE4'\nexport {bulkUpdatePropertyStatus as '701b1d6cac263a13e24049630543eb96880c1a9529'} from 'ACTIONS_MODULE4'\nexport {getPropertyByUser as '7085800a14a02e3d0acf1cb90a916071c6624bb6c0'} from 'ACTIONS_MODULE4'\nexport {getNearbyProperties as '70b1560c1d490fc56c914b2936e74a400a310408cd'} from 'ACTIONS_MODULE4'\nexport {getContactRequestsByPropertyId as '4099f65b2b512be1808e42a90b9b0ad7653979d131'} from 'ACTIONS_MODULE5'\nexport {updateContactRequest as '603b460c5981caea9e608522859d11fabc6fe90e56'} from 'ACTIONS_MODULE5'\n"], "names": [], "mappings": ";AAAA;AAQA;AAQA;AACA;AAGA;AAqBA", "debugId": null}}, {"offset": {"line": 1437, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/button.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot as SlotPrimitive } from \"radix-ui\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n        solid: \"text-primary border-b-primary border-b-2\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n      rounded: {\r\n        none: \"rounded-none\",\r\n        default: \"rounded-md\",\r\n        full: \"rounded-full\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n      rounded: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Button = React.forwardRef(({ className, variant, size, rounded, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? SlotPrimitive.Slot : \"button\"\r\n  return (\r\n    <Comp\r\n      className={cn(buttonVariants({ variant, size, rounded, className }))}\r\n      ref={ref}\r\n      {...props} />\r\n  );\r\n})\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0TACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,SAAS;IACX;AACF;AAGF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACjG,MAAM,OAAO,UAAU,gMAAA,CAAA,OAAa,CAAC,IAAI,GAAG;IAC5C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAS;QAAU;QACjE,KAAK;QACJ,GAAG,KAAK;;;;;;AAEf;AACA,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1505, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/bds/PropertyList.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/[locale]/(protected)/user/bds/PropertyList.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsT,GACnV,oFACA", "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/bds/PropertyList.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/[locale]/(protected)/user/bds/PropertyList.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1543, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/alert.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg~*]:pl-7\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props} />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm \", className)}\r\n    {...props} />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,mJACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAChE,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAEb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAEb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/bds/page.jsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { <PERSON> } from \"@/i18n/navigation\";\r\nimport PropertyList from \"./PropertyList\";\r\nimport { getPropertyByUser } from \"@/app/actions/server/property\";\r\nimport { AlertCircleIcon } from \"lucide-react\";\r\nimport { getTranslations } from \"next-intl/server\";\r\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\r\n\r\nexport default async function PropertyListPage() {\r\n  const t = await getTranslations(\"UserPropertiesPage\");\r\n\r\n  // Fetch both initial data and filter counts in parallel for better performance\r\n  const [propertiesResult, filterCountsResult] = await Promise.all([\r\n    getPropertyByUser(null, 1, 10), // Get all properties by default (no status filter)\r\n    getPropertyByUser(\"counts\"), // Get filter counts\r\n  ]);\r\n\r\n  // Extract items array from the paginated response\r\n  const propertyData =\r\n    propertiesResult && propertiesResult?.success && propertiesResult?.data && propertiesResult?.data?.items ? propertiesResult?.data?.items : [];\r\n\r\n  // Extract filter counts data\r\n  const filterCountsData = filterCountsResult && filterCountsResult?.success && filterCountsResult?.data ? filterCountsResult?.data : null;\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white p-6\">\r\n      <div className=\"mb-8\">\r\n      <div className=\"mb-2 flex items-center justify-between\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-900\">{t(\"title\")}</h1>\r\n          <p className=\"mt-1 text-sm text-gray-500\">{t(\"description\")}</p>\r\n        </div>\r\n        <Button asChild className=\"gap-2 bg-teal-500 hover:bg-teal-600\">\r\n          <Link href=\"/user/bds/new\">{t(\"createButton\")}</Link>\r\n        </Button>\r\n      </div>\r\n      <Alert className=\"mb-3\">\r\n        <AlertCircleIcon className=\"h-4 w-4\" />\r\n        <AlertDescription className=\"mb-0\">{t(\"aleatAnalysis\")}</AlertDescription>\r\n      </Alert>\r\n      </div>\r\n      \r\n      \r\n      {/* Pass both initial data and filter counts from server to avoid duplicate API calls */}\r\n      <PropertyList initialData={propertyData} initialFilterCounts={filterCountsData} />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEe,eAAe;IAC5B,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,+EAA+E;IAC/E,MAAM,CAAC,kBAAkB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC/D,CAAA,GAAA,qIAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,GAAG;QAC3B,CAAA,GAAA,qIAAA,CAAA,oBAAiB,AAAD,EAAE;KACnB;IAED,kDAAkD;IAClD,MAAM,eACJ,oBAAoB,kBAAkB,WAAW,kBAAkB,QAAQ,kBAAkB,MAAM,QAAQ,kBAAkB,MAAM,QAAQ,EAAE;IAE/I,6BAA6B;IAC7B,MAAM,mBAAmB,sBAAsB,oBAAoB,WAAW,oBAAoB,OAAO,oBAAoB,OAAO;IAEpI,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC,EAAE;;;;;;kDACpD,8OAAC;wCAAE,WAAU;kDAA8B,EAAE;;;;;;;;;;;;0CAE/C,8OAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,WAAU;0CACxB,cAAA,8OAAC,kHAAA,CAAA,OAAI;oCAAC,MAAK;8CAAiB,EAAE;;;;;;;;;;;;;;;;;kCAGlC,8OAAC,0HAAA,CAAA,QAAK;wBAAC,WAAU;;0CACf,8OAAC,wNAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;0CAC3B,8OAAC,0HAAA,CAAA,mBAAgB;gCAAC,WAAU;0CAAQ,EAAE;;;;;;;;;;;;;;;;;;0BAMxC,8OAAC,kKAAA,CAAA,UAAY;gBAAC,aAAa;gBAAc,qBAAqB;;;;;;;;;;;;AAGpE", "debugId": null}}]}