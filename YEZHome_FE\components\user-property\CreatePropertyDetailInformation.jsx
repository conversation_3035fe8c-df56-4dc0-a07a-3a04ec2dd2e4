"use client";
import { useState, useEffect } from "react";
import { Switch } from "@/components/ui/switch";
import { CalendarIcon, Star, DollarSign, User, Timer } from "lucide-react";
import { format, addDays } from "date-fns";
import { vi } from "date-fns/locale";
import { Label } from "@/components/ui/label";
import { DEFAULT_POST_PRICE, MemberRank, PropertyStatus } from "@/lib/enum";
import BadgeStatus from "../layout/BadgeStatus";
import BadgeUserRank from "../layout/BadgeUserRank";
import { useTranslations } from "next-intl";
import { useAuth } from "@/contexts/AuthContext";
import { FormControl, FormField, FormItem, FormLabel } from "../ui/form";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

export default function CreatePropertyDetailInformation({
  form,
  property,
  isFormDisabled,
  basePostPrice = DEFAULT_POST_PRICE,
  onRankChange = () => {},
  onRefreshRef = null,
}) {
  const t = useTranslations("CreatePropertyDetailInformation");
  const tCommon = useTranslations("Common");
  const { profile } = useAuth();
  const displayDuration = 10; // days

  // Get the user's rank from profile context
  const userRankFromProfile = profile?.user?.memberRank || MemberRank.DEFAULT;

  // State to track the current rank (can be updated by BadgeUserRank component)
  const [currentRank, setCurrentRank] = useState(userRankFromProfile);

  const status = property?.status || PropertyStatus.DRAFT;
  const statusText = property?.status ? tCommon(`propertyStatus_${property?.status}`) : tCommon(`propertyStatus_${PropertyStatus.DRAFT}`);
  const createdAt = property?.createdAt || new Date();
  const expiresAt = property?.expiresAt;
  const highlight = form.watch("isHighlighted") || false;

  // Update currentRank when profile changes
  useEffect(() => {
    if (profile?.user?.memberRank) {
      setCurrentRank(profile?.user.memberRank);
    }
  }, [profile]);

  // Calculate highlight price using the utility function
  const highlightPrice = profile?.highlightFee?.fee || 0;
  const totalPrice = basePostPrice + (highlight ? highlightPrice : 0);

  const expirationDate = expiresAt || addDays(createdAt, displayDuration);

  return (
    <div className="space-y-3">
      {/* Customer Information Section */}
      <div className="space-y-4">
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-blue-600" />
            <h5 className="font-medium text-sm text-gray-900 mb-0">{t("individualCustomer")}</h5>
          </div>
          <BadgeUserRank
            showRefreshButton={true}
            onRankChange={(rankData) => {
              // Only update if the rank has actually changed
              if (rankData.currentRank !== currentRank) {
                setCurrentRank(rankData.currentRank);
                onRankChange(rankData);
              }
            }}
            onRefreshRef={onRefreshRef}
          />
        </div>
      </div>

      {/* Post Options Section */}
      {!isFormDisabled && (
        <div className="space-y-2">
          <FormField
            control={form.control}
            name="isHighlighted"
            render={({ field }) => (
              <FormItem className="bg-linear-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-2">
                <div className="flex items-center justify-between">
                  <Tooltip>
                    <TooltipTrigger>
                      <div className="flex items-center gap-2 space-y-1">
                        <Star className="h-4 w-4 text-yellow-600" />
                        <FormLabel className="font-medium text-sm text-gray-900">{t("highlightPost")}</FormLabel>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>{t("highlightPostDescription")}</TooltipContent>
                  </Tooltip>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} className="data-[state=checked]:bg-yellow-600" />
                  </FormControl>
                </div>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="isAutoRenew"
            render={({ field }) => (
              <FormItem className="bg-linear-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-2">
                <div className="flex items-center justify-between">
                  <Tooltip>
                    <TooltipTrigger>
                      <div className="flex items-center gap-2 space-y-1">
                        <Timer className="h-4 w-4 text-green-600" />
                        <FormLabel className="font-medium text-gray-900">{t("autoRenew")}</FormLabel>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>{t("autoRenewDescription")}</TooltipContent>
                  </Tooltip>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} className="data-[state=checked]:bg-green-600" />
                  </FormControl>
                </div>
              </FormItem>
            )}
          />
        </div>
      )}

      {/* Post Information Section */}
      <div className="rounded-lg p-4 space-y-4 bg-gray-50">
        <div className="grid grid-cols-1 gap-1">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-600 tracking-wide">{t("postStatus")}</Label>
            <BadgeStatus status={status} statusText={statusText} rounded="full" />
          </div>

          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-600 tracking-wide">{t("expirationTime")}</Label>
            <div className="flex items-center gap-1 text-sm font-medium text-gray-900">
              <Timer className="h-3 w-3 text-blue-600" />
              {displayDuration} {t("days")}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-600 tracking-wide">{t("postDate")}</Label>
            <div className="flex items-center gap-1 text-sm font-medium text-gray-900">
              <CalendarIcon className="h-3 w-3 text-blue-600" />
              {format(createdAt, "dd/MM/yyyy", { locale: vi })}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-gray-600 tracking-wide">{t("expirationDate")}</Label>
            <div className="flex items-center gap-1 text-sm font-medium text-gray-900">
              <CalendarIcon className="h-3 w-3 text-blue-600" />
              {format(expirationDate, "dd/MM/yyyy", { locale: vi })}
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div className="space-y-2">
        <Separator />
        <div className="bg-linear-to-r from-gray-50 to-gray-100 rounded-lg p-3 space-y-2">
          <div className="flex items-center gap-2 mb-3">
            <DollarSign className="h-4 w-4 text-teal-600" />
            <h4 className="font-medium text-gray-900 text-sm mb-0">{t("postFee")}</h4>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">{t("postFee")}</span>
              <span className="font-medium text-gray-900">{basePostPrice.toLocaleString("vi-VN")} đ</span>
            </div>

            {highlight && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{t("highlightFee")}</span>
                <span className="font-medium text-yellow-700">{highlightPrice.toLocaleString("vi-VN")} đ</span>
              </div>
            )}
          </div>
          <Separator />

          <div className="flex justify-between items-center">
            <span className="font-semibold text-gray-900">{t("total")}</span>
            <span className="font-bold text-lg text-green-700">{totalPrice.toLocaleString("vi-VN")} đ</span>
          </div>
        </div>
      </div>
    </div>
  );
}
