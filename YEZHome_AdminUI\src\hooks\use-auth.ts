import { useMutation } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import authService, { type LoginCredentials } from '@/services/auth-service';
import { useAuthStore } from '@/store/auth-store';

export function useAuth() {
  const navigate = useNavigate();
  const { setAuth, clearAuth, isAuthenticated, user } = useAuthStore();

  const loginMutation = useMutation({
    mutationFn: (credentials: LoginCredentials) => authService.login(credentials),
    onSuccess: (data) => {
      // Store auth data in Zustand store
      setAuth(data.accessToken, data.user);
      navigate({ to: '/' });
    },
  });

  const logout = () => {
    clearAuth();
    navigate({ to: '/login' });
  };

  return {
    login: loginMutation.mutate,
    logout,
    isLoading: loginMutation.isPending,
    isError: loginMutation.isError,
    error: loginMutation.error,
    isAuthenticated,
    user,
  };
} 