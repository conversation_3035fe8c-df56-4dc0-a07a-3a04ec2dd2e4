self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"001de94ec731220815d4fe6ce2d548b202bd052ff3\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"0034f2076260b358ea3dfc1c99fa419e3287163fe8\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"007be0fe05adcc7d0c069ca539eb0ff1cb6fd0e443\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\",\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"00e7e2892a7c5df6d1d5fa13dd13a750332361b7bf\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\",\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"605ee68581d93fd51fe0565806b8059b6a037fc225\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"6074658acb00601d2549775ad0d80ebfad3207beb6\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"6095e1a16a36fae9f991406ee5d3ae93ce05419f13\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"60ddee3a1e9f4d6efc9c1cece9c322d5fbc2422f7f\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"008dfdacd08dee8b2631add445c74492baff98a2ad\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"00e6dbd535aa6e9ad1aa89e2cac9bd8d5cc801465a\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"4001fad38119db8542322dccd0617b3df1d830a26c\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"403e60a2cf4748152b9343ec01a868c4669796cd15\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"4096ae64ac4ea3209d6dc5820144fc5deef2f95a15\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"60a89ef542525d5dfde77653987c6ed3b387c5216e\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"60f988a13a61f71753d0e8e0e1219596262b22d654\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"70c1d52c2370d1547b5942fa95004975d259c404e8\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/profile/page\": \"rsc\",\n        \"app/[locale]/page\": \"rsc\"\n      }\n    },\n    \"004337d8d5eb6ed7e2919a0aeefac685ea3d2d1941\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\",\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"40208af54e01b051461b63d477eaaaa55f04d9b278\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\",\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"00bbe381627ea72a4cce4f9c30bb837f34cc1bd027\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\",\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"40ae052a3c4bb7eb61dbfdc1c4f786ce752c93bed7\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/dang-nhap/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/dang-nhap/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/favorite/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\",\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"00119bbc209304daaae56f240d2039a6a43dcb0320\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\"\n      }\n    },\n    \"00988f8ed4a3cfa99fb8476b3194891831467e44d7\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\"\n      }\n    },\n    \"401a97280d5ce9565943fdcbe639ca98ed3df6816d\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"4028a95d00369331b3bf78498e966fc69039c396d5\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\"\n      }\n    },\n    \"40349d8aaa75adc55fddf542d24addefd0ff4b906e\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"409ad784b92a1a313639c04f81ca2c94d075b8cf47\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"40d4df3ad783630ce7b196706828eecdfd7c2d2e76\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"40dc8e5d0cd6942972a005ff59fb5313d50c976a55\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\"\n      }\n    },\n    \"40e516e88dde32b6d807dd4efdd3a65accb46d6fe9\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"40f96f59a10ba7f88d0643fabe18bd637d34fed74a\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"40fccd474b4ea91169f388ffb2b5596f86061fa12c\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"60132b3bf5fb49b2ec0328a6e8371a7e0f39f45431\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\"\n      }\n    },\n    \"601a34545aea8e8cc50a9d61816b2f06952d565a6a\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\"\n      }\n    },\n    \"602db38a384954084ad869270e409d791ecc061b65\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\"\n      }\n    },\n    \"6052a8d78b815c6399a4eeca17f65535c07e14995d\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\"\n      }\n    },\n    \"605e9ce34b71779c72cc0fd79fadaa516765e289db\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"60784b5203110f1c09e12e8495bffd4450889b78b9\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"60eb601005c54d544b3062e77d4c5ae627e94fd88b\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/new/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/new/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/new/page\": \"action-browser\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\"\n      }\n    },\n    \"701b1d6cac263a13e24049630543eb96880c1a9529\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\"\n      }\n    },\n    \"7085800a14a02e3d0acf1cb90a916071c6624bb6c0\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"70b1560c1d490fc56c914b2936e74a400a310408cd\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/[propertyId]/page\": \"rsc\",\n        \"app/[locale]/(protected)/user/bds/page\": \"rsc\",\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"4099f65b2b512be1808e42a90b9b0ad7653979d131\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"603b460c5981caea9e608522859d11fabc6fe90e56\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/bds/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/bds/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/bds/page\": \"action-browser\"\n      }\n    },\n    \"40b8bf52df741401c7c2f61d096cc243772a94f063\": {\n      \"workers\": {\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"40f2b1915562886d43a901d95ff8c50055a5956498\": {\n      \"workers\": {\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"403c24c3bd50d955eb88cdadad68d9b1fe0078c414\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/favorite/page\": \"action-browser\",\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"60807ffa3e0d325e4e19a925a0c7e573b08aace120\": {\n      \"workers\": {\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE5 => \\\"[project]/app/actions/server/contactRequest.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/page\": \"action-browser\"\n      }\n    },\n    \"4065d97c896d3241717fa03aae731abeb6073d8efd\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/favorite/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/favorite/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/favorite/page\": \"action-browser\"\n      }\n    },\n    \"00054d2f227f09c1dd2d48302e7e14203746f1fa42\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\"\n      }\n    },\n    \"4002184dc915ecfdc4fd6456ce3a151182954c685a\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\"\n      }\n    },\n    \"406e2dee543f8ab92aeaf4941de099fb18f3ca1252\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\"\n      }\n    },\n    \"404ef80fc13428a40ef4a9deeff8494440c3971506\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\"\n      }\n    },\n    \"409fe9fdd8c22bbbf2eeb887dfd676f8a32112bdb8\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\"\n      }\n    },\n    \"007345405aa6714ea0609142338a9a327befb7b272\": {\n      \"workers\": {\n        \"app/[locale]/(protected)/user/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(protected)/user/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/app/actions/server/user.jsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(protected)/user/profile/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"ibuBR8gd9czNjVKcVb89mS9lA91pSiY5k7/21W4re7I=\"\n}"