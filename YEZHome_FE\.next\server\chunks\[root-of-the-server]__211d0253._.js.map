{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/api/map/address-suggestions/route.jsx"], "sourcesContent": ["export async function GET(req) {\r\n  const { searchParams } = new URL(req.url);\r\n  const input = searchParams.get(\"input\");\r\n\r\n  if (!input) {\r\n    return Response.json({ error: \"Missing input query\" }, { status: 400 });\r\n  }\r\n\r\n  try {\r\n\r\n    const apiURl = `https://rsapi.goong.io/Place/AutoComplete?api_key=${process.env.GOONG_GEO_API_KEY}&input=${encodeURIComponent(input)}&limit=15&more_compound=true`;\r\n    const response = await fetch(\r\n      apiURl\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to fetch address suggestions\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    return Response.json(data, { status: 200 });\r\n  } catch (error) {\r\n    console.error(error);\r\n    return Response.json({ error: error.message }, { status: 500 });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,eAAe,IAAI,GAAG;IAC3B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG;IACxC,MAAM,QAAQ,aAAa,GAAG,CAAC;IAE/B,IAAI,CAAC,OAAO;QACV,OAAO,SAAS,IAAI,CAAC;YAAE,OAAO;QAAsB,GAAG;YAAE,QAAQ;QAAI;IACvE;IAEA,IAAI;QAEF,MAAM,SAAS,CAAC,kDAAkD,EAAE,QAAQ,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,mBAAmB,OAAO,4BAA4B,CAAC;QAClK,MAAM,WAAW,MAAM,MACrB;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,SAAS,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,OAAO,SAAS,IAAI,CAAC;YAAE,OAAO,MAAM,OAAO;QAAC,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF", "debugId": null}}]}