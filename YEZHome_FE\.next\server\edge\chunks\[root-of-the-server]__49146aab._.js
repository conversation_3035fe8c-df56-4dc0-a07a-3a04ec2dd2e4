(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__49146aab._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/i18n/routing.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "routing": (()=>routing)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$routing$2f$defineRouting$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__defineRouting$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/routing/defineRouting.js [middleware-edge] (ecmascript) <export default as defineRouting>");
;
const routing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$routing$2f$defineRouting$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__defineRouting$3e$__["defineRouting"])({
    // A list of all locales that are supported
    locales: [
        'en',
        'vi'
    ],
    // Used when no locale matches
    defaultLocale: 'vi'
});
}}),
"[project]/lib/sessionUtils.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearSessionAndBackToLogin": (()=>clearSessionAndBackToLogin),
    "createSession": (()=>createSession),
    "deleteSession": (()=>deleteSession),
    "fetchWithAuth": (()=>fetchWithAuth),
    "fetchWithoutAuth": (()=>fetchWithoutAuth),
    "getJwtInfo": (()=>getJwtInfo),
    "getSession": (()=>getSession),
    "verifyJwtToken": (()=>verifyJwtToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$headers$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/headers.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/request/cookies.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jwt$2d$decode$2f$build$2f$esm$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jwt-decode/build/esm/index.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/navigation.react-server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$client$2f$components$2f$redirect$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/client/components/redirect.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/webapi/jwt/verify.js [middleware-edge] (ecmascript)");
"use server";
;
;
;
;
const SECRET_KEY_STRING = process.env.JWT_SECRET;
const SECRET_KEY = new TextEncoder().encode(SECRET_KEY_STRING);
async function createSession(name, value, options) {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["cookies"])();
    const defaultOptions = {
        secure: true,
        httpOnly: true,
        expires: Date.now() + 24 * 60 * 60 * 1000,
        path: "/",
        sameSite: "strict"
    };
    cookieStore.set(name, value, {
        ...defaultOptions,
        ...options
    });
}
async function getSession(name) {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["cookies"])();
    return cookieStore.get(name)?.value;
}
async function deleteSession(name) {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.delete(name);
}
async function fetchWithAuth(url, options = {}) {
    try {
        const token = await getSession("Authorization"); // Lấy token từ session
        if (!token) {
            console.log("No token found");
            return {
                success: false,
                errorType: "no_token",
                message: "User is not logged in."
            };
        }
        console.log(`[${options.method}]: ${url} - Fetching with auth token: ${url} `);
        console.log(`BODY: ${options?.body}`);
        const response1 = await fetch(url, {
            ...options,
            headers: {
                Authorization: `Bearer ${token}`,
                ...options.headers
            },
            credentials: "include"
        });
        // Xử lý lỗi 401 (Unauthorized)
        if (response1.status === 401) {
            try {
                console.log(response1);
                // Xử lý lỗi token hết hạn nếu có 401 nhưng token vẫn có trong cooike thì xóa token => vì token đã hết hạn
                if (token) {
                    console.warn(`Token expired. Clearing session...${url}`);
                    return {
                        success: false,
                        errorType: "token_expired",
                        message: "Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại."
                    };
                }
            } catch (error) {
                console.warn("Lỗi khi xử lý phản hồi 401:", error);
            }
            return {
                success: false,
                errorType: "unauthorized",
                message: "401. Vui lòng đăng nhập."
            };
        }
        // Xử lý lỗi chung
        if (!response1.ok) {
            console.error(`API request failed: ${response1.status} ${response1.statusText}`);
            const errorData1 = await response1.json();
            console.error("Error data:", errorData1);
            let message = `Code: ${response1.status} - ${response1.statusText}: Đã xảy ra lỗi khi xử lý dữ liệu.`;
            if (errorData1?.detail || errorData1?.message) {
                message = errorData1?.detail || errorData1?.message;
            } else if (typeof errorData1 === "object") {
                for(const key in errorData1){
                    if (errorData1.hasOwnProperty(key)) {
                        message = errorData1[key];
                        break;
                    }
                }
            }
            return {
                success: false,
                errorType: errorData1 && errorData1?.errorType || "api_error",
                message: message
            };
        }
        console.log("response", response1);
        // Nếu gọi API thành công, trả về No Content 204 hoặc 201
        if (response1.status === 204 || response1.status === 201) {
            const data = await response1.json();
            console.log("data", data);
            return {
                success: true,
                data
            };
        } else {
            // Nếu gọi API thành công, trả về dữ liệu chuẩn
            const data = await response1.json();
            return {
                success: true,
                data
            };
        }
    } catch (error) {
        console.error("Fetch error:", error);
        return {
            success: false,
            errorType: "network_error",
            message: errorData && (errorData?.detail || errorData?.message || `Code: ${response?.status} - ${response?.statusText}: Failed to connect to the server. Please try again later.`)
        };
    }
}
async function fetchWithoutAuth(url, options = {}) {
    try {
        console.log(`[${options.method || "GET"}]: ${url} - Fetching without auth`);
        if (options?.body) {
            console.log(`BODY: ${options.body}`);
        }
        const response1 = await fetch(url, {
            ...options,
            headers: {
                ...options.headers || {}
            }
        });
        if (!response1.ok) {
            console.error(`Public API request failed: ${response1.status} ${response1.statusText}`);
            let errorData1;
            try {
                errorData1 = await response1.json();
            } catch (e) {
                errorData1 = {
                    message: response1.statusText
                };
            }
            return {
                success: false,
                errorType: errorData1 && errorData1?.errorType || "api_error",
                message: errorData1?.detail || errorData1?.message || `Code: ${response1.status} - ${response1.statusText}: Đã xảy ra lỗi khi xử lý dữ liệu.`
            };
        }
        if (response1.status === 204 || response1.status === 201) {
            return {
                success: true,
                data: null
            };
        } else {
            const data = await response1.json();
            return {
                success: true,
                data
            };
        }
    } catch (error) {
        console.error("Fetch error (without auth):", error);
        return {
            success: false,
            errorType: "network_error",
            message: `Failed to connect to the server. Please try again later. (${error.message || "Unknown network error"})`
        };
    }
}
async function clearSessionAndBackToLogin() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.delete("Authorization"); // Remove auth token
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$client$2f$components$2f$redirect$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["redirect"])("/dang-nhap"); // Redirect to login page
}
async function getJwtInfo() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["cookies"])();
    const decodedToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jwt$2d$decode$2f$build$2f$esm$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["jwtDecode"])(cookieStore.get("Authorization")?.value);
    return decodedToken;
}
async function verifyJwtToken(token) {
    try {
        const { payload } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["jwtVerify"])(token, SECRET_KEY);
        return payload;
    } catch (error) {
        // Xử lý các lỗi cụ thể do 'jose' ném ra (TokenExpiredError, JWSInvalid...)
        // Các lỗi này là runtime error do bản thân token, không phải lỗi cấu hình
        // Log các lỗi này ở mức độ thấp hơn (warn, error)
        if (error.name === 'JOSEError' && error.message === 'signature verification failed') {
            console.warn('JWT verification failed: Invalid signature.');
        } else if (error.name === 'JWTExpired') {
            console.warn('JWT verification failed: Token has expired.');
        // Bạn có thể return một giá trị đặc biệt hoặc ném lỗi khác
        // nếu middleware cần phân biệt hết hạn và invalid signature
        } else {
            console.error('Unexpected JWT verification error:', error);
        }
        return null; // Trả về null nếu token không hợp lệ hoặc hết hạn
    }
}
}}),
"[project]/middleware.jsx [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "default": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/middleware/middleware.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$headers$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/headers.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/request/cookies.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/i18n/routing.ts [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$sessionUtils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/sessionUtils.js [middleware-edge] (ecmascript)");
"use server";
;
;
;
;
;
// 1. Specify protected and public routes (locale-agnostic)
const protectedRoutes = [
    "/user/profile",
    "/user/payments",
    "/user/notifications",
    "/user/bds",
    "/user/wallet",
    "/user/setting",
    "/user/dashboard",
    "/user/favorite",
    "/user/transactions",
    "/test-profile-context"
];
const publicRoutes = [
    "/dang-ki",
    "/dang-nhap",
    "/quen-mat-khau"
];
const handleI18nRouting = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"]);
// Regex for the dynamic EDIT route (locale-stripped)
const propertyRegex = /^\/user\/bds\/[a-zA-Z0-9-]+$/;
// Your authentication middleware logic
async function authMiddleware(request) {
    // pathname here might still have locale prefix when auth runs after intl
    const { pathname, origin } = request.nextUrl;
    // --- Robust locale detection from pathname ---
    let detectedLocale = __TURBOPACK__imported__module__$5b$project$5d2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"].defaultLocale; // Start with default
    let localeStrippedPathname = pathname;
    for (const locale of __TURBOPACK__imported__module__$5b$project$5d2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"].locales){
        if (pathname === `/${locale}` || pathname.startsWith(`/${locale}/`)) {
            detectedLocale = locale;
            if (pathname === `/${locale}`) {
                localeStrippedPathname = "/"; // Root path for the locale
            } else {
                localeStrippedPathname = pathname.substring(`/${locale}`.length) || "/"; // Path after locale prefix
            }
            break; // Found the locale
        }
    }
    // 2. Check if the *locale-stripped* route is protected or public
    const isProtectedRoute = protectedRoutes.includes(localeStrippedPathname) || propertyRegex.test(localeStrippedPathname);
    const isPublicRoute = publicRoutes.includes(localeStrippedPathname);
    // Check for cookie
    const token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$sessionUtils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["getSession"])("Authorization");
    let isAuthenticated = false;
    if (token) {
        const decodedPayload = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$sessionUtils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["verifyJwtToken"])(token);
        if (decodedPayload) {
            isAuthenticated = true;
        } else {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["cookies"])().delete("Authorization");
        }
    }
    if (isProtectedRoute && !isAuthenticated) {
        const locale = detectedLocale;
        const loginPath = `/${locale}/dang-nhap`.replace("//", "/");
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(loginPath, origin));
    }
    if (isAuthenticated && isPublicRoute && !localeStrippedPathname.startsWith("/user/profile")) {
        const locale = detectedLocale;
        const profilePath = `/${locale}/user/profile`.replace("//", "/");
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(profilePath, origin));
    }
    return null; // Indicate auth middleware allows the request
}
async function middleware(request) {
    const originalPathname = request.nextUrl.pathname;
    // 1. Run next-intl middleware first.
    // It handles localization, strips locale prefix from pathname for subsequent checks,
    // and might return a response (e.g., redirect for locale preference or cookie setting).
    const intlResponse = handleI18nRouting(request);
    // 2. Run authentication middleware *after* intl middleware.
    // Pass the request object potentially modified by intl middleware.
    const authResponse = await authMiddleware(request);
    // 3. Prioritize auth response (redirect to login if required).
    if (authResponse) {
        return authResponse;
    }
    // 4. If auth allowed, check if intl middleware had a response to return.
    if (intlResponse) {
        return intlResponse;
    }
    // 5. If neither middleware returned a response, proceed to the requested page
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next({
        request: {
            headers: new Headers(request.headers)
        }
    });
}
const config = {
    matcher: [
        "/((?!api|_next/static|_next/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*)"
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__49146aab._.js.map