{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/bds/new/components/SelectedLocationMap.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useRef, memo } from \"react\";\r\nimport goongjs from \"@goongmaps/goong-js\";\r\nimport \"@goongmaps/goong-js/dist/goong-js.css\";\r\nimport { HCM_COORDINATES_DISTRICT_2 } from \"@/lib/enum\";\r\n\r\nconst isBrowser = typeof window !== \"undefined\";\r\n\r\nconst SelectedLocationMap = memo(({ selectedLocation, isDisabled = false, onMarkerDragEnd }) => {\r\n  const mapContainerRef = useRef(null);\r\n  const mapRef = useRef(null);\r\n  const currentMarkerRef = useRef(null);\r\n\r\n  const center = selectedLocation || HCM_COORDINATES_DISTRICT_2;\r\n\r\n  // Effect 1: Initialize the map instance once\r\n  useEffect(() => {\r\n    if (!isBrowser || !mapContainerRef.current || mapRef.current) {\r\n      return;\r\n    }\r\n\r\n    goongjs.accessToken = `${process.env.NEXT_PUBLIC_GOONG_MAPTILES_KEY}`;\r\n\r\n    const goongMap = new goongjs.Map({\r\n      container: mapContainerRef.current,\r\n      style: \"https://tiles.goong.io/assets/goong_map_highlight.json\",\r\n      center: [center.longitude, center.latitude],\r\n      zoom: 14,\r\n      attributionControl: false,\r\n      interactive: !isDisabled,\r\n    });\r\n\r\n    mapRef.current = goongMap;\r\n\r\n    // Cleanup effect: Remove the map instance when component unmounts\r\n    return () => {\r\n      if (mapRef.current) {\r\n        if (currentMarkerRef.current) {\r\n          currentMarkerRef.current.remove();\r\n          currentMarkerRef.current = null;\r\n        }\r\n        mapRef.current.remove();\r\n        mapRef.current = null;\r\n      }\r\n    };\r\n  }, [isDisabled]);\r\n\r\n  // Effect 2: Update map based on selectedLocation prop and manage marker\r\n  useEffect(() => {\r\n    const map = mapRef.current;\r\n    if (!map) {\r\n      return;\r\n    }\r\n    // Remove old marker if exists\r\n    if (currentMarkerRef.current) {\r\n      currentMarkerRef.current.remove();\r\n      currentMarkerRef.current = null;\r\n    }\r\n\r\n    let markerLngLat = [center.longitude, center.latitude];\r\n    if (selectedLocation && selectedLocation?.latitude !== undefined && selectedLocation?.longitude !== undefined) {\r\n      markerLngLat = [selectedLocation.longitude, selectedLocation.latitude];\r\n    }\r\n\r\n    // Create new marker\r\n    const marker = new goongjs.Marker({\r\n      draggable: !isDisabled,\r\n      color: \"#de5c5c\",\r\n    })\r\n      .setLngLat(markerLngLat)\r\n      .addTo(map);\r\n\r\n    // Store new marker instance\r\n    currentMarkerRef.current = marker;\r\n\r\n    // Attach dragend event listener if not disabled\r\n    if (!isDisabled && onMarkerDragEnd) {\r\n      const handleDragEnd = (e) => {\r\n        const { lng, lat } = e.target.getLngLat();\r\n        onMarkerDragEnd({ lat, lng });\r\n      };\r\n      marker.on(\"dragend\", handleDragEnd);\r\n\r\n      // Store the listener in marker for cleanup\r\n      marker.dragEndListener = handleDragEnd;\r\n    }\r\n\r\n    // Fly to selected location\r\n    setTimeout(() => {\r\n      map.flyTo({\r\n        center: markerLngLat,\r\n        essential: true,\r\n      });\r\n    }, 10);\r\n\r\n    // Cleanup for this effect\r\n    return () => {\r\n      if (currentMarkerRef.current) {\r\n        // Remove dragend listener if it exists\r\n        if (currentMarkerRef.current.dragEndListener) {\r\n          currentMarkerRef.current.off(\"dragend\", currentMarkerRef.current.dragEndListener);\r\n        }\r\n        currentMarkerRef.current.remove();\r\n        currentMarkerRef.current = null;\r\n      }\r\n    };\r\n  }, [isDisabled, selectedLocation, onMarkerDragEnd]);\r\n\r\n  // Render the map container div\r\n  return (\r\n    <div\r\n      ref={mapContainerRef}\r\n      className={`w-full h-[400px] ${isDisabled ? \"opacity-75 cursor-not-allowed\" : \"\"}`}\r\n      style={{ position: \"relative\" }}\r\n    />\r\n  );\r\n});\r\n\r\nexport default SelectedLocationMap;"], "names": [], "mappings": ";;;AAsB6B;;AApB7B;AACA;AAEA;;;AALA;;;;;AAOA,MAAM,YAAY,aAAkB;AAEpC,MAAM,oCAAsB,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,UAAE,CAAC,EAAE,gBAAgB,EAAE,aAAa,KAAK,EAAE,eAAe,EAAE;;IACzF,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,MAAM,SAAS,oBAAoB,8GAAA,CAAA,6BAA0B;IAE7D,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,CAAC,aAAa,CAAC,gBAAgB,OAAO,IAAI,OAAO,OAAO,EAAE;gBAC5D;YACF;YAEA,oKAAA,CAAA,UAAO,CAAC,WAAW,GAAG,+EAA+C;YAErE,MAAM,WAAW,IAAI,oKAAA,CAAA,UAAO,CAAC,GAAG,CAAC;gBAC/B,WAAW,gBAAgB,OAAO;gBAClC,OAAO;gBACP,QAAQ;oBAAC,OAAO,SAAS;oBAAE,OAAO,QAAQ;iBAAC;gBAC3C,MAAM;gBACN,oBAAoB;gBACpB,aAAa,CAAC;YAChB;YAEA,OAAO,OAAO,GAAG;YAEjB,kEAAkE;YAClE;iDAAO;oBACL,IAAI,OAAO,OAAO,EAAE;wBAClB,IAAI,iBAAiB,OAAO,EAAE;4BAC5B,iBAAiB,OAAO,CAAC,MAAM;4BAC/B,iBAAiB,OAAO,GAAG;wBAC7B;wBACA,OAAO,OAAO,CAAC,MAAM;wBACrB,OAAO,OAAO,GAAG;oBACnB;gBACF;;QACF;wCAAG;QAAC;KAAW;IAEf,wEAAwE;IACxE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,MAAM,OAAO,OAAO;YAC1B,IAAI,CAAC,KAAK;gBACR;YACF;YACA,8BAA8B;YAC9B,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,iBAAiB,OAAO,CAAC,MAAM;gBAC/B,iBAAiB,OAAO,GAAG;YAC7B;YAEA,IAAI,eAAe;gBAAC,OAAO,SAAS;gBAAE,OAAO,QAAQ;aAAC;YACtD,IAAI,oBAAoB,kBAAkB,aAAa,aAAa,kBAAkB,cAAc,WAAW;gBAC7G,eAAe;oBAAC,iBAAiB,SAAS;oBAAE,iBAAiB,QAAQ;iBAAC;YACxE;YAEA,oBAAoB;YACpB,MAAM,SAAS,IAAI,oKAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gBAChC,WAAW,CAAC;gBACZ,OAAO;YACT,GACG,SAAS,CAAC,cACV,KAAK,CAAC;YAET,4BAA4B;YAC5B,iBAAiB,OAAO,GAAG;YAE3B,gDAAgD;YAChD,IAAI,CAAC,cAAc,iBAAiB;gBAClC,MAAM;mEAAgB,CAAC;wBACrB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,SAAS;wBACvC,gBAAgB;4BAAE;4BAAK;wBAAI;oBAC7B;;gBACA,OAAO,EAAE,CAAC,WAAW;gBAErB,2CAA2C;gBAC3C,OAAO,eAAe,GAAG;YAC3B;YAEA,2BAA2B;YAC3B;iDAAW;oBACT,IAAI,KAAK,CAAC;wBACR,QAAQ;wBACR,WAAW;oBACb;gBACF;gDAAG;YAEH,0BAA0B;YAC1B;iDAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,uCAAuC;wBACvC,IAAI,iBAAiB,OAAO,CAAC,eAAe,EAAE;4BAC5C,iBAAiB,OAAO,CAAC,GAAG,CAAC,WAAW,iBAAiB,OAAO,CAAC,eAAe;wBAClF;wBACA,iBAAiB,OAAO,CAAC,MAAM;wBAC/B,iBAAiB,OAAO,GAAG;oBAC7B;gBACF;;QACF;wCAAG;QAAC;QAAY;QAAkB;KAAgB;IAElD,+BAA+B;IAC/B,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,iBAAiB,EAAE,aAAa,kCAAkC,IAAI;QAClF,OAAO;YAAE,UAAU;QAAW;;;;;;AAGpC;;uCAEe", "debugId": null}}]}