import { useAuthStore } from '@/store/auth-store';

/**
 * Check if the user is authenticated
 * @returns true if the user is authenticated, false otherwise
 */
export function isAuthenticated(): boolean {
  return useAuthStore.getState().isAuthenticated;
}

/**
 * Redirect to login page if not authenticated
 * @returns true if the user is authenticated, false otherwise
 */
export function requireAuth(): boolean {
  const authenticated = isAuthenticated();
  
  if (!authenticated) {
    // Redirect will be handled by the route
    return false;
  }
  
  return true;
} 