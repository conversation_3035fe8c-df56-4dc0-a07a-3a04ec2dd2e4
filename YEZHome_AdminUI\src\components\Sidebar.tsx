import { Link, useMatchRoute } from '@tanstack/react-router';
import { 
  LayoutDashboard, 
  Home, 
  Users, 
  FileText, 
  Bell, 
  Settings,
  ChevronDown,
  LogOut
} from 'lucide-react';
import { useState } from 'react';
import authService from '@/services/auth-service';
import { useNavigate } from '@tanstack/react-router';

interface SidebarProps {
  collapsed: boolean;
  toggleCollapse: () => void;
}

export default function Sidebar({ collapsed, toggleCollapse }: SidebarProps) {
  const matchRoute = useMatchRoute();
  const navigate = useNavigate();
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  const handleLogout = () => {
    authService.logout();
    navigate({ to: '/login' });
  };

  // Mock user data - in a real app, this would come from your auth context or state
  const user = {
    name: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
    email: 'nguy<PERSON><PERSON>@yez.tech',
    avatar: 'https://ui-avatars.com/api/?name=<PERSON>uy<PERSON>+Van+A&background=0D8ABC&color=fff'
  };

  const menuItems = [
    { path: '/', label: 'Tổng quan', icon: <LayoutDashboard size={20} /> },
    { path: '/property', label: 'Bài đăng', icon: <Home size={20} /> },
    { path: '/user', label: 'Nhân viên', icon: <Users size={20} /> },
    { path: '/news', label: 'Tin tức', icon: <FileText size={20} /> },
    { path: '/notification', label: 'Thông báo', icon: <Bell size={20} /> },
    { path: '/setting', label: 'Cài đặt', icon: <Settings size={20} /> },
  ];

  return (
    <div className={`bg-zinc-900 text-white flex flex-col h-screen transition-all duration-300 ${collapsed ? 'w-20' : 'w-64'}`}>
      {/* Logo section */}
      <div className="p-4 flex justify-center items-center border-b border-zinc-700">
        {collapsed ? (
          <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center text-xl font-bold">
            Y
          </div>
        ) : (
          <div className="text-xl font-bold">YEZ Home Admin</div>
        )}
      </div>

      {/* Menu items */}
      <div className="flex-grow py-4 overflow-y-auto">
        <ul>
          {menuItems.map((item) => {
            const isActive = matchRoute({ to: item.path });
            return (
              <li key={item.path} className="mb-1">
                <Link
                  to={item.path}
                  className={`flex items-center px-4 py-3 ${
                    isActive ? 'bg-blue-600' : 'hover:bg-zinc-800'
                  } transition-colors duration-200`}
                >
                  <span className="mr-3">{item.icon}</span>
                  {!collapsed && <span>{item.label}</span>}
                </Link>
              </li>
            );
          })}
        </ul>
      </div>

      {/* User section */}
      <div className="border-t border-zinc-700 p-4">
        <div 
          className={`flex items-center cursor-pointer ${collapsed ? 'justify-center' : 'justify-between'}`}
          onClick={() => !collapsed && setUserMenuOpen(!userMenuOpen)}
        >
          <div className="flex items-center">
            <img 
              src={user.avatar} 
              alt="User avatar" 
              className="w-10 h-10 rounded-full mr-3" 
            />
            {!collapsed && (
              <div className="flex-grow">
                <p className="font-medium">{user.name}</p>
                <p className="text-xs text-gray-400">{user.email}</p>
              </div>
            )}
          </div>
          {!collapsed && <ChevronDown size={16} className={`transform transition-transform ${userMenuOpen ? 'rotate-180' : ''}`} />}
        </div>

        {/* User menu dropdown */}
        {!collapsed && userMenuOpen && (
          <div className="mt-2 bg-zinc-800 rounded-md overflow-hidden">
            <button 
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-2 hover:bg-zinc-700 transition-colors"
            >
              <LogOut size={16} className="mr-2" />
              <span>Đăng xuất</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 