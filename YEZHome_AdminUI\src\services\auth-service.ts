import axiosInstance from './axios-config';
import type { User } from '@/store/auth-store';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  accessToken: string;
  user: User;
}

class AuthService {
  /**
   * Login with username and password
   * @param credentials - username and password
   * @returns Promise with login response data
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await axiosInstance.post<LoginResponse>('/auth/login', credentials);
    return response.data;
  }
}

export default new AuthService(); 