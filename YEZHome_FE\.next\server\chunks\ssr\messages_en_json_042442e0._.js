module.exports = {

"[project]/messages/en.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"ErrorMessage\":{\"default\":\"An error occurred. Please try again later\",\"locationErrorTitle\":\"Error\",\"locationErrorDescription\":\"Could not get your location: {message}\"},\"Common\":{\"greeting\":\"Hello\",\"loading\":\"Loading\",\"processing\":\"Processing\",\"diamond\":\"Diamond Member\",\"platinum\":\"Platinum Member\",\"silver\":\"Silver Member\",\"gold\":\"Gold Member\",\"bronze\":\"Bronze Member\",\"default\":\"Normal Member\",\"requirementDiamond\":\"Used over 200 million VND\",\"requirementPlatinum\":\"Used over 100 million VND\",\"requirementGold\":\"Used over 50 million VND\",\"requirementSilver\":\"Used over 20 million VND\",\"requirementBronze\":\"Used over 5 million VND\",\"requirementNormal\":\"Used under 5 million VND\",\"propertyStatus_Approved\":\"Approved\",\"propertyStatus_PendingApproval\":\"Pending Approval\",\"propertyStatus_RejectedByAdmin\":\"Rejected by Admin\",\"propertyStatus_RejectedDueToUnpaid\":\"Rejected due to unpaid\",\"propertyStatus_WaitingPayment\":\"Waiting Payment\",\"propertyStatus_Expired\":\"Expired\",\"propertyStatus_Draft\":\"Draft\",\"propertyStatus_Sold\":\"Sold\",\"propertyPostType_sell\":\"Sale\",\"propertyPostType_rent\":\"Rent\",\"highlight_status\":\"Highlight\",\"east\":\"East\",\"west\":\"West\",\"south\":\"South\",\"north\":\"North\",\"northeast\":\"North East\",\"northwest\":\"North West\",\"southeast\":\"South East\",\"southwest\":\"South West\",\"propertyType_nha_rieng\":\"House\",\"propertyType_can_ho\":\"Apartment\",\"propertyType_nha_tro\":\"Motel\",\"legality_so_do\":\"Red Book/ Pink Book\",\"legality_so_hong\":\"Pink Book\",\"legality_hdmb\":\"Sales Contract\",\"legality_khac\":\"Other\",\"interior_day_du\":\"Full\",\"interior_co_ban\":\"Basic\",\"interior_khong_noi_that\":\"No Interior\",\"button_ok\":\"OK\",\"button_cancel\":\"Cancel\",\"button_confirm\":\"Confirm\",\"button_save\":\"Save\",\"button_delete\":\"Delete\",\"button_close\":\"Close\",\"button_next\":\"Continue\",\"button_previous\":\"Back\"},\"NoData\":{\"defaultMessage\":\"No data available\",\"defaultCreateMessage\":\"You haven't posted any listings yet\",\"defaultCreateButtonTitle\":\"Post Listing\"},\"SearchFilter\":{\"addressPlaceholder\":\"Enter address, district, city...\",\"searchButton\":\"Search\",\"transactionTypeButtonDefault\":\"Listing Type\",\"transactionTypeButtonSell\":\"Sell\",\"transactionTypeButtonRent\":\"Rent\",\"popoverTitleTransactionType\":\"Listing Type\",\"transactionSellLabel\":\"Sell\",\"transactionRentLabel\":\"Rent\",\"propertyTypeButton\":\"Property Type\",\"popoverTitlePropertyType\":\"Property Type\",\"propertyTypeHouse\":\"House\",\"propertyTypeApartment\":\"Apartment\",\"propertyTypeVilla\":\"Villa\",\"priceButton\":\"Price Range\",\"popoverTitlePrice\":\"Price Range\",\"pricePlaceholderFrom\":\"From\",\"pricePlaceholderTo\":\"To\",\"priceDisplayFrom\":\"From\",\"priceDisplayTo\":\"To\",\"priceUnitBillion\":\" billion\",\"priceUnitMillion\":\" million\",\"advancedFilterButton\":\"Advanced Filters\",\"popoverTitleAdvanced\":\"Advanced Filters\",\"areaLabel\":\"Area (m²)\",\"bedroomsLabel\":\"Bedrooms\",\"bedroomsPlaceholder\":\"Number of bedrooms\",\"bedroomsOption1\":\"1+\",\"bedroomsOption2\":\"2+\",\"bedroomsOption3\":\"3+\",\"bedroomsOption4\":\"4+\",\"bathroomsLabel\":\"Bathrooms\",\"bathroomsPlaceholder\":\"Number of bathrooms\",\"bathroomsOption1\":\"1+\",\"bathroomsOption2\":\"2+\",\"bathroomsOption3\":\"3+\",\"directionLabel\":\"Direction\",\"directionPlaceholder\":\"House Direction\",\"directionEast\":\"East\",\"directionWest\":\"West\",\"directionSouth\":\"South\",\"directionNorth\":\"North\",\"directionSouthEast\":\"South East\",\"directionSouthWest\":\"South West\",\"directionNorthEast\":\"North East\",\"directionNorthWest\":\"North West\",\"legalStatusLabel\":\"Legal Status\",\"legalStatusPlaceholder\":\"Legal Status\",\"legalStatusRedBook\":\"Red Book\",\"legalStatusPinkBook\":\"Pink Book\",\"legalStatusHandwritten\":\"Handwritten Paper\",\"legalStatusOther\":\"Other\",\"roadWidthLabel\":\"Road Width\",\"roadWidthPlaceholder\":\"Road Width Before House\",\"roadWidthOption1\":\"Under 3m\",\"roadWidthOption2\":\"3-5m\",\"roadWidthOption3\":\"5-7m\",\"roadWidthOption4\":\"Over 7m\",\"resetButton\":\"Reset\",\"applyButton\":\"Apply\",\"activeFiltersLabel\":\"Active Filters:\",\"filterLabelTransactionType\":\"Listing Type\",\"filterLabelPropertyType\":\"Property Type\",\"filterLabelPrice\":\"Price\",\"filterLabelArea\":\"Area\",\"filterLabelBedrooms\":\"Bedrooms\",\"filterLabelBathrooms\":\"Bathrooms\",\"filterLabelDirection\":\"Direction\",\"filterLabelLegalStatus\":\"Legal Status\",\"filterLabelRoadWidth\":\"Road Width\",\"filterLabelAddress\":\"Address\",\"unlimited\":\"unlimited\",\"resetAllButton\":\"Reset All\",\"provincePlaceholder\":\"Select Province\",\"provincePlaceholderAll\":\"All Provinces\",\"loadingProvinces\":\"Loading Provinces\",\"districtPlaceholder\":\"Select District\",\"districtPlaceholderAll\":\"All Districts\",\"loadingDistricts\":\"Loading Districts\",\"filterLabelLocation\":\"Location\",\"addressSearchPlaceholder\":\"Enter address\"},\"HomePage\":{\"fetchErrorTitle\":\"Error\",\"fetchErrorDescription\":\"Could not load property data\"},\"PropertyList\":{\"loading\":\"Loading\",\"noResults\":\"No results found\",\"tryDifferentCriteria\":\"Try different search criteria\",\"searchResults\":\"Found {count} results\",\"pageInfo\":\"Page {current} / {total}\",\"loginRequired\":\"Login Required\",\"dontHaveAccount\":\"Don't have an account?\",\"signUpHere\":\"Sign up here\",\"viewDetails\":\"View Details\",\"checking\":\"Checking\",\"cannotRequestVerification\":\"Cannot request verification\",\"requestVerification\":\"Request Re-verification\",\"requestContact\":\"Contact Requests\",\"activityHistory\":\"Activity History\",\"deleting\":\"Deleting\",\"delete\":\"Delete\",\"noProperty\":\"You haven't posted any properties yet\",\"createProperty\":\"Post Property\",\"propertyListTitle\":\"Your Property Listings\",\"all\":\"All\",\"deleteConfirmTitle\":\"Confirm Deletion\",\"deleteConfirmMessage\":\"Are you sure you want to delete this property?\",\"deleteSuccessToast\":\"Property deleted successfully\",\"deleteErrorToast\":\"Error deleting property\",\"verifyConfirmTitle\":\"Confirm Approval Request\",\"verifyConfirmMessage\":\"You are requesting re-Approval. You have {remainingTimes} Approval attempts left for this listing. Are you sure?\",\"verifySuccessToast\":\"Approval request sent successfully\",\"verifyErrorToast\":\"Could not send Approval request\",\"verifyCheckErrorToast\":\"Could not check remaining Approval times\",\"verifyGenericErrorToast\":\"Error sending Approval request\",\"moreAction\":\"More Actions\",\"searchPlaceholder\":\"Search by property name...\",\"selectAll\":\"Select All\",\"renew\":\"Renew\",\"approved\":\"Approved\",\"pendingApproval\":\"Pending Approval\",\"rejectedByAdmin\":\"Rejected by Admin\",\"rejectedDueToUnpaid\":\"Rejected due to unpaid\",\"waitingPayment\":\"Waiting Payment\",\"expired\":\"Expired\",\"draft\":\"Draft\",\"sold\":\"Sold\",\"bulkHighlightConfirmTitle\":\"Confirm Highlight {count} Properties\",\"bulkHighlightConfirmMessage\":\"Are you sure you want to highlight the selected properties?\",\"bulkHighlightSuccessToast\":\"Successfully highlighted {count} properties\",\"bulkHighlightAlreadyHighlighted\":\"{count} properties were already highlighted\",\"bulkHighlightFailed\":\"{count} properties failed to highlight\",\"bulkHighlightErrorToast\":\"Could not highlight selected properties\",\"bulkHighlightGenericErrorToast\":\"An error occurred while highlighting properties\",\"bulkRenewFeatureInDevelopment\":\"Bulk renew feature is under development\",\"bulkDeleteConfirmTitle\":\"Confirm Delete {count} Properties\",\"bulkDeleteConfirmMessage\":\"Are you sure you want to delete the selected properties? This action cannot be undone?\",\"bulkDeleteSuccessToast\":\"Successfully deleted {count} properties\",\"bulkDeleteErrorToast\":\"Could not delete selected properties\",\"bulkDeleteGenericErrorToast\":\"An error occurred while deleting properties\",\"previous\":\"Previous\",\"next\":\"Next\",\"page\":\"Page\"},\"PropertyCard\":{\"addedToFavorites\":\"Added to Favorites\",\"removedFromFavorites\":\"Removed from Favorites\",\"addedToFavoritesDesc\":\"Property has been added to your favorites\",\"removedFromFavoritesDesc\":\"Property has been removed from your favorites\",\"errorOccurred\":\"Error Occurred\",\"cannotUpdateFavorite\":\"Cannot update favorite status\",\"forSale\":\"For Sale\",\"forRent\":\"For Rent\",\"month\":\"month\",\"bedrooms\":\"BR\",\"bathrooms\":\"BA\",\"details\":\"Details\",\"postDate\":\"Post Date\",\"expiryDate\":\"Expiry Date\",\"daysRemaining\":\"{days} days remaining\",\"expired\":\"Expired\",\"edit\":\"Edit\",\"share\":\"Share\",\"more\":\"More\",\"highlightAddSuccess\":\"Property highlighted successfully\",\"highlightRemoveSuccess\":\"Property unhighlighted successfully\",\"highlightUpdateError\":\"Could not update highlight status\",\"highlightGenericError\":\"An error occurred while updating highlight status\",\"renewFeatureInDevelopment\":\"Renew feature is under development\",\"renew\":\"Renew\",\"cannotRequestVerification\":\"Cannot request verification\",\"requestVerification\":\"Request Re-verification\",\"requestContact\":\"Contact Requests\",\"activityHistory\":\"Activity History\",\"delete\":\"Delete\",\"deleting\":\"Deleting\",\"alreadyHighlighted\":\"This property is already highlighted\",\"highlightConfirmTitle\":\"Highlight Property\",\"highlightConfirmDescription\":\"Confirm highlighting this property\",\"highlightConfirmMessage\":\"Highlighting this post will cost you {amount} VND based on your current membership tier ({memberRank}). Do you want to proceed?\",\"insufficientBalanceTitle\":\"Insufficient Balance\",\"insufficientBalanceMessage\":\"Your wallet balance is insufficient for this action. Please top up your wallet to continue\",\"currentBalance\":\"Current Balance\",\"highlightFee\":\"Highlight Fee\",\"balanceAfter\":\"Balance After\",\"topUpWallet\":\"Top Up Wallet\",\"confirmHighlight\":\"Confirm\",\"cancel\":\"Cancel\"},\"NotFound\":{\"title\":\"Page Not Found\",\"description\":\"The page you are looking for does not exist. Please try again\",\"backButton\":\"Back to Home\"},\"Loading\":{\"message\":\"Please wait a moment\"},\"LoginPage\":{\"title\":\"Login\",\"description\":\"Welcome back to YEZHome!\",\"signUpPrompt\":\"Don't have a YEZHome account?\",\"signUpLink\":\"Sign up here\",\"logoAlt\":\"YEZHome Logo\",\"loginErrorTitle\":\"Login Failed\",\"emailLabel\":\"Email\",\"passwordLabel\":\"Password\",\"emailPlaceholder\":\"Email address\",\"passwordPlaceholder\":\"Password\",\"rememberMeLabel\":\"Remember me\",\"forgotPasswordLink\":\"Forgot password?\",\"loginButton\":\"Login\"},\"RegisterPage\":{\"title\":\"Create Account\",\"description\":\"Create your YEZHome account to start posting listings and managing your properties\",\"loginPrompt\":\"Already have an account?\",\"loginLink\":\"Login\",\"registerErrorTitle\":\"Registration Failed\",\"fullNameLabel\":\"Full Name\",\"emailLabel\":\"Email address\",\"passwordLabel\":\"Password\",\"confirmPasswordLabel\":\"Confirm Password\",\"phoneLabel\":\"Phone Number\",\"usernamePlaceholder\":\"Username\",\"emailPlaceholder\":\"Email address\",\"passwordPlaceholder\":\"Password\",\"confirmPasswordPlaceholder\":\"Confirm Password\",\"phonePlaceholder\":\"Phone number\",\"userTypeQuestion\":\"What do you primarily use YEZHome for?\",\"userTypeBuyer\":\"Buy\",\"userTypeSeller\":\"Sell\",\"registerButton\":\"Register\"},\"ForgotPasswordPage\":{\"title\":\"Forgot Password\",\"description\":\"Enter your email to receive password reset instructions\",\"errorTitle\":\"An Error Occurred\",\"emailLabel\":\"Email\",\"emailPlaceholder\":\"<EMAIL>\",\"submitButton\":\"Send Instructions\",\"backToLoginLink\":\"Back to Login\"},\"PasswordEmailSentPage\":{\"title\":\"Password Reset Request Sent\",\"message\":\"We have sent password reset instructions to your email address. Please check your inbox and follow the link provided\",\"backToLoginLink\":\"Back to Login Page\"},\"UserProfilePage\":{\"loadingMessage\":\"Loading profile\",\"userNotFound\":\"User not found. Please log in\",\"mainTitle\":\"My Account\",\"contactInfoTitle\":\"Contact Information\",\"fullNameLabel\":\"Full Name\",\"emailLabel\":\"Email Address\",\"phoneLabel\":\"Phone Number\",\"userTypeLabel\":\"User Type\",\"passwordSuccessTitle\":\"Password Changed Successfully\",\"passwordSuccessMessage\":\"Your password has been changed successfully. Please log in again\",\"passwordSectionTitle\":\"Change password\",\"passwordSectionDescription\":\"Change your password to protect your account\",\"passwordErrorTitle\":\"Password Change Failed\",\"oldPasswordLabel\":\"Current Password\",\"newPasswordLabel\":\"New Password\",\"confirmNewPasswordLabel\":\"Confirm New Password\",\"saveButton\":\"Save Changes\",\"editButton\":\"Edit\",\"cancelButton\":\"Cancel\",\"taxInfoTitle\":\"Tax and Invoice Information\",\"taxInfoLoadingError\":\"Error loading tax information\",\"taxInfoUpdateSuccess\":\"Tax information updated successfully\",\"taxInfoUpdateError\":\"Error updating tax information\",\"personalTaxCodeLabel\":\"Personal Tax Code\",\"personalTaxCodePlaceholder\":\"Ex: ********** or **********-123\",\"personalTaxCodeHelperText\":\"Tax code consists of 10 or 13 digits\",\"invoiceInfoTitle\":\"Invoice Information\",\"buyerNameLabel\":\"Buyer Name\",\"buyerNamePlaceholder\":\"Enter buyer name\",\"invoiceEmailLabel\":\"Email for Invoice\",\"invoiceEmailPlaceholder\":\"Enter email for invoice\",\"companyNameLabel\":\"Company Name\",\"companyNamePlaceholder\":\"Enter company name\",\"taxCodeLabel\":\"Tax Code\",\"taxCodePlaceholder\":\"Enter tax code\",\"addressLabel\":\"Address\",\"addressPlaceholder\":\"Enter address\",\"notProvided\":\"Not provided\",\"deactivateAccountTitle\":\"Request Account Deactivation\",\"deactivateAccountButton\":\"Deactivate Account\",\"deactivateAccountDescription\":\"Please enter your password to confirm account deactivation\",\"warningTitle\":\"Warning\",\"deactivateWarning1\":\"You will not be able to log in to this account after deactivation\",\"deactivateWarning2\":\"Your active listings will continue to be displayed until their expiration date\",\"deactivateWarning3\":\"Any remaining balance in your accounts will not be refunded\",\"deactivateWarning4\":\"Your service account can only be deactivated when there is no outstanding balance\",\"deactivateWarning5\":\"The primary phone number registered with this account and your listing phone numbers cannot be used to register a new account\",\"deleteAccountTitle\":\"Request Account Deletion\",\"deleteAccountButton\":\"Delete Account\",\"deleteAccountDescription\":\"Please enter your password to confirm account deletion request\",\"deleteAccountWarning\":\"Submit a request to delete all account information. Once processed, all information will be deleted and cannot be undone\",\"passwordLabel\":\"Password\",\"passwordPlaceholder\":\"Enter your password\",\"reasonLabel\":\"Reason\",\"reasonPlaceholder\":\"Please provide a reason (optional)\",\"confirmDeactivateButton\":\"Confirm Deactivation\",\"confirmDeleteButton\":\"Confirm Deletion\",\"passwordRequired\":\"Password is required\",\"accountDeactivateSuccess\":\"Account deactivation request submitted successfully\",\"accountDeactivateError\":\"Error submitting account deactivation request\",\"accountDeleteRequestSuccess\":\"Account deletion request submitted successfully\",\"accountDeleteRequestError\":\"Error submitting account deletion request\",\"avatarTitle\":\"Profile Picture\",\"uploadAvatarButton\":\"Upload Avatar\",\"removeAvatarButton\":\"Remove Avatar\",\"avatarRequirements\":\"Image must be JPG or PNG, max 200KB\",\"avatarSizeError\":\"Image size exceeds 200KB limit\",\"avatarUploadSuccess\":\"Avatar uploaded successfully\",\"avatarUploadError\":\"Failed to upload avatar\",\"avatarDeleteSuccess\":\"Avatar removed successfully\",\"avatarDeleteError\":\"Failed to remove avatar\",\"personalTab\":\"Personal\",\"accountSettingsTab\":\"Account Settings\"},\"UserDashboardPage\":{\"loadingMessage\":\"Loading data\",\"fetchError\":\"Could not load dashboard data\",\"genericFetchError\":\"An error occurred while loading data\",\"noData\":\"No data available\",\"greeting\":\"Hello, {fullName}\",\"lastLoginLabel\":\"Last login:\",\"postListingButton\":\"Post New Listing\",\"depositButton\":\"Deposit Funds\",\"walletCardTitle\":\"Wallet Information\",\"propertyStatsCardTitle\":\"Property Statistics\",\"recentTransactionsTitle\":\"Recent Transactions\",\"memberRankingTitle\":\"Member Rank\"},\"UserPropertiesPage\":{\"title\":\"Manage Properties\",\"description\":\"Manage your property listings\",\"aleatAnalysis\":\"Listing information will be updated every 60 minutes\",\"createButton\":\"Post Property\",\"viewDetailsButton\":\"View Details\",\"moreActionsButton\":\"More Action\",\"verifyLoading\":\"Checking\",\"verifyDisabledTooltip\":\"Cannot request approval\",\"verifyButton\":\"Request Re-approval\",\"contactsButton\":\"Contact Requests\",\"historyButton\":\"Activity History\",\"deleteLoading\":\"Deleting\",\"deleteButton\":\"Delete\",\"deleteConfirmTitle\":\"Confirm Deletion\",\"deleteConfirmMessage\":\"Are you sure you want to delete this property?\",\"deleteSuccessToast\":\"Property deleted successfully\",\"deleteErrorToast\":\"Error deleting property\",\"verifyConfirmTitle\":\"Confirm Verification approval\",\"verifyConfirmMessage\":\"You are requesting re-approval. You have {remainingTimes} approval attempts left for this listing. Are you sure you want to submit it for review?\",\"verifySuccessToast\":\"Approval request sent successfully\",\"verifyErrorToast\":\"Could not send Approval request\",\"verifyGenericErrorToast\":\"Error sending Approval request\",\"verifyCheckErrorToast\":\"Could not check remaining Approval\",\"verifyCheckGenericErrorToast\":\"Error checking Approval times\"},\"FavoritePage\":{\"title\":\"Favorite Properties\",\"description\":\"Manage your saved property listings\"},\"FavoriteList\":{\"filters\":\"Filters\",\"filterOptions\":\"Filter Options\",\"priceRange\":\"Price Range\",\"minPrice\":\"Minimum Price\",\"maxPrice\":\"Maximum Price\",\"dateRange\":\"Date Range\",\"applyFilters\":\"Apply\",\"clearFilters\":\"Clear\",\"sortByDate\":\"Date Added\",\"sortByPrice\":\"Price\",\"descending\":\"Descending\",\"ascending\":\"Ascending\",\"resultsCount\":\"Found {count} favorite properties\",\"noFavorites\":\"You don't have any favorite properties yet\",\"errorFetchingFavorites\":\"Unable to load favorites list\",\"removedFromFavorites\":\"Removed from favorites\",\"errorRemovingFavorite\":\"Unable to remove from favorites\"},\"FavoritePropertyCard\":{\"forSale\":\"For Sale\",\"forRent\":\"For Rent\",\"confirmRemoveTitle\":\"Remove from favorites?\",\"confirmRemoveDescription\":\"Are you sure you want to remove this property from your favorites?\",\"cancel\":\"Cancel\",\"remove\":\"Remove\",\"removing\":\"Removing\",\"month\":\"month\",\"bedrooms\":\"BR\",\"bathrooms\":\"BA\",\"addedOn\":\"Added on\",\"viewDetails\":\"View Details\"},\"PropertyForm\":{\"mediaSection\":\"Upload Images and Videos\",\"basicInfo\":\"Basic Information\",\"selectPropertyType\":\"Select Property Type\",\"addressSection\":\"Address\",\"requiredInfo\":\"Required Information\",\"fetchPlaceError\":\"Error fetching place data:\",\"fetchCitiesError\":\"Error fetching cities\",\"fetchDistrictsError\":\"Error fetching districts\",\"fetchWardsError\":\"Error fetching wards\",\"fetchAddressSuggestionsError\":\"Error fetching address suggestions:\",\"addressDescription\":\"Enter address or location to search, if not found you can search for nearby addresses and use the drag and drop checkpoint feature to determine the exact location\",\"addressDescription2\":\"This is the address used to display on your post\",\"locationConflictTitle\":\"Location Conflict Detected\",\"locationConflictDescription\":\"The address you selected is in {newLocation}, which is different from the {currentLocation} area you previously selected.\\n\\nDo you want to automatically switch to {newLocation}?\",\"switchToNewLocation\":\"Switch to {location}\",\"keepCurrentLocation\":\"Keep {location}\",\"mapDescription\":\"Move the pin to the exact location of the property\",\"draft\":\"Draft\",\"propertyIsBeingReviewedOrApprovedOrSold\":\"The property is being reviewed or approved or sold, and cannot be edited\",\"backToPropertyList\":\"Back to my property list\",\"noChanges\":\"You have not changed any information\",\"saveSuccess\":\"Save successfully\",\"saveFailed\":\"Save failed\",\"postInformation\":\"Post Information\",\"addressSelected\":\"Address displayed on the post\",\"addressSelectedPlaceholder\":\"e.g. address displayed on the post\",\"price\":\"Price\",\"pricePlaceholder\":\"Enter price\",\"updatePrice\":\"Update Price\",\"continue\":\"Continue\",\"announcementChange\":\"Announcement Change\",\"congratulations\":\"🎉 Congratulations! You have been upgraded\",\"cancel\":\"Cancel\",\"map\":\"Map Location\",\"rankUpgradeMessage\":\"Congratulations! You've been upgraded to {currentRank}. The posting fee has decreased from {previousPrice} to {currentPrice}! Continue with the new price?\",\"rankChangeMessage\":\"Your member rank has changed. The current posting fee is {currentPrice} instead of {previousPrice}. Do you want to continue?\",\"processing\":\"Processing\",\"saveDraft\":\"Save Draft\",\"saveAndSendForApproval\":\"Save and Send for Approval\",\"propertyUpdated\":\"Property updated successfully\",\"propertyNotUpdated\":\"Property update failed\",\"createPostFailed\":\"Create Post Failed\",\"postCannotBeEdited\":\"Post Cannot Be Edited\",\"propertyCannotBeEdited\":\"This property is under review or has been approved/sold and cannot be edited\",\"propertyVideoLink\":\"Property Video Link\",\"propertyVideoLinkPlaceholder\":\"Enter Youtube, Tiktok link, ...\",\"postType\":\"Listing Type\",\"sell\":\"Sell\",\"rent\":\"Rent\",\"propertyType\":\"Property Type\",\"house\":\"House\",\"apartment\":\"Apartment\",\"motel\":\"Motel\",\"city\":\"Province/City\",\"cityPlaceholder\":\"Select province/city\",\"district\":\"District\",\"districtPlaceholder\":\"Select district\",\"ward\":\"Ward\",\"wardPlaceholder\":\"Select ward/commune\",\"address\":\"Enter location (street name, project) to find the marked location on the map\",\"loadingRankChangeInfo\":\"Loading rank change information\",\"addressPlaceholder\":\"Enter address, street name, project...\",\"submissionErrorTitle\":\"Submission Error\",\"submissionErrorSizeLimit\":\"Could not submit the post. Please check file sizes or try again later\",\"refreshRank\":\"Refresh Rank\",\"refreshing\":\"Refreshing\",\"rankUpdated\":\"Rank Updated\",\"rankRefreshed\":\"Your rank information has been refreshed\",\"error\":\"Error\",\"errorRefreshingRank\":\"Error refreshing rank data\",\"enterAddressManually\":\"Enter address manually\",\"noteEnterAddress\":\"The address you select must match the province/city information above\",\"manualInputAddressToggle\":\"Manual address input\",\"fetchPropertyErrorTitle\":\"Error Fetching Property\",\"fetchPropertyErrorDescription\":\"An error occurred while fetching property data. Please try again later\"},\"PropertyImageUploader\":{\"avatar\":\"Avatar\",\"setAsAvatar\":\"Set as Avatar\",\"deleteImage\":\"Delete Image\",\"error\":\"Error\",\"setAvatarSuccess\":\"Set avatar successfully\",\"deleteImageSuccess\":\"Delete image successfully\",\"dropHere\":\"Drop images here\",\"uploadPropertyImage\":\"Upload Property Images\",\"dragAndDrop\":\"Drag & drop or click to select images\",\"supportedFormats\":\"Supported formats: JPEG, PNG, JPG, WEBP\",\"uploadingImage\":\"Uploading images\",\"uploadedImages\":\"Uploaded Images\",\"image\":\"image\",\"images\":\"images\",\"edit\":\"Edit\",\"manageImages\":\"Manage Images\",\"maxFileSize\":\"Maximum file size: 1MB\",\"addCaptionPlaceholder\":\"Enter image caption\"},\"PropertyPostInformation\":{\"postInformation\":\"Post Information\",\"requiredInformation\":\"Required Information\",\"title\":\"Post Title\",\"titlePlaceholder\":\"e.g., House for Sale in District 1, Good Price\",\"titleDescription\":\"Minimum 30, maximum 99 characters\",\"introduction\":\"Detailed Description\",\"descriptionPlaceholder\":\"Enter detailed description of the property...\",\"descriptionDescription\":\"Minimum 30, maximum 3000 characters\"},\"AdditionalInformation\":{\"detailedInformation\":\"Detailed Information\",\"optional\":\"Optional Information\",\"area\":\"Area\",\"floors\":\"Floors\",\"rooms\":\"Bedrooms\",\"toilets\":\"Bathrooms\",\"direction\":\"Direction\",\"selectDirection\":\"Select Direction\",\"north\":\"North\",\"south\":\"South\",\"east\":\"East\",\"west\":\"West\",\"northeast\":\"Northeast\",\"northwest\":\"Northwest\",\"southeast\":\"Southeast\",\"southwest\":\"Southwest\",\"balconyDirection\":\"Balcony Direction\",\"selectBalconyDirection\":\"Select Balcony Direction\",\"width\":\"Width\",\"roadWidth\":\"Road Width\",\"legality\":\"Legal Documents\",\"selectLegality\":\"Select Legal Status\",\"redBook\":\"Red Book/ Pink Book\",\"pinkBook\":\"Pink Book\",\"purchaseContract\":\"Sales Contract\",\"other\":\"Other\",\"interior\":\"Interior Condition\",\"selectInterior\":\"Select Interior Condition\",\"full\":\"Full\",\"basic\":\"Basic\",\"noInterior\":\"No Interior\"},\"CreatePropertyDetailInformation\":{\"loading\":\"Loading\",\"customerType\":\"Customer Type\",\"individualCustomer\":\"Individual Customer\",\"memberRank\":\"User Rank\",\"highlightPost\":\"Highlight Post\",\"highlightPostDescription\":\"The post will be highlighted in the list\",\"autoRenew\":\"Auto Renew\",\"autoRenewDescription\":\"The post will be auto renewed after expiration\",\"postStatus\":\"Post Status\",\"expirationTime\":\"Display Time\",\"days\":\"days\",\"postDate\":\"Post Date\",\"expirationDate\":\"Expiration Date\",\"postFeeTitle\":\"Posting Fee Details\",\"postFee\":\"Posting Fee\",\"highlightFee\":\"Highlight Fee\",\"total\":\"Total\"},\"CreatePropertySuccessPage\":{\"pageTitle\":\"Transaction Result\",\"successMessage\":\"Listing Submitted\",\"baseCostLabel\":\"0 đ\",\"moderationNotice\":\"Your listing will be reviewed within 8 working hours\",\"statusLabel\":\"Status\",\"statusPending\":\"Pending Review\",\"listingIdLabel\":\"Listing ID\",\"displayTimeLabel\":\"Display Time\",\"highlightTitle\":\"Use highlight checkpoint feature for better visibility and reach\",\"highlightPackageTitle\":\"Highlight Checkpoint Package\",\"highlightDuration\":\"Highlight checkpoint duration matches listing duration\",\"highlightOption3\":\"3 Pushes\",\"highlightOption6\":\"6 Pushes\",\"autoRenewTitle\":\"Auto Re-list\",\"autoRenewDescription\":\"Listing will be re-listed immediately upon expiration. Only points for that re-list will be deducted\",\"postAnotherButton\":\"Post Another Property\",\"manageListingsButton\":\"Manage Listings\"},\"UserTransactionsPage\":{\"statusCompleted\":\"Completed\",\"statusPending\":\"Processing\",\"statusFailed\":\"Failed\",\"statusCancelled\":\"Cancelled\",\"loadingMessage\":\"Loading data\",\"fetchError\":\"Could not load transaction history\",\"genericFetchError\":\"An error occurred while loading transaction history\",\"pageTitle\":\"Transaction History\",\"detailPageTitle\":\"Transaction Details\",\"backButton\":\"Back\",\"transactionFetchError\":\"Could not load transaction details\",\"transactionGenericFetchError\":\"An error occurred loading transaction details\",\"transactionNotFound\":\"Transaction not found\",\"backToListButton\":\"Back to Transaction List\",\"cardTitleDeposit\":\"Wallet Deposit\",\"cardTitlePayment\":\"Payment\",\"transactionIdLabel\":\"Transaction ID:\",\"orderIdLabel\":\"Order ID:\",\"transactionTypeLabel\":\"Transaction Type:\",\"paymentMethodLabel\":\"Method:\",\"paymentMethodBank\":\"Bank Transfer\",\"paymentMethodMomo\":\"MoMo Wallet\",\"paymentMethodCard\":\"Credit/Debit Card\",\"creationDateLabel\":\"Creation Date:\",\"completionDateLabel\":\"Completion Date:\",\"descriptionLabel\":\"Description:\",\"relatedListingLink\":\"View Related Listing\",\"verifyButton\":\"I have paid, verify now\",\"verifyingButton\":\"Verifying\",\"verifySuccessTitle\":\"Success\",\"verifySuccessMessage\":\"Verification request submitted\",\"verifyErrorTitle\":\"Error\",\"verifyErrorMessage\":\"Could not verify transaction\",\"verifyGenericErrorMessage\":\"An error occurred during verification\",\"totalDepositLabel\":\"Total Deposit\",\"totalSpendingLabel\":\"Total Spending\",\"currentBalanceLabel\":\"Current Balance\",\"filterAll\":\"All\",\"filterDeposit\":\"Deposits\",\"filterSpending\":\"Spending\",\"timeframePlaceholder\":\"Timeframe\",\"timeframeAll\":\"All Time\",\"timeframeMonth\":\"This Month\",\"timeframeYear\":\"This Year\",\"exportButton\":\"Export PDF\",\"transactionTypeDeposit\":\"Deposit\",\"transactionTypePayment\":\"Payment\",\"noTransactions\":\"No transactions found\"},\"UserWalletPage\":{\"statusCompleted\":\"Completed\",\"statusPending\":\"Processing\",\"statusFailed\":\"Failed\",\"statusCancelled\":\"Cancelled\",\"fetchWalletError\":\"Could not load wallet information\",\"genericFetchError\":\"An error occurred loading wallet information\",\"copySuccessToastTitle\":\"Copied!\",\"copySuccessToastDesc\":\"Information copied to clipboard\",\"invalidAmountErrorTitle\":\"Invalid Amount\",\"invalidAmountErrorDesc\":\"Minimum deposit amount is 50,000₫\",\"topUpErrorTitle\":\"Error\",\"topUpCreateErrorDesc\":\"Could not create deposit transaction\",\"topUpProcessErrorDesc\":\"An error occurred processing the deposit\",\"checkPaymentErrorTitle\":\"Payment Check Error\",\"paymentSuccessTitle\":\"Top-up Successful!\",\"paymentSuccessDesc\":\"Your balance has been updated with {amount}\",\"checkPaymentErrorDesc\":\"Could not check payment status\",\"checkPaymentGenericErrorDesc\":\"An error occurred checking payment status\",\"pageTitle\":\"Manage Wallet\",\"walletInfoTitle\":\"Wallet Information\",\"currentBalanceLabel\":\"Current Balance\",\"memberRankLabel\":\"Member Rank\",\"depositSectionTitle\":\"Deposit to Wallet\",\"depositStep1\":\"Amount\",\"depositStep2\":\"Payment\",\"depositStep3\":\"Invoice\",\"depositAmount\":\"Amount: \",\"depositPaymentMethod\":\"Payment Method: \",\"depositInvoice\":\"Invoice: \",\"transactionHistoryLabel\":\"Transaction History\",\"amountLabel\":\"Enter deposit amount\",\"customAmountPlaceholder\":\"Enter other amount (Min 50,000₫)\",\"paymentMethodLabel\":\"Select payment method\",\"methodBank\":\"Bank Transfer\",\"methodMomo\":\"MoMo Wallet\",\"methodCard\":\"Credit/Debit Card\",\"depositButton\":\"Deposit\",\"processingButton\":\"Processing\",\"bankTransferTitle\":\"Bank Transfer Information\",\"bankTransferInstruction\":\"Please transfer the exact amount according to the information below:\",\"bankLabel\":\"Bank:\",\"accountNumberLabel\":\"Account Number:\",\"accountNameLabel\":\"Account Name:\",\"branchLabel\":\"Branch:\",\"transferContentLabel\":\"Transfer Content:\",\"copyButton\":\"Copy\",\"importantNoteTitle\":\"Important Note:\",\"note1\":\"Transfer the exact deposit amount\",\"note2\":\"Enter the transfer content exactly as provided. Complete the transfer and then click the verify button\",\"note3\":\"Funds may take 5-15 minutes to reflect after successful transfer\",\"note4\":\"Contact support if funds are not credited after 30 minutes\",\"confirmTransferButton\":\"I have paid, verify now\",\"dialogTitleSuccess\":\"Transaction Successful\",\"dialogTitlePending\":\"Transaction Pending\",\"dialogTitleCreated\":\"Transaction Created\",\"dialogTitleFailed\":\"Transaction Failed\",\"dialogMessageSuccess\":\"Successfully deposited {amount} into your wallet\",\"dialogMessagePendingBank\":\"Transaction created. Please complete the bank transfer\",\"dialogMessagePendingOther\":\"Transaction is being processed\",\"dialogMessageFailed\":\"Transaction failed. Please try again\",\"dialogTransactionIdLabel\":\"Transaction ID:\",\"dialogAmountLabel\":\"Amount:\",\"dialogDateLabel\":\"Transaction Date:\",\"dialogPaymentMethodLabel\":\"Payment Method:\",\"dialogStatusLabel\":\"Status:\",\"dialogCloseButton\":\"Close\",\"dialogViewHistoryButton\":\"View Transaction History\",\"qrInstruction\":\"Scan QR code with {methodName} app\",\"qrExpiryLabel\":\"QR code expires in:\",\"resultLoadingTitle\":\"Processing Payment\",\"resultLoadingMessage\":\"Please wait while we verify your transaction\",\"resultSuccessTitle\":\"Payment Successful!\",\"resultSuccessMessage\":\"Your transaction has been processed successfully\",\"resultPendingTitle\":\"Payment Processing\",\"resultPendingMessage\":\"Your transaction is being processed. Your balance will be updated upon confirmation from the payment gateway\",\"resultFailedTitle\":\"Payment Failed\",\"resultFailedMessage\":\"Sorry, your transaction could not be completed. Please try again or choose a different payment method\",\"resultTransactionIdLabel\":\"Transaction ID:\",\"resultAmountLabel\":\"Amount:\",\"resultTimeLabel\":\"Time:\",\"resultActionSuccess\":\"Back to Wallet\",\"resultActionRetry\":\"Try Again\",\"resultActionHistory\":\"View Transaction History\",\"loadingMessage\":\"Loading data\",\"customAmountLabel\":\"Enter custom amount\",\"noTransactionsMessage\":\"No transactions found\",\"viewAllTransactionsButton\":\"View All Transactions\",\"cancelButton\":\"Cancel\",\"postingFeeLabel\":\"Posting Fee\",\"postingFeeValue\":\"55,000₫/10 days\",\"highlightFeeLabel\":\"Highlight Fee\",\"viewPricingLink\":\"View detailed pricing\",\"bankingTransfer\":\"Bank Transfer\",\"momoPay\":\"MoMo Pay\",\"creditCard\":\"Credit/Debit Card\",\"atm\":\"ATM Card\",\"vnPay\":\"VNPay QR\",\"bankingTransferDescription\":\"Direct bank transfer from your bank account\",\"momoPayDescription\":\"Pay with your MoMo wallet\",\"creditCardDescription\":\"Pay with your credit/debit card\",\"atmDescription\":\"Pay with your ATM card\",\"vnPayDescription\":\"Pay with your VNPay QR\",\"yes\":\"Yes\",\"no\":\"No\",\"requestInvoice\":\"Request Invoice\",\"requestInvoiceDescription\":\"Get an invoice for this transaction\",\"invoiceInfoTitle\":\"Invoice Information\",\"invoiceCompanyNameLabel\":\"Company Name\",\"invoiceTaxCodeLabel\":\"Tax Code\",\"invoiceAddressLabel\":\"Address\"},\"UserNotificationsPage\":{\"title\":\"Notifications\",\"description\":\"Manage your notifications\",\"typeSystem\":\"System\",\"typeTransaction\":\"Transaction\",\"typePromotion\":\"Promotion\",\"typeContactRequest\":\"Contact Request\",\"loadingErrorTitle\":\"Error\",\"loadingErrorMessage\":\"Could not load notifications\",\"loadingGenericError\":\"An error occurred loading notifications\",\"markReadSuccessTitle\":\"Success\",\"markReadSuccessMessage\":\"Notification marked as read\",\"markReadErrorTitle\":\"Error\",\"markReadErrorMessage\":\"Could not mark notification as read\",\"markReadGenericError\":\"An error occurred marking notification as read\",\"markAllReadSuccessTitle\":\"Success\",\"markAllReadSuccessMessage\":\"All notifications marked as read\",\"markAllReadErrorTitle\":\"Error\",\"markAllReadErrorMessage\":\"Could not mark all notifications as read\",\"markAllReadGenericError\":\"An error occurred marking all notifications as read\",\"markAllReadButton\":\"Mark All as Read\",\"dateFormatTimeConnector\":\"'at'\",\"noNotificationsMessage\":\"You have no notifications\",\"previousPageButton\":\"Previous\",\"nextPageButton\":\"Next\",\"navigationErrorTitle\":\"Navigation Error\",\"navigationErrorMessage\":\"Unable to open link. Please try again\",\"actionTextContact\":\"View Property\",\"actionTextContactDefault\":\"View Details\",\"actionTextTransaction\":\"View Transaction\",\"actionTextWalletUpdate\":\"View Wallet\",\"actionTextSystem\":\"View Notification\",\"actionTextPromotion\":\"View Promotion\",\"actionTextNews\":\"Read News\",\"actionTextDefault\":\"View Details\",\"notificationTimeMinutes\":\"{count, plural, =1 {# minute ago} other {# minutes ago}}\",\"notificationTimeHours\":\"{count, plural, =1 {# hour ago} other {# hours ago}}\",\"notificationTimeDays\":\"{count, plural, =1 {# day ago} other {# days ago}}\",\"notificationViewAll\":\"View all notifications\",\"loadingNotifications\":\"Loading notifications\"},\"PricingPage\":{\"viewPricing\":\"View Pricing\",\"pricingTable\":\"Service Pricing Table\",\"individualCustomer\":\"Individual Customer\",\"userRank\":\"Member Rank\",\"highlightPost\":\"Highlight Post\",\"individualCustomerService\":\"Services for Individual Customers\",\"servicePriceIncludes10VAT\":\"Service prices include 10% VAT\",\"price\":\"Price\",\"displayTime\":\"Display Time\",\"eachPost\":\"Per Listing Post\",\"10Days\":\"10 days\",\"memberBenefits\":\"Member Benefits\",\"memberBenefitsDescription\":\"Benefits of becoming our member\",\"trust\":\"Reputation\",\"trustDescription\":\"Display member rank next to username to increase credibility\",\"quick\":\"Speed\",\"quickDescription\":\"Fast post approval within 5 minutes of submission\",\"save\":\"Savings\",\"saveDescription\":\"Lower fees for highlight checkpoint service\",\"userRankDescription\":\"Based on amount spent (excluding deposits). Valid for 1 year from account creation date\",\"highlightCheckpointPrice\":\"Highlight Checkpoint Fee\",\"highlightCheckpointDescription\":\"Lower highlight checkpoint fee\",\"pageTitle\":\"Service Pricing\",\"vatNotice\":\"Service prices include 10% VAT\",\"individualSectionTitle\":\"Services for Individual Customers\",\"serviceHeader\":\"Service\",\"priceHeader\":\"Unit Price\",\"durationHeader\":\"Duration\",\"postingFeeService\":\"Basic Listing Fee\",\"postingFeeDuration\":\"10 days\",\"benefitsSectionTitle\":\"Member Benefits\",\"benefitsSectionDesc\":\"Benefits of becoming our member\",\"benefit1Title\":\"Reputation\",\"benefit1Desc\":\"Display member rank next to username to increase credibility\",\"benefit2Title\":\"Speed\",\"benefit2Desc\":\"Fast post approval within 5 minutes of submission\",\"benefit3Title\":\"Savings\",\"benefit3Desc\":\"Lower fees for highlight checkpoint service\",\"rankingSectionTitle\":\"Member Ranking\",\"rankingSectionDesc\":\"Based on amount spent (excluding deposits). Valid for 1 year from account creation date\",\"highlightFeeSectionTitle\":\"Highlight Checkpoint Fee\",\"highlightFeeSectionDesc\":\"Highlight checkpoint fee based on member rank\",\"rankHeader\":\"Rank\",\"returnButton\":\"Return to Dashboard\"},\"UserSidebar\":{\"profile\":\"My Profile\",\"payments\":\"Payment Management\",\"notifications\":\"Notifications\",\"properties\":\"Properties\",\"dashboard\":\"Dashboard\",\"setting\":\"Setting\",\"transferCode\":\"Transfer Code\",\"topUpWallet\":\"Top Up\",\"walletBalance\":\"Balance\",\"userRank\":\"Member Rank\"},\"ContactRequestModal\":{\"title\":\"Contact Requests\",\"description\":\"Contact requests from customers\",\"requestContact\":\"Contact Requests\",\"requestContactDescription\":\"Contact requests from customers\",\"requestContactButton\":\"Contact Requests\",\"error\":\"Error\",\"success\":\"Success\",\"cannotLoadContactRequestList\":\"Cannot load contact request list\",\"errorLoadingContactRequests\":\"An error occurred while loading contact requests\",\"errorMarkingAsRead\":\"An error occurred while marking as read\",\"errorInBulkMarkAsRead\":\"An error occurred while bulk marking as read\",\"pleaseSelectAtLeastOneRequest\":\"Please select at least one contact request\",\"allRequestsMarkedAsRead\":\"All contact requests marked as read\",\"cannotUpdateSomeRequests\":\"Cannot update status of some contact requests\",\"errorOccurredWhenUpdating\":\"An error occurred while updating contact request status\",\"contactRequestList\":\"Contact Request List\",\"selectAll\":\"Select All\",\"markAsRead\":\"Mark as Read\",\"loading\":\"Loading\",\"read\":\"Read\",\"unread\":\"Unread\",\"noContactRequest\":\"No contact requests\",\"close\":\"Close\"},\"AboutPage\":{\"heroTitle\":\"ABOUT YEZ HOME\",\"heroSubtitle\":\"YEZ Home is a real estate application within the YEZ Tech ecosystem. We provide modern solutions for searching and transacting properties\",\"missionTitle\":\"Our Mission\",\"missionText\":\"With the increasing real estate needs of young people, especially students needing rental accommodation when starting university, YEZ Home aims to bring convenience to those genuinely looking to buy, sell, or rent property. Making real estate transactions younger and less dry. Shortening the distance between sellers/landlords and buyers/renters. Helping customers easily search and select properties that meet their needs and preferences\",\"feature1Title\":\"Dual View & Checkpoint\",\"feature1Desc\":\"Displayed directly on the homepage, helping users easily find properties in their desired location\",\"feature2Title\":\"User Verification\",\"feature2Desc\":\"Verification via phone number increases the trustworthiness of posted information\",\"feature3Title\":\"Smart Search\",\"feature3Desc\":\"Intuitive, friendly interface, easy to use even for those less familiar with technology\",\"dashboardSectionTitle\":\"Effective Management\",\"dashboardSectionDesc\":\"With a clear, intuitive dashboard display, sellers, landlords, and real estate agents can see performance and easily manage their product portfolio\",\"companySectionTitle\":\"About YEZ Tech\",\"companySectionDesc\":\"YEZ Tech is a technology company established in [Month] 2025. Specializing in creating web and mobile applications aimed at bringing convenience and ease to modern life\",\"contactSectionTitle\":\"Contact Us\",\"contactEmail\":\"<EMAIL>\",\"contactPhone\":\"Hotline: 1900 xxxx\"},\"ContactPage\":{\"headerTitle\":\"Contact Us\",\"headerSubtitle\":\"We are happy to answer any questions you may have\",\"formTitle\":\"Send us a message\",\"nameLabel\":\"Full Name\",\"namePlaceholder\":\"Enter your full name\",\"emailLabel\":\"Email\",\"emailPlaceholder\":\"Enter your email address\",\"messageLabel\":\"Message\",\"messagePlaceholder\":\"Enter your message here\",\"submitButton\":\"Send Message\",\"infoTitle\":\"Contact Information\",\"infoEmailLabel\":\"Email\",\"infoPhoneLabel\":\"Phone\",\"infoAddressLabel\":\"Address\",\"infoEmailValue\":\"<EMAIL>\",\"infoPhoneValue\":\"+1 (234) 567-890\",\"infoAddressValue\":\"123 Example St, City, Province 12345\"},\"NewsPage\":{\"pageTitle\":\"Latest Real Estate News\",\"pageDescription\":\"New, comprehensive, and engaging information about the Vietnam real estate market\",\"searchPlaceholder\":\"Search by title...\",\"searchButton\":\"Search\",\"sortByDate\":\"Date Published\",\"sortByTitle\":\"Title\",\"pageSizeLabel\":\"Show:\",\"fetchError\":\"Failed to fetch blog posts\",\"authorPrefix\":\"By\",\"dateConnector\":\"•\",\"readMoreButton\":\"Read More\",\"paginationShowing\":\"Showing {start} to {end} of {total} results\",\"paginationPrevious\":\"Previous\",\"paginationNext\":\"Next\",\"backButton\":\"Back to list\",\"tagsLabel\":\"Tags:\",\"commentsTitle\":\"Comments\",\"noComments\":\"No comments yet\",\"notFoundTitle\":\"Blog Post Not Found\",\"notFoundDescription\":\"The requested blog post could not be found\"},\"Navbar\":{\"logoAlt\":\"YEZHome Logo\",\"linkHome\":\"Home\",\"linkBuy\":\"Buy\",\"linkRent\":\"Rent\",\"linkNews\":\"News\",\"linkAbout\":\"About Us\",\"linkContact\":\"Contact Us\",\"linkPricing\":\"Pricing\",\"userMenuMyAccount\":\"My Account\",\"userMenuMyProperties\":\"My Properties\",\"userMenuMyFavorites\":\"My Favorites\",\"favoriteTooltip\":\"Saved Listings\",\"postProperty\":\"Post Listing\",\"downloadApp\":\"Download App\",\"login\":\"Login\",\"register\":\"Register\",\"mobileMenuTitle\":\"Menu\",\"refreshData\":\"Refresh Data\",\"userGreeting\":\"Welcome back\"},\"VerifyEmail\":{\"title\":\"Verify Your Email\",\"message\":\"Thank you for signing up! We have sent a verification link to your email address. Please check your inbox and click the link to activate your account\",\"spamNotice\":\"If you don't see the email, please check your spam folder\",\"loginButton\":\"Login\",\"homeButton\":\"Home\"},\"UserInfo\":{\"refresh\":\"Refresh\",\"refreshUserData\":\"Refresh user data\"},\"PropertyShare\":{\"title\":\"Share Property\",\"description\":\"Share this property with friends or on social media\",\"viewProperty\":\"View property\",\"copySuccess\":\"Link copied to clipboard\",\"copyError\":\"Could not copy link\",\"copy\":\"Copy\"}}"));}}),

};