(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/[locale]/(protected)/user/bds/new/components/SelectedLocationMap.jsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@goongmaps_goong-js_dist_goong-js_95be0bc1.js",
  "static/chunks/330cb_[locale]_(protected)_user_bds_new_components_SelectedLocationMap_jsx_c1e3578a._.js",
  {
    "path": "static/chunks/node_modules_@goongmaps_goong-js_dist_goong-js_23b57252.css",
    "included": [
      "[project]/node_modules/@goongmaps/goong-js/dist/goong-js.css [app-client] (css)"
    ]
  },
  "static/chunks/330cb_[locale]_(protected)_user_bds_new_components_SelectedLocationMap_jsx_0b60d101._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/app/[locale]/(protected)/user/bds/new/components/SelectedLocationMap.jsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);