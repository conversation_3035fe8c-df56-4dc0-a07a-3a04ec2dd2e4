{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/label.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Label as LabelPrimitive } from \"radix-ui\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root ref={ref} className={cn(labelVariants(), className)} {...props} />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACvD,8OAAC,kMAAA,CAAA,QAAc,CAAC,IAAI;QAAC,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAAa,GAAG,KAAK;;;;;;AAErF,MAAM,WAAW,GAAG,kMAAA,CAAA,QAAc,CAAC,IAAI,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/form.jsx"], "sourcesContent": ["\"use client\";\r\nimport * as React from \"react\"\r\nimport { Slot as SlotPrimitive } from \"radix-ui\"\r\nimport { Controller, FormProvider, useFormContext } from \"react-hook-form\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\nconst FormFieldContext = React.createContext({})\r\n\r\nconst FormField = (\r\n  {\r\n    ...props\r\n  }\r\n) => {\r\n  return (\r\n    (<FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>)\r\n  );\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState, formState } = useFormContext()\r\n\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useForm<PERSON>ield should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\nconst FormItemContext = React.createContext({})\r\n\r\nconst FormItem = React.forwardRef(({ className, ...props }, ref) => {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    (<FormItemContext.Provider value={{ id }}>\r\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\r\n    </FormItemContext.Provider>)\r\n  );\r\n})\r\nFormItem.displayName = \"FormItem\"\r\n\r\nconst FormLabel = React.forwardRef(({ className, ...props }, ref) => {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    (<Label\r\n      ref={ref}\r\n      className={cn(error && \"text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props} />)\r\n  );\r\n})\r\nFormLabel.displayName = \"FormLabel\"\r\n\r\nconst FormControl = React.forwardRef(({ ...props }, ref) => {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    (<SlotPrimitive.Slot\r\n      ref={ref}\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props} />)\r\n  );\r\n})\r\nFormControl.displayName = \"FormControl\"\r\n\r\nconst FormDescription = React.forwardRef(({ className, ...props }, ref) => {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    (<p\r\n      ref={ref}\r\n      id={formDescriptionId}\r\n      className={cn(\"text-[0.8rem] text-muted-foreground\", className)}\r\n      {...props} />)\r\n  );\r\n})\r\nFormDescription.displayName = \"FormDescription\"\r\n\r\nconst FormMessage = React.forwardRef(({ className, children, ...props }, ref) => {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message) : children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    (<p\r\n      ref={ref}\r\n      id={formMessageId}\r\n      className={cn(\"text-[0.8rem] font-medium text-destructive\", className)}\r\n      {...props}>\r\n      {body}\r\n    </p>)\r\n  );\r\n})\r\nFormMessage.displayName = \"FormMessage\"\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;AACA;AACA;AAEA;AACA;AANA;;;;;;;AAQA,MAAM,OAAO,8JAAA,CAAA,eAAY;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,CAAC;AAE9C,MAAM,YAAY,CAChB,EACE,GAAG,OACJ;IAED,qBACG,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACpD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAEA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,CAAC;AAE7C,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1D,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACG,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACrC,cAAA,8OAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC3D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACG,8OAAC,0HAAA,CAAA,QAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB;QAC3C,SAAS;QACR,GAAG,KAAK;;;;;;AAEf;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,GAAG,OAAO,EAAE;IAClD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACG,8OAAC,gMAAA,CAAA,OAAa,CAAC,IAAI;QAClB,KAAK;QACL,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAEf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACjE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACG,8OAAC;QACA,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;QACpD,GAAG,KAAK;;;;;;AAEf;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW;IAE9C,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACG,8OAAC;QACA,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;kBACR;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAmOsB,iBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgJsB,qBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/lib/schemas/propertyFormSchema.jsx"], "sourcesContent": ["import { is } from \"date-fns/locale\";\r\nimport { z } from \"zod\";\r\n\r\nexport const propertyFormSchema = z.object({\r\n  videoUrl: z.string(),\r\n  postType: z.enum([\"sell\", \"rent\"], {\r\n    errorMap: (issue, ctx) => ({ message: 'Cần chọn loại tin đăng.' })\r\n  }),\r\n  propertyType: z.enum([\"nha_rieng\", \"can_ho\", \"nha_tro\"], {\r\n    errorMap: (issue, ctx) => ({ message: 'Cần chọn loại bất động sản.' })\r\n  }),\r\n  price: z\r\n    .number({\r\n      coerce: true,\r\n      required_error: \"<PERSON>i<PERSON> tiền bắt buộc phải điền\",\r\n      invalid_type_error: \"<PERSON>i<PERSON> tiền không hợp lệ\",\r\n    })\r\n    .positive(\"Gi<PERSON> phải là số dương\"),\r\n  cityId: z.string().min(1, \"Cần chọn Tỉnh/Thành phố\"),\r\n  districtId: z.string().min(1, \"<PERSON>ần chọn Quận/Huyện\"),\r\n  wardId: z.string().min(1, \"Cần chọn Phường/Xã\"),\r\n  address: z.string().min(1, \"Thông tin địa chỉ bắt buộc phải điền.\"),\r\n  name: z\r\n    .string({ required_error: \"Vui lòng nhập tối thiểu 30 ký tự.\" })\r\n    .min(30, \"Vui lòng nhập tối thiểu 30 ký tự.\")\r\n    .max(99, \"Vui lòng nhập tối đa 99 ký tự.\"),\r\n  description: z\r\n    .string({ required_error: \"Vui lòng nhập tối thiểu 30 ký tự.\" })\r\n    .min(30, \"Vui lòng nhập tối thiểu 30 ký tự.\")\r\n    .max(3000, \"Vui lòng nhập tối đa 3000 ký tự.\"),\r\n  floors: z\r\n    .number({ coerce: true })\r\n    .optional(),\r\n  rooms: z\r\n    .number({ coerce: true })\r\n    .optional(),\r\n  toilets: z\r\n    .number({ coerce: true })\r\n    .optional(),\r\n  direction: z.string().optional(),\r\n  balconyDirection: z.string().optional(),\r\n  legality: z.string().optional(),\r\n  interior: z.string().optional(),\r\n  width: z\r\n    .number({ coerce: true })\r\n    .optional(),\r\n  roadWidth: z\r\n    .number({ coerce: true })\r\n    .optional(),\r\n  latitude: z.number().optional(),\r\n  longitude: z.number().optional(),\r\n  placeData: z.string().optional(),\r\n  isHighlighted: z.boolean().default(false).optional(),\r\n  isAutoRenew: z.boolean().default(false).optional(),\r\n});\r\n"], "names": [], "mappings": ";;;AACA;;;AAEO,MAAM,qBAAqB,kKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU,kKAAA,CAAA,IAAC,CAAC,MAAM;IAClB,UAAU,kKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;KAAO,EAAE;QACjC,UAAU,CAAC,OAAO,MAAQ,CAAC;gBAAE,SAAS;YAA0B,CAAC;IACnE;IACA,cAAc,kKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAU;KAAU,EAAE;QACvD,UAAU,CAAC,OAAO,MAAQ,CAAC;gBAAE,SAAS;YAA8B,CAAC;IACvE;IACA,OAAO,kKAAA,CAAA,IAAC,CACL,MAAM,CAAC;QACN,QAAQ;QACR,gBAAgB;QAChB,oBAAoB;IACtB,GACC,QAAQ,CAAC;IACZ,QAAQ,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,YAAY,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,QAAQ,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,SAAS,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,MAAM,kKAAA,CAAA,IAAC,CACJ,MAAM,CAAC;QAAE,gBAAgB;IAAoC,GAC7D,GAAG,CAAC,IAAI,qCACR,GAAG,CAAC,IAAI;IACX,aAAa,kKAAA,CAAA,IAAC,CACX,MAAM,CAAC;QAAE,gBAAgB;IAAoC,GAC7D,GAAG,CAAC,IAAI,qCACR,GAAG,CAAC,MAAM;IACb,QAAQ,kKAAA,CAAA,IAAC,CACN,MAAM,CAAC;QAAE,QAAQ;IAAK,GACtB,QAAQ;IACX,OAAO,kKAAA,CAAA,IAAC,CACL,MAAM,CAAC;QAAE,QAAQ;IAAK,GACtB,QAAQ;IACX,SAAS,kKAAA,CAAA,IAAC,CACP,MAAM,CAAC;QAAE,QAAQ;IAAK,GACtB,QAAQ;IACX,WAAW,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,kBAAkB,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACrC,UAAU,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,UAAU,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,OAAO,kKAAA,CAAA,IAAC,CACL,MAAM,CAAC;QAAE,QAAQ;IAAK,GACtB,QAAQ;IACX,WAAW,kKAAA,CAAA,IAAC,CACT,MAAM,CAAC;QAAE,QAAQ;IAAK,GACtB,QAAQ;IACX,UAAU,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,WAAW,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,eAAe,kKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,QAAQ;IAClD,aAAa,kKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,QAAQ;AAClD", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/alert.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg~*]:pl-7\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props} />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm \", className)}\r\n    {...props} />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,mJACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAChE,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAEb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAEb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/collapse.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { ChevronDown, ChevronUp } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst CollapseHeader = ({ title, subTitle, children, defaultOpen = true, className }) => {\r\n  const [isOpen, setIsOpen] = useState(defaultOpen);\r\n  const isOpenRef = useRef(isOpen);\r\n\r\n  const contentRef = useRef(null);\r\n  const [maxHeight, setMaxHeight] = useState(\"0px\");\r\n\r\n  // Keep isOpenRef in sync with isOpen\r\n  useEffect(() => {\r\n    isOpenRef.current = isOpen;\r\n  }, [isOpen]);\r\n\r\n  // Function to update max height based on content\r\n  const updateMaxHeight = () => {\r\n    if (contentRef.current) {\r\n      setMaxHeight(isOpenRef.current ? `${contentRef.current.scrollHeight}px` : \"0px\");\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    updateMaxHeight();\r\n  }, [isOpen]);\r\n\r\n  // Use ResizeObserver to detect content size changes\r\n  useEffect(() => {\r\n    const contentElement = contentRef.current;\r\n    if (!contentElement) return;\r\n\r\n    const resizeObserver = new ResizeObserver((entries) => {\r\n      // Only update if the collapse is open (using ref for latest state)\r\n      if (isOpenRef.current) {\r\n        updateMaxHeight();\r\n      }\r\n    });\r\n\r\n    resizeObserver.observe(contentElement);\r\n\r\n    // Cleanup observer on unmount\r\n    return () => {\r\n      resizeObserver.unobserve(contentElement);\r\n      resizeObserver.disconnect();\r\n    };\r\n  }, []); // Re-run only on mount and unmount\r\n\r\n  return (\r\n    <div className={cn(\"bg-white rounded-md shadow-xs border mb-4\", className)}>\r\n      <button\r\n        type=\"button\"\r\n        className=\"w-full flex justify-between items-center px-4 py-3 hover:text-teal-600 transition-colors\"\r\n        onClick={(e) => {\r\n          e.preventDefault();\r\n          setIsOpen((prev) => !prev);\r\n        }}\r\n        aria-expanded={isOpen}\r\n        aria-controls=\"collapse-content\"\r\n      >\r\n        <h4 className=\"font-semibold text-gray-800 mb-0\">\r\n          {title}\r\n          {subTitle && <span className=\"text-xs ml-2 text-gray-400\">({subTitle})</span>}\r\n        </h4>\r\n        {isOpen ? <ChevronUp className=\"text-gray-500\" size={20} /> : <ChevronDown className=\"text-gray-500\" size={20} />}\r\n      </button>\r\n\r\n      <div\r\n        ref={contentRef}\r\n        style={{ maxHeight }}\r\n        className={cn(`overflow-hidden transition-[max-height] duration-300 ease-in-out`, isOpen ? \"px-4 pb-4\" : \"px-4 pb-0\")}\r\n      >\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CollapseHeader;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,IAAI,EAAE,SAAS,EAAE;IAClF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU,OAAO,GAAG;IACtB,GAAG;QAAC;KAAO;IAEX,iDAAiD;IACjD,MAAM,kBAAkB;QACtB,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,UAAU,OAAO,GAAG,GAAG,WAAW,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG;QAC5E;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAO;IAEX,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,CAAC,gBAAgB;QAErB,MAAM,iBAAiB,IAAI,eAAe,CAAC;YACzC,mEAAmE;YACnE,IAAI,UAAU,OAAO,EAAE;gBACrB;YACF;QACF;QAEA,eAAe,OAAO,CAAC;QAEvB,8BAA8B;QAC9B,OAAO;YACL,eAAe,SAAS,CAAC;YACzB,eAAe,UAAU;QAC3B;IACF,GAAG,EAAE,GAAG,mCAAmC;IAE3C,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;0BAC9D,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,UAAU,CAAC,OAAS,CAAC;gBACvB;gBACA,iBAAe;gBACf,iBAAc;;kCAEd,8OAAC;wBAAG,WAAU;;4BACX;4BACA,0BAAY,8OAAC;gCAAK,WAAU;;oCAA6B;oCAAE;oCAAS;;;;;;;;;;;;;oBAEtE,uBAAS,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAgB,MAAM;;;;;6CAAS,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;wBAAgB,MAAM;;;;;;;;;;;;0BAG7G,8OAAC;gBACC,KAAK;gBACL,OAAO;oBAAE;gBAAU;gBACnB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,CAAC,gEAAgE,CAAC,EAAE,SAAS,cAAc;0BAExG;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/select.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Select as SelectPrimitive } from \"radix-ui\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-xs ring-offset-background placeholder:text-muted-foreground focus:outline-hidden focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 dropdown-select\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\r\n    {...props}>\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\r\n    {...props}>\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-32 overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}>\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\"p-1\", position === \"popper\" &&\r\n          \"h-(--radix-select-trigger-height) w-full min-w-(--radix-select-trigger-width)\")}>\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props} />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-hidden focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props} />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,oMAAA,CAAA,SAAe,CAAC,IAAI;AAEnC,MAAM,cAAc,oMAAA,CAAA,SAAe,CAAC,KAAK;AAEzC,MAAM,cAAc,oMAAA,CAAA,SAAe,CAAC,KAAK;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzE,8OAAC,oMAAA,CAAA,SAAe,CAAC,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6UACA;QAED,GAAG,KAAK;;YACR;0BACD,8OAAC,oMAAA,CAAA,SAAe,CAAC,IAAI;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,oMAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACtE,8OAAC,oMAAA,CAAA,SAAe,CAAC,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBACT,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,oMAAA,CAAA,SAAe,CAAC,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxE,8OAAC,oMAAA,CAAA,SAAe,CAAC,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBACT,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,oMAAA,CAAA,SAAe,CAAC,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC9F,8OAAC,oMAAA,CAAA,SAAe,CAAC,MAAM;kBACrB,cAAA,8OAAC,oMAAA,CAAA,SAAe,CAAC,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BACT,8OAAC;;;;;8BACD,8OAAC,oMAAA,CAAA,SAAe,CAAC,QAAQ;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,OAAO,aAAa,YAChC;8BACD;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,oMAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC,oMAAA,CAAA,SAAe,CAAC,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAEb,YAAY,WAAW,GAAG,oMAAA,CAAA,SAAe,CAAC,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACtE,8OAAC,oMAAA,CAAA,SAAe,CAAC,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2NACA;QAED,GAAG,KAAK;;0BACT,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,oMAAA,CAAA,SAAe,CAAC,aAAa;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,8OAAC,oMAAA,CAAA,SAAe,CAAC,QAAQ;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,oMAAA,CAAA,SAAe,CAAC,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACjE,8OAAC,oMAAA,CAAA,SAAe,CAAC,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAEb,gBAAgB,WAAW,GAAG,oMAAA,CAAA,SAAe,CAAC,SAAS,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/AdditionalInformation.jsx"], "sourcesContent": ["import CollapseHeader from \"@/components/ui/collapse\";\r\nimport { FormControl, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport React from \"react\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nexport default function AdditionalInformation({ form, isFormDisabled }) {\r\n  const t = useTranslations(\"AdditionalInformation\");\r\n\r\n  return (\r\n    <CollapseHeader title={t('detailedInformation')} subTitle={t('optional')}>\r\n      <Separator className=\"mb-6\" />\r\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mt-3\">\r\n        <FormField\r\n          control={form.control}\r\n          name=\"area\"\r\n          className=\"mt-3\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>{t('area')}</FormLabel>\r\n              <FormControl>\r\n                <Input placeholder=\"VD: 100\" {...field} type=\"number\" suffix=\"m²\" disabled={isFormDisabled} readOnly={isFormDisabled} />\r\n              </FormControl>\r\n            </FormItem>\r\n          )}\r\n        />\r\n        <FormField\r\n          control={form.control}\r\n          name=\"floors\"\r\n          className=\"mt-3\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>{t('floors')}</FormLabel>\r\n              <FormControl>\r\n                <Input placeholder=\"VD: 3\" {...field} type=\"number\" disabled={isFormDisabled} readOnly={isFormDisabled} />\r\n              </FormControl>\r\n            </FormItem>\r\n          )}\r\n        />\r\n        <FormField\r\n          control={form.control}\r\n          name=\"rooms\"\r\n          className=\"mt-3\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>{t('rooms')}</FormLabel>\r\n              <FormControl>\r\n                <Input placeholder=\"VD: 3\" {...field} type=\"number\" disabled={isFormDisabled} readOnly={isFormDisabled} />\r\n              </FormControl>\r\n            </FormItem>\r\n          )}\r\n        />\r\n        <FormField\r\n          control={form.control}\r\n          name=\"toilets\"\r\n          className=\"mt-3\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>{t('toilets')}</FormLabel>\r\n              <FormControl>\r\n                <Input placeholder=\"VD: 3\" {...field} type=\"number\" disabled={isFormDisabled} readOnly={isFormDisabled} />\r\n              </FormControl>\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3\">\r\n        <FormField\r\n          control={form.control}\r\n          name=\"direction\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"mt-3\">\r\n              <FormLabel>{t('direction')}</FormLabel>\r\n              <FormControl>\r\n                <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>\r\n                  <SelectTrigger className=\"mt-1\">\r\n                    <SelectValue placeholder={t('selectDirection')} />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"north\">{t('north')}</SelectItem>\r\n                    <SelectItem value=\"south\">{t('south')}</SelectItem>\r\n                    <SelectItem value=\"east\">{t('east')}</SelectItem>\r\n                    <SelectItem value=\"west\">{t('west')}</SelectItem>\r\n                    <SelectItem value=\"northeast\">{t('northeast')}</SelectItem>\r\n                    <SelectItem value=\"northwest\">{t('northwest')}</SelectItem>\r\n                    <SelectItem value=\"southeast\">{t('southeast')}</SelectItem>\r\n                    <SelectItem value=\"southwest\">{t('southwest')}</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n        <FormField\r\n          control={form.control}\r\n          name=\"balconyDirection\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"mt-3\">\r\n              <FormLabel>{t('balconyDirection')}</FormLabel>\r\n              <FormControl>\r\n                <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>\r\n                  <SelectTrigger className=\"mt-1\">\r\n                    <SelectValue placeholder={t('selectBalconyDirection')} />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"north\">{t('north')}</SelectItem>\r\n                    <SelectItem value=\"south\">{t('south')}</SelectItem>\r\n                    <SelectItem value=\"east\">{t('east')}</SelectItem>\r\n                    <SelectItem value=\"west\">{t('west')}</SelectItem>\r\n                    <SelectItem value=\"northeast\">{t('northeast')}</SelectItem>\r\n                    <SelectItem value=\"northwest\">{t('northwest')}</SelectItem>\r\n                    <SelectItem value=\"southeast\">{t('southeast')}</SelectItem>\r\n                    <SelectItem value=\"southwest\">{t('southwest')}</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3\">\r\n        <FormField\r\n          control={form.control}\r\n          name=\"width\"\r\n          className=\"mt-3\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>{t('width')}</FormLabel>\r\n              <FormControl>\r\n                <Input placeholder=\"VD: 3\" {...field} type=\"number\" suffix=\"m\" disabled={isFormDisabled} readOnly={isFormDisabled} />\r\n              </FormControl>\r\n            </FormItem>\r\n          )}\r\n        />\r\n        <FormField\r\n          control={form.control}\r\n          name=\"roadWidth\"\r\n          className=\"mt-3\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>{t('roadWidth')}</FormLabel>\r\n              <FormControl>\r\n                <Input placeholder=\"VD: 3\" {...field} type=\"number\" suffix=\"m\" disabled={isFormDisabled} readOnly={isFormDisabled} />\r\n              </FormControl>\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3\">\r\n        <FormField\r\n          control={form.control}\r\n          name=\"legality\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"mt-3\">\r\n              <FormLabel>{t('legality')}</FormLabel>\r\n              <FormControl>\r\n                <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>\r\n                  <SelectTrigger className=\"mt-1\">\r\n                    <SelectValue placeholder={t('selectLegality')} />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"so_do\">{t('redBook')}</SelectItem>\r\n                    <SelectItem value=\"so_hong\">{t('pinkBook')}</SelectItem>\r\n                    <SelectItem value=\"hdmb\">{t('purchaseContract')}</SelectItem>\r\n                    <SelectItem value=\"khac\">{t('other')}</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n        <FormField\r\n          control={form.control}\r\n          name=\"interior\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"mt-3\">\r\n              <FormLabel>{t('interior')}</FormLabel>\r\n              <FormControl>\r\n                <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>\r\n                  <SelectTrigger className=\"mt-1\">\r\n                    <SelectValue placeholder={t('selectInterior')} />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"day_du\">{t('full')}</SelectItem>\r\n                    <SelectItem value=\"co_ban\">{t('basic')}</SelectItem>\r\n                    <SelectItem value=\"khong\">{t('noInterior')}</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n    </CollapseHeader>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAOA;AACA;AACA;;;;;;;;;AAEe,SAAS,sBAAsB,EAAE,IAAI,EAAE,cAAc,EAAE;IACpE,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC,6HAAA,CAAA,UAAc;QAAC,OAAO,EAAE;QAAwB,UAAU,EAAE;;0BAC3D,8OAAC,8HAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;0BACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,WAAU;wBACV,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kDACP,8OAAC,yHAAA,CAAA,YAAS;kDAAE,EAAE;;;;;;kDACd,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CAAC,aAAY;4CAAW,GAAG,KAAK;4CAAE,MAAK;4CAAS,QAAO;4CAAK,UAAU;4CAAgB,UAAU;;;;;;;;;;;;;;;;;;;;;;kCAK9G,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,WAAU;wBACV,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kDACP,8OAAC,yHAAA,CAAA,YAAS;kDAAE,EAAE;;;;;;kDACd,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CAAC,aAAY;4CAAS,GAAG,KAAK;4CAAE,MAAK;4CAAS,UAAU;4CAAgB,UAAU;;;;;;;;;;;;;;;;;;;;;;kCAKhG,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,WAAU;wBACV,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kDACP,8OAAC,yHAAA,CAAA,YAAS;kDAAE,EAAE;;;;;;kDACd,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CAAC,aAAY;4CAAS,GAAG,KAAK;4CAAE,MAAK;4CAAS,UAAU;4CAAgB,UAAU;;;;;;;;;;;;;;;;;;;;;;kCAKhG,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,WAAU;wBACV,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kDACP,8OAAC,yHAAA,CAAA,YAAS;kDAAE,EAAE;;;;;;kDACd,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CAAC,aAAY;4CAAS,GAAG,KAAK;4CAAE,MAAK;4CAAS,UAAU;4CAAgB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlG,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,yHAAA,CAAA,YAAS;kDAAE,EAAE;;;;;;kDACd,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,eAAe,MAAM,QAAQ;4CAAE,cAAc,MAAM,KAAK;4CAAE,UAAU;;8DAC1E,8OAAC,2HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;wDAAC,aAAa,EAAE;;;;;;;;;;;8DAE9B,8OAAC,2HAAA,CAAA,gBAAa;;sEACZ,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS,EAAE;;;;;;sEAC7B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS,EAAE;;;;;;sEAC7B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ,EAAE;;;;;;sEAC5B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ,EAAE;;;;;;sEAC5B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa,EAAE;;;;;;sEACjC,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa,EAAE;;;;;;sEACjC,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa,EAAE;;;;;;sEACjC,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAIvC,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAIlB,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,yHAAA,CAAA,YAAS;kDAAE,EAAE;;;;;;kDACd,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,eAAe,MAAM,QAAQ;4CAAE,cAAc,MAAM,KAAK;4CAAE,UAAU;;8DAC1E,8OAAC,2HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;wDAAC,aAAa,EAAE;;;;;;;;;;;8DAE9B,8OAAC,2HAAA,CAAA,gBAAa;;sEACZ,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS,EAAE;;;;;;sEAC7B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS,EAAE;;;;;;sEAC7B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ,EAAE;;;;;;sEAC5B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ,EAAE;;;;;;sEAC5B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa,EAAE;;;;;;sEACjC,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa,EAAE;;;;;;sEACjC,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa,EAAE;;;;;;sEACjC,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAIvC,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0BAKpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,WAAU;wBACV,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kDACP,8OAAC,yHAAA,CAAA,YAAS;kDAAE,EAAE;;;;;;kDACd,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CAAC,aAAY;4CAAS,GAAG,KAAK;4CAAE,MAAK;4CAAS,QAAO;4CAAI,UAAU;4CAAgB,UAAU;;;;;;;;;;;;;;;;;;;;;;kCAK3G,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,WAAU;wBACV,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kDACP,8OAAC,yHAAA,CAAA,YAAS;kDAAE,EAAE;;;;;;kDACd,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CAAC,aAAY;4CAAS,GAAG,KAAK;4CAAE,MAAK;4CAAS,QAAO;4CAAI,UAAU;4CAAgB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM7G,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,yHAAA,CAAA,YAAS;kDAAE,EAAE;;;;;;kDACd,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,eAAe,MAAM,QAAQ;4CAAE,cAAc,MAAM,KAAK;4CAAE,UAAU;;8DAC1E,8OAAC,2HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;wDAAC,aAAa,EAAE;;;;;;;;;;;8DAE9B,8OAAC,2HAAA,CAAA,gBAAa;;sEACZ,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS,EAAE;;;;;;sEAC7B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW,EAAE;;;;;;sEAC/B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ,EAAE;;;;;;sEAC5B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAIlC,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAIlB,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,yHAAA,CAAA,YAAS;kDAAE,EAAE;;;;;;kDACd,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,eAAe,MAAM,QAAQ;4CAAE,cAAc,MAAM,KAAK;4CAAE,UAAU;;8DAC1E,8OAAC,2HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;wDAAC,aAAa,EAAE;;;;;;;;;;;8DAE9B,8OAAC,2HAAA,CAAA,gBAAa;;sEACZ,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU,EAAE;;;;;;sEAC9B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU,EAAE;;;;;;sEAC9B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAInC,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B", "debugId": null}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/textarea.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Textarea = React.forwardRef(({ className, ...props }, ref) => {\r\n  return (\r\n    (<textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-xs placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props} />)\r\n  );\r\n})\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1D,qBACG,8OAAC;QACA,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAEf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/PropertyPostInformation.jsx"], "sourcesContent": ["import CollapseHeader from \"@/components/ui/collapse\";\r\nimport { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nexport default function PropertyPostInformation({ form, isFormDisabled }) {\r\n  const t = useTranslations(\"PropertyPostInformation\");  \r\n\r\n  return (\r\n    <CollapseHeader title={t('postInformation')} subTitle={t('requiredInformation')}>\r\n      <Separator className=\"mb-6\" />\r\n      <FormField\r\n        control={form.control}\r\n        name=\"name\"\r\n        className=\"mt-3\"\r\n        render={({ field }) => (\r\n          <FormItem>\r\n            <FormLabel>{t('title')}</FormLabel>\r\n            <FormControl>\r\n              <Input\r\n                placeholder={t('titlePlaceholder')}\r\n                {...field}\r\n                disabled={isFormDisabled}\r\n                readOnly={isFormDisabled}\r\n              />\r\n            </FormControl>\r\n            <FormDescription className=\"text-xs\">\r\n              {t('titleDescription')}\r\n            </FormDescription>\r\n            <FormMessage />\r\n          </FormItem>\r\n        )}\r\n      />\r\n      <FormField\r\n        control={form.control}\r\n        name=\"description\"\r\n        className=\"mt-3\"\r\n        render={({ field }) => (\r\n          <FormItem className=\"mt-3\">\r\n            <FormLabel>{t('introduction')}</FormLabel>\r\n            <FormControl>\r\n              <Textarea\r\n                placeholder={t('descriptionPlaceholder')}\r\n                {...field}\r\n                rows={10}\r\n                disabled={isFormDisabled}\r\n                readOnly={isFormDisabled}\r\n              />\r\n            </FormControl>\r\n            <FormDescription className=\"text-xs\">\r\n              {t('descriptionDescription')}\r\n            </FormDescription>\r\n            <FormMessage />\r\n          </FormItem>\r\n        )}\r\n      />\r\n    </CollapseHeader>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS,wBAAwB,EAAE,IAAI,EAAE,cAAc,EAAE;IACtE,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC,6HAAA,CAAA,UAAc;QAAC,OAAO,EAAE;QAAoB,UAAU,EAAE;;0BACvD,8OAAC,8HAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;0BACrB,8OAAC,yHAAA,CAAA,YAAS;gBACR,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,WAAU;gBACV,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;0CACP,8OAAC,yHAAA,CAAA,YAAS;0CAAE,EAAE;;;;;;0CACd,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;oCACJ,aAAa,EAAE;oCACd,GAAG,KAAK;oCACT,UAAU;oCACV,UAAU;;;;;;;;;;;0CAGd,8OAAC,yHAAA,CAAA,kBAAe;gCAAC,WAAU;0CACxB,EAAE;;;;;;0CAEL,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0BAIlB,8OAAC,yHAAA,CAAA,YAAS;gBACR,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,WAAU;gBACV,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,yHAAA,CAAA,YAAS;0CAAE,EAAE;;;;;;0CACd,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,6HAAA,CAAA,WAAQ;oCACP,aAAa,EAAE;oCACd,GAAG,KAAK;oCACT,MAAM;oCACN,UAAU;oCACV,UAAU;;;;;;;;;;;0CAGd,8OAAC,yHAAA,CAAA,kBAAe;gCAAC,WAAU;0CACxB,EAAE;;;;;;0CAEL,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;AAMxB", "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAqXsB,uBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get property report data from PropertyEngagementSummary\r\nexport async function getPropertyReportById(propertyId) {\r\n\r\n  try {\r\n    // Call the real API endpoint to get property engagement summary\r\n    const response = await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/property/${propertyId}/summary`);\r\n\r\n    if (!response.success) {\r\n      console.error(`API error for propertyId ${propertyId}:`, response);\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n        errorType: response.errorType || \"api_error\",\r\n      };\r\n    }\r\n\r\n    // The API returns data in the format expected by the frontend\r\n    return {\r\n      success: true,\r\n      data: response.data,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA+gBsB,sBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1670, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/badge.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 h-5 ml-0 mt-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground shadow-sm\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground shadow-sm\",\r\n        outline: \"text-foreground\",\r\n        ghost: \"\",\r\n        primary: \"bg-primary text-primary-foreground\",\r\n      },\r\n      rounded: {\r\n        default: \"rounded-md\",\r\n        full: \"rounded-full\",\r\n      },\r\n      height: {\r\n        default: \"h-5\",\r\n        sm: \"h-4\",\r\n        fit: \"h-fit\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      rounded: \"default\",\r\n      height: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  rounded,\r\n  height,\r\n  ...props\r\n}) {\r\n  return (<div className={cn(badgeVariants({ variant, rounded, height }), className)} {...props} />);\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,OAAO;YACP,SAAS;QACX;QACA,SAAS;YACP,SAAS;YACT,MAAM;QACR;QACA,QAAQ;YACN,SAAS;YACT,IAAI;YACJ,KAAK;QACP;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;QACT,QAAQ;IACV;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,OAAO,EACP,MAAM,EACN,GAAG,OACJ;IACC,qBAAQ,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;YAAS;QAAO,IAAI;QAAa,GAAG,KAAK;;;;;;AAC/F", "debugId": null}}, {"offset": {"line": 1729, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/dialog.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Dialog as DialogPrimitive } from \"radix-ui\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      {children}\r\n      <DialogPrimitive.Close\r\n        className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)}\r\n    {...props} />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className)}\r\n    {...props} />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,oMAAA,CAAA,SAAe,CAAC,IAAI;AAEnC,MAAM,gBAAgB,oMAAA,CAAA,SAAe,CAAC,OAAO;AAE7C,MAAM,eAAe,oMAAA,CAAA,SAAe,CAAC,MAAM;AAE3C,MAAM,cAAc,oMAAA,CAAA,SAAe,CAAC,KAAK;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC/D,8OAAC,oMAAA,CAAA,SAAe,CAAC,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAEb,cAAc,WAAW,GAAG,oMAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,oMAAA,CAAA,SAAe,CAAC,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBACR;kCACD,8OAAC,oMAAA,CAAA,SAAe,CAAC,KAAK;wBACpB,WAAU;;0CACV,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,oMAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;AAEb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;;;;;;AAEb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC,oMAAA,CAAA,SAAe,CAAC,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAEb,YAAY,WAAW,GAAG,oMAAA,CAAA,SAAe,CAAC,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACnE,8OAAC,oMAAA,CAAA,SAAe,CAAC,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,kBAAkB,WAAW,GAAG,oMAAA,CAAA,SAAe,CAAC,WAAW,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1861, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/ImageCard.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { memo } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Star, Trash2 } from \"lucide-react\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\n// Create a memoized image card component outside the main component\r\nconst ImageCard = memo(\r\n  ({\r\n    file,\r\n    caption,\r\n    isAvatar,\r\n    onCaptionChange,\r\n    onSetAvatar,\r\n    onDelete,\r\n    onClick,\r\n    showMoreCount = 0,\r\n    isFormDisabled,\r\n  }) => {\r\n    const t = useTranslations(\"PropertyImageUploader\"); // Assuming translations are needed here, might need adjustment\r\n    return (\r\n      <div className={`border rounded-md overflow-hidden cursor-pointer'}`} onClick={onClick}>\r\n        <div className=\"relative h-[160px] w-full\">\r\n          <Image\r\n            src={file.mediaURL || \"/placeholder.svg\"}\r\n            alt={caption || \"Property image\"}\r\n            fill\r\n            className=\"object-cover\"\r\n            loading=\"lazy\"\r\n            sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n          />\r\n\r\n          {isAvatar && (\r\n            <div className=\"absolute top-2 left-2 z-10\">\r\n              <Badge rounded=\"full\" className=\"bg-primary text-white\">{t(\"avatar\")}</Badge>\r\n            </div>\r\n          )}\r\n\r\n          {showMoreCount > 0 && (\r\n            <div className=\"absolute inset-0 bg-black/60 flex items-center justify-center\">\r\n              <span className=\"text-white text-2xl font-bold\">+{showMoreCount}</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {!isFormDisabled && (\r\n          <div className=\"p-2 space-y-2\">\r\n            <Input\r\n              placeholder={t(\"addCaptionPlaceholder\")} // Using translation key\r\n              value={caption || \"\"}\r\n              onChange={(e) => onCaptionChange(file.id, e.target.value)}\r\n              className=\"text-sm h-8\"\r\n              onClick={(e) => e.stopPropagation()} // Keep stopPropagation\r\n              disabled={isFormDisabled}\r\n            />\r\n            <div className=\"flex justify-between\">\r\n              <Button\r\n                type=\"button\"\r\n                size=\"sm\"\r\n                variant=\"ghost\"\r\n                className=\"h-8 w-8 p-0\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation(); // Keep stopPropagation\r\n                  e.preventDefault(); // Prevent form submission\r\n                  if (!isFormDisabled) onSetAvatar(file.id);\r\n                }}\r\n                disabled={isAvatar || isFormDisabled}\r\n                title={t(\"setAsAvatar\")}\r\n              >\r\n                <Star className={`w-4 h-4 ${isAvatar ? \"fill-primary text-primary\" : \"\"}`} />\r\n              </Button>\r\n              <Button\r\n                type=\"button\"\r\n                size=\"sm\"\r\n                variant=\"ghost\"\r\n                className=\"h-8 w-8 p-0 text-red-500 hover:text-red-700\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation(); // Keep stopPropagation\r\n                  e.preventDefault(); // Prevent form submission\r\n                  if (!isFormDisabled) onDelete(file.id);\r\n                }}\r\n \r\n                disabled={isFormDisabled}\r\n              >\r\n                <Trash2 className=\"w-4 h-4\" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n);\r\nImageCard.displayName = \"ImageCard\";\r\n\r\nexport default ImageCard;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AARA;;;;;;;;;AAUA,oEAAoE;AACpE,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EACnB,CAAC,EACC,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,eAAe,EACf,WAAW,EACX,QAAQ,EACR,OAAO,EACP,gBAAgB,CAAC,EACjB,cAAc,EACf;IACC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE,0BAA0B,+DAA+D;IACnH,qBACE,8OAAC;QAAI,WAAW,CAAC,kDAAkD,CAAC;QAAE,SAAS;;0BAC7E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,KAAK,QAAQ,IAAI;wBACtB,KAAK,WAAW;wBAChB,IAAI;wBACJ,WAAU;wBACV,SAAQ;wBACR,OAAM;;;;;;oBAGP,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAO,WAAU;sCAAyB,EAAE;;;;;;;;;;;oBAI9D,gBAAgB,mBACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;;gCAAgC;gCAAE;;;;;;;;;;;;;;;;;;YAKvD,CAAC,gCACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0HAAA,CAAA,QAAK;wBACJ,aAAa,EAAE;wBACf,OAAO,WAAW;wBAClB,UAAU,CAAC,IAAM,gBAAgB,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBACxD,WAAU;wBACV,SAAS,CAAC,IAAM,EAAE,eAAe;wBACjC,UAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe,IAAI,uBAAuB;oCAC5C,EAAE,cAAc,IAAI,0BAA0B;oCAC9C,IAAI,CAAC,gBAAgB,YAAY,KAAK,EAAE;gCAC1C;gCACA,UAAU,YAAY;gCACtB,OAAO,EAAE;0CAET,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,8BAA8B,IAAI;;;;;;;;;;;0CAE3E,8OAAC,2HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe,IAAI,uBAAuB;oCAC5C,EAAE,cAAc,IAAI,0BAA0B;oCAC9C,IAAI,CAAC,gBAAgB,SAAS,KAAK,EAAE;gCACvC;gCAEA,UAAU;0CAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC;AAEF,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/PropertyImageUploader.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useCallback, useEffect, memo } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport {\r\n  uploadPropertyImages,\r\n  updatePropertyMediaCaption,\r\n  updatePropertyMediaIsAvatar,\r\n  deletePropertyMedia\r\n} from \"@/app/actions/server/property\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { Upload, Loader2 } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport ImageCard from \"@/components/ui/ImageCard\";\r\n\r\nconst PropertyImageUploader = ({ propertyId, onUploadComplete, initialImages = [], isFormDisabled }) => {\r\n  const [uploadedFiles, setUploadedFiles] = useState([]);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [avatarImageId, setAvatarImageId] = useState(null);\r\n  const [captions, setCaptions] = useState({});\r\n  const [showImageGallery, setShowImageGallery] = useState(false);\r\n  const { toast } = useToast();\r\n  const t = useTranslations(\"PropertyImageUploader\");\r\n\r\n  const [debouncedCaptionId, setDebouncedCaptionId] = useState(null);\r\n\r\n  useEffect(() => {\r\n    if (initialImages && initialImages.length > 0) {\r\n      // Only update if the arrays are different\r\n      if (JSON.stringify(initialImages) !== JSON.stringify(uploadedFiles)) {\r\n        setUploadedFiles(initialImages);\r\n\r\n        // Initialize captions\r\n        const initialCaptions = {};\r\n        initialImages.forEach((img) => {\r\n          if (img.caption) {\r\n            initialCaptions[img.id] = img.caption;\r\n          }\r\n          // Set avatar image if found\r\n          if (img.isAvatar) {\r\n            setAvatarImageId(img.id);\r\n          }\r\n        });\r\n\r\n        setCaptions(initialCaptions);\r\n\r\n        // Notify parent component about the initial images\r\n        if (onUploadComplete) {\r\n          onUploadComplete(initialImages);\r\n        }\r\n      }\r\n    }\r\n  }, [initialImages, onUploadComplete]);\r\n\r\n  const onDrop = useCallback(\r\n    async (acceptedFiles) => {\r\n      setIsUploading(true);\r\n\r\n      // Validate files before uploading\r\n      const MAX_FILE_SIZE = 1048576; // 1MB in bytes\r\n      const validFiles = [];\r\n      const invalidFiles = [];\r\n\r\n      // Check each file for type and size\r\n      for (const file of acceptedFiles) {\r\n        // Check if file is an image\r\n        if (!file.type.startsWith('image/')) {\r\n          invalidFiles.push({ file, reason: 'type' });\r\n          continue;\r\n        }\r\n\r\n        // Check file size\r\n        if (file.size > MAX_FILE_SIZE) {\r\n          invalidFiles.push({ file, reason: 'size' });\r\n          continue;\r\n        }\r\n\r\n        // File passed all checks\r\n        validFiles.push(file);\r\n      }\r\n\r\n      // Show error messages for invalid files\r\n      if (invalidFiles.length > 0) {\r\n        const typeErrors = invalidFiles.filter(item => item.reason === 'type');\r\n        const sizeErrors = invalidFiles.filter(item => item.reason === 'size');\r\n\r\n        if (typeErrors.length > 0) {\r\n          toast({\r\n            title: t('error') || 'Error',\r\n            description: typeErrors.length === 1\r\n              ? `${typeErrors[0].file.name} is not a valid image file.`\r\n              : `${typeErrors.length} files are not valid image files.`,\r\n            variant: \"destructive\",\r\n            className: \"bg-red-600 text-white\",\r\n          });\r\n        }\r\n\r\n        if (sizeErrors.length > 0) {\r\n          toast({\r\n            title: t('error') || 'Error',\r\n            description: sizeErrors.length === 1\r\n              ? `${sizeErrors[0].file.name} exceeds the 1MB size limit.`\r\n              : `${sizeErrors.length} files exceed the 1MB size limit.`,\r\n            variant: \"destructive\",\r\n            className: \"bg-red-600 text-white\",\r\n          });\r\n        }\r\n\r\n        // If no valid files, stop the upload process\r\n        if (validFiles.length === 0) {\r\n          setIsUploading(false);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const formData = new FormData();\r\n      formData.append(\"propertyId\", propertyId ?? \"\");\r\n      validFiles.forEach((file) => formData.append(\"files\", file));\r\n\r\n      try {\r\n        const result = await uploadPropertyImages(null, formData);\r\n\r\n        if (!result.success) {\r\n          throw new Error(result.message);\r\n        }\r\n\r\n        // Add unique IDs to the uploaded files\r\n        const filesWithIds = result?.data?.map((file) => ({\r\n          ...file,\r\n          caption: captions[file.id] || null,\r\n          isAvatar: file.id === avatarImageId,\r\n        }));\r\n\r\n        // Update state with new files\r\n        const updatedFiles = [...uploadedFiles, ...filesWithIds];\r\n        setUploadedFiles(updatedFiles);\r\n\r\n        toast({\r\n          description: \"Tải lên thành công!\",\r\n          className: \"bg-teal-600 text-white\",\r\n        });\r\n\r\n        // Call the parent callback with the updated files\r\n        if (onUploadComplete) {\r\n          onUploadComplete(updatedFiles);\r\n        }\r\n      } catch (error) {\r\n        // Check if it's the size limit error\r\n        if (error.message && (error.message.includes('Body exceeded') || error.message.includes('limit'))) {\r\n          toast({\r\n            title: t('submissionErrorTitle'),\r\n            description: t('submissionErrorSizeLimit'),\r\n            variant: \"destructive\",\r\n            className: \"bg-red-600 text-white\",\r\n          });\r\n        } else {\r\n          // Show generic error for other issues\r\n          toast({\r\n            title: t('error'),\r\n            description: error.message || \"An unknown error occurred during upload.\",\r\n            variant: \"destructive\",\r\n            className: \"bg-red-600 text-white\",\r\n          });\r\n        }\r\n      } finally {\r\n        setIsUploading(false);\r\n      }\r\n    },\r\n    [propertyId, toast, uploadedFiles, captions, avatarImageId, onUploadComplete, t]\r\n  );\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    multiple: true,\r\n    accept: {\r\n      \"image/*\": [\".jpeg\", \".png\", \".jpg\", \".webp\"],\r\n    },\r\n    disabled: isFormDisabled,\r\n  });\r\n\r\n  const handleSetAvatar = (fileId) => {\r\n    setAvatarImageId(fileId);\r\n\r\n    // Update the isAvatar property for all files\r\n    const updatedFiles = uploadedFiles.map((file) => ({\r\n      ...file,\r\n      isAvatar: file.id === fileId,\r\n    }));\r\n\r\n    setUploadedFiles(updatedFiles);\r\n\r\n    // Notify parent component\r\n    if (onUploadComplete) {\r\n      onUploadComplete(updatedFiles);\r\n    }\r\n  };\r\n\r\n  const handleDeleteImage = async (fileId) => {\r\n    try {\r\n      // Call the API to delete the image\r\n      const result = await deletePropertyMedia(fileId);\r\n\r\n      if (!result.success) {\r\n        throw new Error(result.message || 'Failed to delete image');\r\n      }\r\n\r\n      const updatedFiles = uploadedFiles.filter((file) => file.id !== fileId);\r\n      setUploadedFiles(updatedFiles);\r\n\r\n      // If we're deleting the avatar image, clear the avatar\r\n      if (avatarImageId === fileId) {\r\n        setAvatarImageId(null);\r\n      }\r\n\r\n      // Remove the caption for this image\r\n      const newCaptions = { ...captions };\r\n      delete newCaptions[fileId];\r\n      setCaptions(newCaptions);\r\n\r\n      // Notify parent component\r\n      if (onUploadComplete) {\r\n        onUploadComplete(updatedFiles);\r\n      }\r\n\r\n      toast({\r\n        description: t('deleteImageSuccess'),\r\n        className: \"bg-teal-600 text-white\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        title: t('error') || 'Error',\r\n        description: error.message || 'Failed to delete image',\r\n        variant: \"destructive\",\r\n        className: \"bg-red-600 text-white\",\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleCaptionChange = (fileId, caption) => {\r\n    // Only update the captions state (lightweight operation)\r\n    setCaptions((prev) => ({\r\n      ...prev,\r\n      [fileId]: caption,\r\n    }));\r\n\r\n    // Set a debounce identifier\r\n    if (debouncedCaptionId) clearTimeout(debouncedCaptionId);\r\n\r\n    // Debounce the heavy operations\r\n    const timeoutId = setTimeout(() => {\r\n      // Update uploadedFiles and notify parent only after typing stops\r\n      const updatedFiles = uploadedFiles.map((file) => {\r\n        if (file.id === fileId) {\r\n          return { ...file, caption };\r\n        }\r\n        return file;\r\n      });\r\n\r\n      setUploadedFiles(updatedFiles);\r\n\r\n      // Notify parent component\r\n      if (onUploadComplete) {\r\n        onUploadComplete(updatedFiles);\r\n      }\r\n    }, 500); // 500ms debounce\r\n\r\n    setDebouncedCaptionId(timeoutId);\r\n  };\r\n\r\n  // Clean up timeout on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (debouncedCaptionId) clearTimeout(debouncedCaptionId);\r\n    };\r\n  }, [debouncedCaptionId]);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Conditionally render the dropzone */}\r\n      {!isFormDisabled && (\r\n        <div\r\n          {...getRootProps()}\r\n          className={`\r\n            relative flex flex-col items-center justify-center\r\n            border-2 border-dashed rounded-lg p-8\r\n            transition-colors duration-200 ease-in-out\r\n            ${\r\n              isDragActive\r\n                ? \"border-primary bg-primary/5\"\r\n                : \"border-gray-300 hover:border-primary hover:bg-gray-50\"\r\n            }\r\n          `}\r\n        >\r\n          <div className=\"flex flex-col items-center text-center space-y-2\">\r\n            <div className=\"p-3 rounded-full bg-primary/10\">\r\n              <Upload className=\"w-8 h-8 text-primary\" />\r\n            </div>\r\n            <input {...getInputProps()} />\r\n\r\n            <div className=\"space-y-1\">\r\n              <h3 className=\"text-lg font-medium\">\r\n                {isDragActive ? t('dropHere') : t('uploadPropertyImage')}\r\n              </h3>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                {t('dragAndDrop')}\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"mt-2 text-xs text-muted-foreground\">\r\n              {t('supportedFormats')}\r\n            </div>\r\n            <div className=\"text-xs text-muted-foreground\">\r\n              {t('maxFileSize') || 'Maximum file size: 1MB'}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {isUploading && (\r\n        <div className=\"flex items-center justify-center p-4 bg-primary/10 rounded-lg\">\r\n          <Loader2 className=\"w-5 h-5 text-primary mr-2 animate-spin\" />\r\n          <span className=\"font-medium text-primary\">{t('uploadingImage')}</span>\r\n        </div>\r\n      )}\r\n\r\n      {uploadedFiles.length > 0 && (\r\n        <div className=\"space-y-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h2 className=\"text-xl font-semibold\">{t('uploadedImages')}</h2>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Badge rounded=\"full\" variant=\"outline\" className=\"px-2 py-1\">\r\n                {uploadedFiles.length} {uploadedFiles.length === 1 ? t('image') : t('images')}\r\n              </Badge>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  e.preventDefault();\r\n                  setShowImageGallery(true);\r\n                }}\r\n                disabled={isFormDisabled}\r\n              >\r\n                {t('edit')} ({uploadedFiles.length})\r\n              </Button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Combined Image Gallery and Management */}\r\n          <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4\">\r\n            {uploadedFiles.slice(0, 8).map((file, index) => (\r\n              <ImageCard\r\n                key={file.id}\r\n                file={file}\r\n                caption={captions[file.id] || file.caption || \"\"}\r\n                isAvatar={avatarImageId === file.id}\r\n                onCaptionChange={handleCaptionChange}\r\n                onSetAvatar={handleSetAvatar}\r\n                onDelete={handleDeleteImage}\r\n                onClick={() => {\r\n                  setShowImageGallery(true);\r\n                }}\r\n                showMoreCount={index === 7 && uploadedFiles.length > 8 ? uploadedFiles.length - 8 : 0}\r\n                isFormDisabled={isFormDisabled}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Updated Image Gallery Dialog */}\r\n      <Dialog open={showImageGallery} onOpenChange={setShowImageGallery}>\r\n        <DialogContent className=\"max-w-5xl h-[80vh] flex flex-col\">\r\n          <DialogHeader>\r\n            <DialogTitle className=\"flex justify-between items-center\">\r\n              <span>{t('manageImages')} ({uploadedFiles.length})</span>\r\n            </DialogTitle>\r\n          </DialogHeader>\r\n\r\n          <div className=\"grow overflow-y-auto p-4\">\r\n            <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4\">\r\n              {uploadedFiles.map((file) => (\r\n                <ImageCard\r\n                  key={file.id}\r\n                  file={file}\r\n                  caption={captions[file.id] || file.caption || \"\"}\r\n                  isAvatar={avatarImageId === file.id}\r\n                  onCaptionChange={handleCaptionChange}\r\n                  onSetAvatar={handleSetAvatar}\r\n                  onDelete={handleDeleteImage}\r\n                  showMoreCount={0}\r\n                  isFormDisabled={isFormDisabled}\r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PropertyImageUploader;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAMA;AACA;AAAA;AACA;AACA;AACA;AAMA;AACA;AArBA;;;;;;;;;;;;AAuBA,MAAM,wBAAwB,CAAC,EAAE,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,EAAE,cAAc,EAAE;IACjG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;YAC7C,0CAA0C;YAC1C,IAAI,KAAK,SAAS,CAAC,mBAAmB,KAAK,SAAS,CAAC,gBAAgB;gBACnE,iBAAiB;gBAEjB,sBAAsB;gBACtB,MAAM,kBAAkB,CAAC;gBACzB,cAAc,OAAO,CAAC,CAAC;oBACrB,IAAI,IAAI,OAAO,EAAE;wBACf,eAAe,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,OAAO;oBACvC;oBACA,4BAA4B;oBAC5B,IAAI,IAAI,QAAQ,EAAE;wBAChB,iBAAiB,IAAI,EAAE;oBACzB;gBACF;gBAEA,YAAY;gBAEZ,mDAAmD;gBACnD,IAAI,kBAAkB;oBACpB,iBAAiB;gBACnB;YACF;QACF;IACF,GAAG;QAAC;QAAe;KAAiB;IAEpC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvB,OAAO;QACL,eAAe;QAEf,kCAAkC;QAClC,MAAM,gBAAgB,SAAS,eAAe;QAC9C,MAAM,aAAa,EAAE;QACrB,MAAM,eAAe,EAAE;QAEvB,oCAAoC;QACpC,KAAK,MAAM,QAAQ,cAAe;YAChC,4BAA4B;YAC5B,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,aAAa,IAAI,CAAC;oBAAE;oBAAM,QAAQ;gBAAO;gBACzC;YACF;YAEA,kBAAkB;YAClB,IAAI,KAAK,IAAI,GAAG,eAAe;gBAC7B,aAAa,IAAI,CAAC;oBAAE;oBAAM,QAAQ;gBAAO;gBACzC;YACF;YAEA,yBAAyB;YACzB,WAAW,IAAI,CAAC;QAClB;QAEA,wCAAwC;QACxC,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,MAAM,aAAa,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;YAC/D,MAAM,aAAa,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;YAE/D,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,MAAM;oBACJ,OAAO,EAAE,YAAY;oBACrB,aAAa,WAAW,MAAM,KAAK,IAC/B,GAAG,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,GACvD,GAAG,WAAW,MAAM,CAAC,iCAAiC,CAAC;oBAC3D,SAAS;oBACT,WAAW;gBACb;YACF;YAEA,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,MAAM;oBACJ,OAAO,EAAE,YAAY;oBACrB,aAAa,WAAW,MAAM,KAAK,IAC/B,GAAG,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,GACxD,GAAG,WAAW,MAAM,CAAC,iCAAiC,CAAC;oBAC3D,SAAS;oBACT,WAAW;gBACb;YACF;YAEA,6CAA6C;YAC7C,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,eAAe;gBACf;YACF;QACF;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,cAAc,cAAc;QAC5C,WAAW,OAAO,CAAC,CAAC,OAAS,SAAS,MAAM,CAAC,SAAS;QAEtD,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM;YAEhD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO;YAChC;YAEA,uCAAuC;YACvC,MAAM,eAAe,QAAQ,MAAM,IAAI,CAAC,OAAS,CAAC;oBAChD,GAAG,IAAI;oBACP,SAAS,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI;oBAC9B,UAAU,KAAK,EAAE,KAAK;gBACxB,CAAC;YAED,8BAA8B;YAC9B,MAAM,eAAe;mBAAI;mBAAkB;aAAa;YACxD,iBAAiB;YAEjB,MAAM;gBACJ,aAAa;gBACb,WAAW;YACb;YAEA,kDAAkD;YAClD,IAAI,kBAAkB;gBACpB,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,qCAAqC;YACrC,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,oBAAoB,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ,GAAG;gBACjG,MAAM;oBACJ,OAAO,EAAE;oBACT,aAAa,EAAE;oBACf,SAAS;oBACT,WAAW;gBACb;YACF,OAAO;gBACL,sCAAsC;gBACtC,MAAM;oBACJ,OAAO,EAAE;oBACT,aAAa,MAAM,OAAO,IAAI;oBAC9B,SAAS;oBACT,WAAW;gBACb;YACF;QACF,SAAU;YACR,eAAe;QACjB;IACF,GACA;QAAC;QAAY;QAAO;QAAe;QAAU;QAAe;QAAkB;KAAE;IAGlF,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,UAAU;QACV,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;aAAQ;QAC/C;QACA,UAAU;IACZ;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QAEjB,6CAA6C;QAC7C,MAAM,eAAe,cAAc,GAAG,CAAC,CAAC,OAAS,CAAC;gBAChD,GAAG,IAAI;gBACP,UAAU,KAAK,EAAE,KAAK;YACxB,CAAC;QAED,iBAAiB;QAEjB,0BAA0B;QAC1B,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,mCAAmC;YACnC,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,sBAAmB,AAAD,EAAE;YAEzC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,MAAM,eAAe,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;YAChE,iBAAiB;YAEjB,uDAAuD;YACvD,IAAI,kBAAkB,QAAQ;gBAC5B,iBAAiB;YACnB;YAEA,oCAAoC;YACpC,MAAM,cAAc;gBAAE,GAAG,QAAQ;YAAC;YAClC,OAAO,WAAW,CAAC,OAAO;YAC1B,YAAY;YAEZ,0BAA0B;YAC1B,IAAI,kBAAkB;gBACpB,iBAAiB;YACnB;YAEA,MAAM;gBACJ,aAAa,EAAE;gBACf,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO,EAAE,YAAY;gBACrB,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;gBACT,WAAW;YACb;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC,QAAQ;QACnC,yDAAyD;QACzD,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;YACZ,CAAC;QAED,4BAA4B;QAC5B,IAAI,oBAAoB,aAAa;QAErC,gCAAgC;QAChC,MAAM,YAAY,WAAW;YAC3B,iEAAiE;YACjE,MAAM,eAAe,cAAc,GAAG,CAAC,CAAC;gBACtC,IAAI,KAAK,EAAE,KAAK,QAAQ;oBACtB,OAAO;wBAAE,GAAG,IAAI;wBAAE;oBAAQ;gBAC5B;gBACA,OAAO;YACT;YAEA,iBAAiB;YAEjB,0BAA0B;YAC1B,IAAI,kBAAkB;gBACpB,iBAAiB;YACnB;QACF,GAAG,MAAM,iBAAiB;QAE1B,sBAAsB;IACxB;IAEA,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,oBAAoB,aAAa;QACvC;IACF,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,CAAC,gCACA,8OAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;;;YAIV,EACE,eACI,gCACA,wDACL;UACH,CAAC;0BAED,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,8OAAC;4BAAO,GAAG,eAAe;;;;;;sCAE1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,eAAe,EAAE,cAAc,EAAE;;;;;;8CAEpC,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;sCACZ,EAAE;;;;;;sCAEL,8OAAC;4BAAI,WAAU;sCACZ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;YAM5B,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAA4B,EAAE;;;;;;;;;;;;YAIjD,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyB,EAAE;;;;;;0CACzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAO,SAAQ;wCAAU,WAAU;;4CAC/C,cAAc,MAAM;4CAAC;4CAAE,cAAc,MAAM,KAAK,IAAI,EAAE,WAAW,EAAE;;;;;;;kDAEtE,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,EAAE,cAAc;4CAChB,oBAAoB;wCACtB;wCACA,UAAU;;4CAET,EAAE;4CAAQ;4CAAG,cAAc,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAMzC,8OAAC;wBAAI,WAAU;kCACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACpC,8OAAC,8HAAA,CAAA,UAAS;gCAER,MAAM;gCACN,SAAS,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,KAAK,OAAO,IAAI;gCAC9C,UAAU,kBAAkB,KAAK,EAAE;gCACnC,iBAAiB;gCACjB,aAAa;gCACb,UAAU;gCACV,SAAS;oCACP,oBAAoB;gCACtB;gCACA,eAAe,UAAU,KAAK,cAAc,MAAM,GAAG,IAAI,cAAc,MAAM,GAAG,IAAI;gCACpF,gBAAgB;+BAXX,KAAK,EAAE;;;;;;;;;;;;;;;;0BAmBtB,8OAAC,2HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,2HAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,2HAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,2HAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;;wCAAM,EAAE;wCAAgB;wCAAG,cAAc,MAAM;wCAAC;;;;;;;;;;;;;;;;;sCAIrD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,8HAAA,CAAA,UAAS;wCAER,MAAM;wCACN,SAAS,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,KAAK,OAAO,IAAI;wCAC9C,UAAU,kBAAkB,KAAK,EAAE;wCACnC,iBAAiB;wCACjB,aAAa;wCACb,UAAU;wCACV,eAAe;wCACf,gBAAgB;uCARX,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiB9B;uCAEe", "debugId": null}}, {"offset": {"line": 2598, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/PropertyVideoInput.jsx"], "sourcesContent": ["\"use client\";\n\nimport { FormField, FormItem, FormLabel, FormControl } from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { useTranslations } from \"next-intl\";\n\nconst PropertyVideoInput = ({ form, isFormDisabled }) => {\n  const t = useTranslations(\"PropertyForm\");\n\n  return (\n    <FormField\n      control={form.control}\n      name=\"videoUrl\"\n      className=\"mt-6\"\n      render={({ field }) => (\n        <FormItem>\n          <FormLabel className=\"mt-6\">{t(\"propertyVideoLink\")}</FormLabel>\n          <FormControl>\n            <Input \n              placeholder={t(\"propertyVideoLinkPlaceholder\")} \n              {...field} \n              disabled={isFormDisabled}\n            />\n          </FormControl>\n        </FormItem>\n      )}\n    />\n  );\n};\n\nexport default PropertyVideoInput;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,qBAAqB,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE;IAClD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC,yHAAA,CAAA,YAAS;QACR,SAAS,KAAK,OAAO;QACrB,MAAK;QACL,WAAU;QACV,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kCACP,8OAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;kCAAQ,EAAE;;;;;;kCAC/B,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4BACJ,aAAa,EAAE;4BACd,GAAG,KAAK;4BACT,UAAU;;;;;;;;;;;;;;;;;;;;;;AAOxB;uCAEe", "debugId": null}}, {"offset": {"line": 2660, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/PropertyMediaSection.jsx"], "sourcesContent": ["\"use client\";\n\nimport { Separator } from \"@/components/ui/separator\";\nimport PropertyImageUploader from \"./PropertyImageUploader\";\nimport PropertyVideoInput from \"./PropertyVideoInput\";\nimport CollapseHeader from \"@/components/ui/collapse\";\nimport { useTranslations } from \"next-intl\";\n\nconst PropertyMediaSection = ({ form, property, uploadedFiles, setUploadedFiles, isFormDisabled }) => {\n  const t = useTranslations(\"PropertyForm\");\n\n  const onUploadComplete = (files) => {\n    setUploadedFiles(files);\n  };\n\n  return (\n    <CollapseHeader title={t(\"mediaSection\")} subTitle={t(\"requiredInfo\")}>\n      <Separator className=\"mb-6\" />\n\n      <PropertyImageUploader\n        propertyId={property?.id}\n        initialImages={uploadedFiles}\n        onUploadComplete={onUploadComplete}\n        isFormDisabled={isFormDisabled}\n      />\n      <div className=\"mt-6\">\n        <PropertyVideoInput form={form} isFormDisabled={isFormDisabled} />\n      </div>\n    </CollapseHeader>\n  );\n};\n\nexport default PropertyMediaSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,uBAAuB,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE;IAC/F,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;IACnB;IAEA,qBACE,8OAAC,6HAAA,CAAA,UAAc;QAAC,OAAO,EAAE;QAAiB,UAAU,EAAE;;0BACpD,8OAAC,8HAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;0BAErB,8OAAC,wJAAA,CAAA,UAAqB;gBACpB,YAAY,UAAU;gBACtB,eAAe;gBACf,kBAAkB;gBAClB,gBAAgB;;;;;;0BAElB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,qJAAA,CAAA,UAAkB;oBAAC,MAAM;oBAAM,gBAAgB;;;;;;;;;;;;;;;;;AAIxD;uCAEe", "debugId": null}}, {"offset": {"line": 2731, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/radio-group.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { RadioGroup as RadioGroupPrimitive } from \"radix-ui\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef(({ className, ...props }, ref) => {\r\n  return (<RadioGroupPrimitive.Root className={cn(\"grid gap-2\", className)} {...props} ref={ref} />);\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef(({ className, ...props }, ref) => {\r\n  return (\r\n    (<RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow-sm focus:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-3.5 w-3.5 fill-primary\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>)\r\n  );\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC5D,qBAAQ,8OAAC,gNAAA,CAAA,aAAmB,CAAC,IAAI;QAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAAa,GAAG,KAAK;QAAE,KAAK;;;;;;AAC5F;AACA,WAAW,WAAW,GAAG,gNAAA,CAAA,aAAmB,CAAC,IAAI,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAChE,qBACG,8OAAC,gNAAA,CAAA,aAAmB,CAAC,IAAI;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qMACA;QAED,GAAG,KAAK;kBACT,cAAA,8OAAC,gNAAA,CAAA,aAAmB,CAAC,SAAS;YAAC,WAAU;sBACvC,cAAA,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;AACA,eAAe,WAAW,GAAG,gNAAA,CAAA,aAAmB,CAAC,IAAI,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2791, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/PropertyBasicInfoSection.jsx"], "sourcesContent": ["\"use client\";\n\nimport { FormField, FormItem, FormLabel, FormControl, FormMessage } from \"@/components/ui/form\";\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Input } from \"@/components/ui/input\";\nimport { useTranslations } from \"next-intl\";\nimport { Separator } from \"@/components/ui/separator\";\nimport CollapseHeader from \"@/components/ui/collapse\";\n\nconst PropertyBasicInfoSection = ({ form, isFormDisabled }) => {\n  const t = useTranslations(\"PropertyForm\");\n\n  return (\n    <CollapseHeader title={t(\"basicInfo\")} subTitle={t(\"requiredInfo\")}>\n      <Separator className=\"mb-6\" />\n      <FormField\n        control={form.control}\n        name=\"postType\"\n        render={({ field }) => (\n          <FormItem>\n            <FormLabel>{t(\"postType\")}</FormLabel>\n            <FormControl>\n              <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className=\"flex gap-4 mt-2\">\n                <FormItem className=\"flex items-center space-x-3 space-y-0\">\n                  <FormControl>\n                    <RadioGroupItem value=\"sell\" disabled={isFormDisabled} />\n                  </FormControl>\n                  <FormLabel className=\"font-normal\">{t(\"sell\")}</FormLabel>\n                </FormItem>\n                <FormItem className=\"flex items-center space-x-3 space-y-0\">\n                  <FormControl>\n                    <RadioGroupItem value=\"rent\" disabled={isFormDisabled} />\n                  </FormControl>\n                  <FormLabel className=\"font-normal\">{t(\"rent\")}</FormLabel>\n                </FormItem>\n              </RadioGroup>\n            </FormControl>\n            <FormMessage />\n          </FormItem>\n        )}\n      />\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <div>\n          <FormField\n            control={form.control}\n            name=\"propertyType\"\n            render={({ field }) => (\n              <FormItem className=\"mt-3\">\n                <FormLabel>{t(\"propertyType\")}</FormLabel>\n                <FormControl>\n                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>\n                    <SelectTrigger className=\"mt-1\">\n                      <SelectValue placeholder={t(\"selectPropertyType\")} />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"nha_rieng\">{t(\"house\")}</SelectItem>\n                      <SelectItem value=\"can_ho\">{t(\"apartment\")}</SelectItem>\n                      <SelectItem value=\"nha_tro\">{t(\"motel\")}</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </FormControl>\n                <FormMessage />\n              </FormItem>\n            )}\n          />\n        </div>\n        <div>\n          <FormField\n            control={form.control}\n            name=\"price\"\n            render={({ field }) => (\n              <FormItem className=\"mt-3\">\n                <FormLabel>{t(\"price\")}</FormLabel>\n                <FormControl>\n                  <Input placeholder={t(\"pricePlaceholder\")} {...field} type=\"number\" suffix=\" VND\" disabled={isFormDisabled} />\n                </FormControl>\n                <FormMessage></FormMessage>\n              </FormItem>\n            )}\n          />\n        </div>\n      </div>\n    </CollapseHeader>\n  );\n};\n\nexport default PropertyBasicInfoSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,2BAA2B,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE;IACxD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC,6HAAA,CAAA,UAAc;QAAC,OAAO,EAAE;QAAc,UAAU,EAAE;;0BACjD,8OAAC,8HAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;0BACrB,8OAAC,yHAAA,CAAA,YAAS;gBACR,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;0CACP,8OAAC,yHAAA,CAAA,YAAS;0CAAE,EAAE;;;;;;0CACd,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;oCAAC,eAAe,MAAM,QAAQ;oCAAE,cAAc,MAAM,KAAK;oCAAE,WAAU;;sDAC9E,8OAAC,yHAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,8OAAC,yHAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,OAAM;wDAAO,UAAU;;;;;;;;;;;8DAEzC,8OAAC,yHAAA,CAAA,YAAS;oDAAC,WAAU;8DAAe,EAAE;;;;;;;;;;;;sDAExC,8OAAC,yHAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,8OAAC,yHAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,OAAM;wDAAO,UAAU;;;;;;;;;;;8DAEzC,8OAAC,yHAAA,CAAA,YAAS;oDAAC,WAAU;8DAAe,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAI5C,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0BAIlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCACC,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,yHAAA,CAAA,YAAS;sDAAE,EAAE;;;;;;sDACd,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,2HAAA,CAAA,SAAM;gDAAC,eAAe,MAAM,QAAQ;gDAAE,cAAc,MAAM,KAAK;gDAAE,UAAU;;kEAC1E,8OAAC,2HAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;4DAAC,aAAa,EAAE;;;;;;;;;;;kEAE9B,8OAAC,2HAAA,CAAA,gBAAa;;0EACZ,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAa,EAAE;;;;;;0EACjC,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU,EAAE;;;;;;0EAC9B,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;sDAIrC,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;kCAKpB,8OAAC;kCACC,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,yHAAA,CAAA,YAAS;sDAAE,EAAE;;;;;;sDACd,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;gDAAC,aAAa,EAAE;gDAAsB,GAAG,KAAK;gDAAE,MAAK;gDAAS,QAAO;gDAAO,UAAU;;;;;;;;;;;sDAE9F,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B;uCAEe", "debugId": null}}, {"offset": {"line": 3104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/command.jsx"], "sourcesContent": ["\"use client\";\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { Search } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Dialog, DialogContent } from \"@/components/ui/dialog\"\r\n\r\nconst Command = React.forwardRef(({ className, ...props }, ref) => (\r\n  <CommandPrimitive\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nCommand.displayName = CommandPrimitive.displayName\r\n\r\nconst CommandDialog = ({\r\n  children,\r\n  ...props\r\n}) => {\r\n  return (\r\n    (<Dialog {...props}>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command\r\n          className=\"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>)\r\n  );\r\n}\r\n\r\nconst CommandInput = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div className=\"flex items-center border-b px-3\" cmdk-input-wrapper=\"\">\r\n    <Search className=\"mr-2 h-4 w-4 shrink-0 opacity-50\" />\r\n    <CommandPrimitive.Input\r\n      ref={ref}\r\n      className={cn(\r\n        \"flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  </div>\r\n))\r\n\r\nCommandInput.displayName = CommandPrimitive.Input.displayName\r\n\r\nconst CommandList = React.forwardRef(({ className, ...props }, ref) => (\r\n  <CommandPrimitive.List\r\n    ref={ref}\r\n    className={cn(\"max-h-[300px] overflow-y-auto overflow-x-hidden\", className)}\r\n    {...props} />\r\n))\r\n\r\nCommandList.displayName = CommandPrimitive.List.displayName\r\n\r\nconst CommandEmpty = React.forwardRef((props, ref) => (\r\n  <CommandPrimitive.Empty ref={ref} className=\"py-6 text-center text-sm\" {...props} />\r\n))\r\n\r\nCommandEmpty.displayName = CommandPrimitive.Empty.displayName\r\n\r\nconst CommandGroup = React.forwardRef(({ className, ...props }, ref) => (\r\n  <CommandPrimitive.Group\r\n    ref={ref}\r\n    className={cn(\r\n      \"overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\n\r\nCommandGroup.displayName = CommandPrimitive.Group.displayName\r\n\r\nconst CommandSeparator = React.forwardRef(({ className, ...props }, ref) => (\r\n  <CommandPrimitive.Separator ref={ref} className={cn(\"-mx-1 h-px bg-border\", className)} {...props} />\r\n))\r\nCommandSeparator.displayName = CommandPrimitive.Separator.displayName\r\n\r\nconst CommandItem = React.forwardRef(({ className, ...props }, ref) => (\r\n  <CommandPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-hidden data-[disabled=true]:pointer-events-none data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\n\r\nCommandItem.displayName = CommandPrimitive.Item.displayName\r\n\r\nconst CommandShortcut = ({\r\n  className,\r\n  ...props\r\n}) => {\r\n  return (\r\n    (<span\r\n      className={cn(\"ml-auto text-xs tracking-widest text-muted-foreground\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\nCommandShortcut.displayName = \"CommandShortcut\"\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AACA;AACA;AAEA;AACA;AANA;;;;;;;AAQA,MAAM,wBAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,sIAAA,CAAA,UAAgB;QACf,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAEb,QAAQ,WAAW,GAAG,sIAAA,CAAA,UAAgB,CAAC,WAAW;AAElD,MAAM,gBAAgB,CAAC,EACrB,QAAQ,EACR,GAAG,OACJ;IACC,qBACG,8OAAC,2HAAA,CAAA,SAAM;QAAE,GAAG,KAAK;kBAChB,cAAA,8OAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;sBACvB,cAAA,8OAAC;gBACC,WAAU;0BACT;;;;;;;;;;;;;;;;AAKX;AAEA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC9D,8OAAC;QAAI,WAAU;QAAkC,sBAAmB;;0BAClE,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;0BAClB,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIf,aAAa,WAAW,GAAG,sIAAA,CAAA,UAAgB,CAAC,KAAK,CAAC,WAAW;AAE7D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC,sIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;QAChE,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,sIAAA,CAAA,UAAgB,CAAC,IAAI,CAAC,WAAW;AAE3D,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,OAAO,oBAC5C,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;QAAC,KAAK;QAAK,WAAU;QAA4B,GAAG,KAAK;;;;;;AAGlF,aAAa,WAAW,GAAG,sIAAA,CAAA,UAAgB,CAAC,KAAK,CAAC,WAAW;AAE7D,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC9D,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG,sIAAA,CAAA,UAAgB,CAAC,KAAK,CAAC,WAAW;AAE7D,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,SAAS;QAAC,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QAAa,GAAG,KAAK;;;;;;AAEnG,iBAAiB,WAAW,GAAG,sIAAA,CAAA,UAAgB,CAAC,SAAS,CAAC,WAAW;AAErE,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC,sIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6TACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,sIAAA,CAAA,UAAgB,CAAC,IAAI,CAAC,WAAW;AAE3D,MAAM,kBAAkB,CAAC,EACvB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,8OAAC;QACA,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;;;;;;AAEf;AACA,gBAAgB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3257, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/AddressConflictDialog.jsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from \"@/components/ui/alert-dialog\";\nimport { useTranslations } from \"next-intl\";\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\nimport { AlertCircle } from \"lucide-react\";\n\nexport default function AddressConflictDialog({ open, onOpenChange, currentLocation, newLocation, onConfirmChange, onKeepCurrent }) {\n  const t = useTranslations(\"PropertyForm\");\n\n  return (\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\n      <AlertDialogContent>\n        <AlertDialogHeader>\n          <AlertDialogTitle className=\"text-lg font-semibold text-red-600\">{t(\"locationConflictTitle\")}</AlertDialogTitle>\n          <AlertDialogDescription asChild className=\"text-base\">\n            <div className=\"flex-col items-center gap-2 \">\n              <Alert className=\"bg-teal-100 mb-3\">\n                <AlertCircle className=\"h-4 w-4 text-teal-600\" />\n                <AlertDescription>{t(\"noteEnterAddress\")}</AlertDescription>\n              </Alert>\n              <div>\n                {t(\"locationConflictDescription\", {\n                  newLocation: newLocation?.name || \"\",\n                  currentLocation: currentLocation?.name || \"\",\n                })}\n              </div>\n            </div>\n          </AlertDialogDescription>\n        </AlertDialogHeader>\n        <AlertDialogFooter className=\"flex-col space-y-2 sm:space-y-0 sm:flex-row\">\n          <AlertDialogCancel onClick={onKeepCurrent} className=\"mt-2 sm:mt-0\">\n            {t(\"keepCurrentLocation\", { location: currentLocation?.name || \"\" })}\n          </AlertDialogCancel>\n          <AlertDialogAction onClick={onConfirmChange} className=\"bg-teal-600 hover:bg-teal-700\">\n            {t(\"switchToNewLocation\", { location: newLocation?.name || \"\" })}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAUA;AACA;AACA;AAdA;;;;;;AAgBe,SAAS,sBAAsB,EAAE,IAAI,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE;IAChI,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC,oIAAA,CAAA,cAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,8OAAC,oIAAA,CAAA,qBAAkB;;8BACjB,8OAAC,oIAAA,CAAA,oBAAiB;;sCAChB,8OAAC,oIAAA,CAAA,mBAAgB;4BAAC,WAAU;sCAAsC,EAAE;;;;;;sCACpE,8OAAC,oIAAA,CAAA,yBAAsB;4BAAC,OAAO;4BAAC,WAAU;sCACxC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;wCAAC,WAAU;;0DACf,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC,0HAAA,CAAA,mBAAgB;0DAAE,EAAE;;;;;;;;;;;;kDAEvB,8OAAC;kDACE,EAAE,+BAA+B;4CAChC,aAAa,aAAa,QAAQ;4CAClC,iBAAiB,iBAAiB,QAAQ;wCAC5C;;;;;;;;;;;;;;;;;;;;;;;8BAKR,8OAAC,oIAAA,CAAA,oBAAiB;oBAAC,WAAU;;sCAC3B,8OAAC,oIAAA,CAAA,oBAAiB;4BAAC,SAAS;4BAAe,WAAU;sCAClD,EAAE,uBAAuB;gCAAE,UAAU,iBAAiB,QAAQ;4BAAG;;;;;;sCAEpE,8OAAC,oIAAA,CAAA,oBAAiB;4BAAC,SAAS;4BAAiB,WAAU;sCACpD,EAAE,uBAAuB;gCAAE,UAAU,aAAa,QAAQ;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM1E", "debugId": null}}, {"offset": {"line": 3393, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/AddressInput.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useCallback, useRef, useEffect } from \"react\";\r\nimport { FormField, FormItem, FormLabel, FormControl, FormMessage } from \"@/components/ui/form\";\r\nimport { Command, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator } from \"@/components/ui/command\";\r\nimport { MapPin } from \"lucide-react\";\r\nimport { HelpCircle } from \"lucide-react\";\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from \"@/components/ui/tooltip\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport AddressConflictDialog from \"./AddressConflictDialog\";\r\n\r\nconst AddressInput = ({\r\n  form,\r\n  isFormDisabled,\r\n  selectedCity,\r\n  selectedDistrict,\r\n  selectedWard,\r\n  onAddressSelect,\r\n  onManualAddressClick,\r\n  locationSelectorRef,\r\n}) => {\r\n  const t = useTranslations(\"PropertyForm\");\r\n\r\n  // Initialize with the form value or empty string\r\n  const [inputValue, setInputValue] = useState(form.getValues(\"address\") || \"\");\r\n  const [predictions, setPredictions] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const timeoutRef = useRef(null);\r\n\r\n  // State for location conflict dialog\r\n  const [showConflictDialog, setShowConflictDialog] = useState(false);\r\n  const [conflictData, setConflictData] = useState(null);\r\n  const [pendingPrediction, setPendingPrediction] = useState(null);\r\n\r\n\r\n\r\n  // Update inputValue when form value changes\r\n  useEffect(() => {\r\n    const subscription = form.watch((value, { name }) => {\r\n      if (name === \"address\") {\r\n        setInputValue(value.address || \"\");\r\n      }\r\n    });\r\n\r\n    return () => subscription.unsubscribe();\r\n  }, [form]);\r\n\r\n  // Efficient debounced fetch implementation\r\n  const fetchAddressSuggestions = useCallback(\r\n    async (query) => {\r\n      if (!query) {\r\n        setPredictions([]);\r\n        return;\r\n      }\r\n\r\n      setIsLoading(true);\r\n\r\n      const locationParts = [query];\r\n\r\n      // Get location data from LocationSelector component\r\n      const locationData = locationSelectorRef?.current || {};\r\n      const { cities = [], districts = [], wards = [] } = locationData;\r\n\r\n      const cityName = cities.find((c) => c.id.toString() === selectedCity)?.name;\r\n      if (cityName) locationParts.push(cityName);\r\n\r\n      const districtName = districts.find((d) => d.id.toString() === selectedDistrict)?.name;\r\n      if (districtName) locationParts.push(districtName);\r\n\r\n      const wardName = wards.find((w) => w.id.toString() === selectedWard)?.name;\r\n      if (wardName) locationParts.push(wardName);\r\n\r\n      const fullQuery = encodeURIComponent(locationParts.join(\", \"));\r\n\r\n      try {\r\n        const response = await fetch(`/api/map/address-suggestions?input=${fullQuery}`);\r\n        const data = await response.json();\r\n        setPredictions(data.predictions ?? []);\r\n      } catch (error) {\r\n        console.error(\"Error fetching address suggestions:\", error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    },\r\n    [selectedCity, selectedDistrict, selectedWard, locationSelectorRef]\r\n  );\r\n\r\n  // Debounce the input changes\r\n  const handleInputChange = useCallback(\r\n    (value) => {\r\n      setInputValue(value);\r\n\r\n      // Clear any existing timeout\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current);\r\n      }\r\n\r\n      // Set a new timeout\r\n      timeoutRef.current = setTimeout(() => {\r\n        fetchAddressSuggestions(value);\r\n      }, 300); // 300ms debounce time\r\n    },\r\n    [fetchAddressSuggestions]\r\n  );\r\n\r\n  // Clean up timeout on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Check for location conflicts between selected address and current dropdowns\r\n  const checkLocationConflict = useCallback(\r\n    async (place_id) => {\r\n      try {\r\n        // Get place details to extract administrative components\r\n        const response = await fetch(`/api/map/place-detail?place_id=${place_id}`);\r\n        const data = await response.json();\r\n\r\n        if (!data.result || !data.result.compound) {\r\n          return null;\r\n        }\r\n\r\n        // Extract city, district, ward from compound object\r\n        const { compound } = data.result;\r\n\r\n        // Get province (city), district, and commune (ward) from compound\r\n        const cityName = compound.province;\r\n        const districtName = compound.district;\r\n        const wardName = compound.commune;\r\n\r\n        // Store these for potential future use (district/ward matching)\r\n        const addressLocation = {\r\n          city: cityName,\r\n          district: districtName,\r\n          ward: wardName,\r\n        };\r\n\r\n        // If no city found, can't compare\r\n        if (!cityName) return null;\r\n\r\n        // Get location data from LocationSelector component\r\n        const locationData = locationSelectorRef?.current || {};\r\n        const { cities = [] } = locationData;\r\n\r\n        // Check if city matches\r\n        const selectedCityObj = cities.find((city) => city.id.toString() === selectedCity);\r\n\r\n        // If no city is selected in dropdown, no conflict\r\n        if (!selectedCity || !selectedCityObj) return null;\r\n\r\n        // Check if the city name from address matches the selected city\r\n        const cityMatches =\r\n          selectedCityObj.name.toLowerCase().includes(cityName.toLowerCase()) || cityName.toLowerCase().includes(selectedCityObj.name.toLowerCase());\r\n\r\n        // If city doesn't match, we have a conflict\r\n        if (!cityMatches) {\r\n          return {\r\n            currentLocation: {\r\n              id: selectedCityObj.id,\r\n              name: selectedCityObj.nameWithType || selectedCityObj.name,\r\n            },\r\n            newLocation: {\r\n              name: cityName,\r\n              // Try to find matching city in our list\r\n              matchingCity: cities.find(\r\n                (city) => city.name.toLowerCase().includes(cityName.toLowerCase()) || cityName.toLowerCase().includes(city.name.toLowerCase())\r\n              ),\r\n              addressLocation, // Include full location data for future use\r\n            },\r\n            placeDetails: data.result,\r\n          };\r\n        }\r\n\r\n        return null; // No conflict\r\n      } catch (error) {\r\n        console.error(\"Error checking location conflict:\", error);\r\n        return null;\r\n      }\r\n    },\r\n    [selectedCity, locationSelectorRef]\r\n  );\r\n\r\n  // Handle location conflict resolution\r\n  const handleConfirmLocationChange = useCallback(async () => {\r\n    if (!conflictData || !pendingPrediction) return;\r\n\r\n    // Find the matching city in our list (optional, parent component can handle this)\r\n    const newCity = conflictData?.newLocation?.matchingCity; // Pass resolved location details back to parent component\r\n\r\n    if (onAddressSelect && pendingPrediction && conflictData?.newLocation?.addressLocation) {\r\n      const description = pendingPrediction.description || \"\";\r\n      onAddressSelect(\r\n        pendingPrediction.place_id,\r\n        { ...pendingPrediction, description },\r\n        {\r\n          city: conflictData.newLocation.addressLocation.city,\r\n          district: conflictData.newLocation.addressLocation.district,\r\n          ward: conflictData.newLocation.addressLocation.ward,\r\n          newCityId: newCity?.id,\r\n        }\r\n      );\r\n    }\r\n\r\n    // Reset state\r\n    setShowConflictDialog(false);\r\n    setConflictData(null);\r\n    setPendingPrediction(null);\r\n  }, [conflictData, pendingPrediction, form, onAddressSelect]);\r\n\r\n  // Handle keeping current location\r\n  const handleKeepCurrentLocation = useCallback(() => {\r\n    // Clear the input and predictions\r\n    setInputValue(\"\");\r\n    setPredictions([]);\r\n\r\n    // Reset state\r\n    setShowConflictDialog(false);\r\n    setConflictData(null);\r\n    setPendingPrediction(null);\r\n  }, []);\r\n\r\n  // Handle address selection\r\n  const handleSelectAddress = useCallback(\r\n    async (place_id) => {\r\n      const selectedPrediction = predictions.find((item) => item.place_id === place_id);\r\n      if (!selectedPrediction) return;\r\n\r\n      // Check for location conflicts\r\n      const conflict = await checkLocationConflict(place_id);\r\n\r\n      if (conflict) {\r\n        // Store the prediction for later use\r\n        setPendingPrediction(selectedPrediction);\r\n        setConflictData(conflict);\r\n        setShowConflictDialog(true);\r\n      } else {\r\n        // No conflict, proceed normally\r\n        if (onAddressSelect) {\r\n          // Make sure we're passing a valid description\r\n          const description = selectedPrediction.description || \"\";\r\n          onAddressSelect(place_id, { ...selectedPrediction, description });\r\n        }\r\n      }\r\n\r\n      setPredictions([]);\r\n    },\r\n    [predictions, onAddressSelect, checkLocationConflict]\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <FormField\r\n        control={form.control}\r\n        name=\"address\"\r\n        render={({ field }) => (\r\n          <FormItem className=\"flex-1\">\r\n            <FormLabel className=\"flex gap-3\">\r\n              {t(\"address\")}\r\n              <TooltipProvider>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <HelpCircle className=\"h-4 w-4 text-gray-500 cursor-help\" />\r\n                  </TooltipTrigger>\r\n                  <TooltipContent>\r\n                    <p>{t(\"addressDescription\")}</p>\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n            </FormLabel>\r\n            <FormControl>\r\n              <Command shouldFilter={false} className=\"border rounded-md\">\r\n                <CommandInput\r\n                  value={inputValue}\r\n                  placeholder={t(\"addressPlaceholder\")}\r\n                  onValueChange={handleInputChange}\r\n                  disabled={!selectedWard || isFormDisabled}\r\n                  className={isLoading ? \"opacity-70\" : \"\"}\r\n                  onBlur={() => {\r\n                    field.onBlur();\r\n                    // Update form value when input loses focus\r\n                    form.setValue(\"address\", inputValue);\r\n                  }}\r\n                />\r\n                <CommandList>\r\n                  {!isFormDisabled && predictions.length > 0 && (\r\n                    <>\r\n                      <CommandGroup>\r\n                        {predictions.map((prediction) => (\r\n                          <CommandItem\r\n                            key={prediction.place_id}\r\n                            value={prediction.place_id}\r\n                            onSelect={handleSelectAddress}\r\n                            className=\"flex items-center gap-2\"\r\n                          >\r\n                            <MapPin className=\"h-4 w-4 text-gray-400\" />\r\n                            {prediction.description}\r\n                          </CommandItem>\r\n                        ))}\r\n                      </CommandGroup>\r\n                      <CommandSeparator />\r\n                      <CommandGroup>\r\n                        <CommandItem onSelect={onManualAddressClick} className=\"text-teal-600 font-medium hover:text-teal-700 hover:bg-teal-50\">\r\n                          {t(\"enterAddressManually\")}\r\n                        </CommandItem>\r\n                      </CommandGroup>\r\n                    </>\r\n                  )}\r\n                </CommandList>\r\n              </Command>\r\n            </FormControl>\r\n            <FormMessage />\r\n          </FormItem>\r\n        )}\r\n      />\r\n\r\n      {/* Location conflict dialog */}\r\n      <AddressConflictDialog\r\n        open={showConflictDialog}\r\n        onOpenChange={setShowConflictDialog}\r\n        currentLocation={conflictData?.currentLocation}\r\n        newLocation={conflictData?.newLocation}\r\n        onConfirmChange={handleConfirmLocationChange}\r\n        onKeepCurrent={handleKeepCurrentLocation}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddressInput;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,MAAM,eAAe,CAAC,EACpB,IAAI,EACJ,cAAc,EACd,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,oBAAoB,EACpB,mBAAmB,EACpB;IACC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,iDAAiD;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,SAAS,CAAC,cAAc;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,qCAAqC;IACrC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAI3D,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;YAC9C,IAAI,SAAS,WAAW;gBACtB,cAAc,MAAM,OAAO,IAAI;YACjC;QACF;QAEA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;KAAK;IAET,2CAA2C;IAC3C,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxC,OAAO;QACL,IAAI,CAAC,OAAO;YACV,eAAe,EAAE;YACjB;QACF;QAEA,aAAa;QAEb,MAAM,gBAAgB;YAAC;SAAM;QAE7B,oDAAoD;QACpD,MAAM,eAAe,qBAAqB,WAAW,CAAC;QACtD,MAAM,EAAE,SAAS,EAAE,EAAE,YAAY,EAAE,EAAE,QAAQ,EAAE,EAAE,GAAG;QAEpD,MAAM,WAAW,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,CAAC,QAAQ,OAAO,eAAe;QACvE,IAAI,UAAU,cAAc,IAAI,CAAC;QAEjC,MAAM,eAAe,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,CAAC,QAAQ,OAAO,mBAAmB;QAClF,IAAI,cAAc,cAAc,IAAI,CAAC;QAErC,MAAM,WAAW,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,CAAC,QAAQ,OAAO,eAAe;QACtE,IAAI,UAAU,cAAc,IAAI,CAAC;QAEjC,MAAM,YAAY,mBAAmB,cAAc,IAAI,CAAC;QAExD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mCAAmC,EAAE,WAAW;YAC9E,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe,KAAK,WAAW,IAAI,EAAE;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD,SAAU;YACR,aAAa;QACf;IACF,GACA;QAAC;QAAc;QAAkB;QAAc;KAAoB;IAGrE,6BAA6B;IAC7B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,cAAc;QAEd,6BAA6B;QAC7B,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;QACjC;QAEA,oBAAoB;QACpB,WAAW,OAAO,GAAG,WAAW;YAC9B,wBAAwB;QAC1B,GAAG,MAAM,sBAAsB;IACjC,GACA;QAAC;KAAwB;IAG3B,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,aAAa,WAAW,OAAO;YACjC;QACF;IACF,GAAG,EAAE;IAEL,8EAA8E;IAC9E,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACtC,OAAO;QACL,IAAI;YACF,yDAAyD;YACzD,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,UAAU;YACzE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,QAAQ,EAAE;gBACzC,OAAO;YACT;YAEA,oDAAoD;YACpD,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,MAAM;YAEhC,kEAAkE;YAClE,MAAM,WAAW,SAAS,QAAQ;YAClC,MAAM,eAAe,SAAS,QAAQ;YACtC,MAAM,WAAW,SAAS,OAAO;YAEjC,gEAAgE;YAChE,MAAM,kBAAkB;gBACtB,MAAM;gBACN,UAAU;gBACV,MAAM;YACR;YAEA,kCAAkC;YAClC,IAAI,CAAC,UAAU,OAAO;YAEtB,oDAAoD;YACpD,MAAM,eAAe,qBAAqB,WAAW,CAAC;YACtD,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG;YAExB,wBAAwB;YACxB,MAAM,kBAAkB,OAAO,IAAI,CAAC,CAAC,OAAS,KAAK,EAAE,CAAC,QAAQ,OAAO;YAErE,kDAAkD;YAClD,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,OAAO;YAE9C,gEAAgE;YAChE,MAAM,cACJ,gBAAgB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW,OAAO,SAAS,WAAW,GAAG,QAAQ,CAAC,gBAAgB,IAAI,CAAC,WAAW;YAEzI,4CAA4C;YAC5C,IAAI,CAAC,aAAa;gBAChB,OAAO;oBACL,iBAAiB;wBACf,IAAI,gBAAgB,EAAE;wBACtB,MAAM,gBAAgB,YAAY,IAAI,gBAAgB,IAAI;oBAC5D;oBACA,aAAa;wBACX,MAAM;wBACN,wCAAwC;wBACxC,cAAc,OAAO,IAAI,CACvB,CAAC,OAAS,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW,OAAO,SAAS,WAAW,GAAG,QAAQ,CAAC,KAAK,IAAI,CAAC,WAAW;wBAE7H;oBACF;oBACA,cAAc,KAAK,MAAM;gBAC3B;YACF;YAEA,OAAO,MAAM,cAAc;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;QACT;IACF,GACA;QAAC;QAAc;KAAoB;IAGrC,sCAAsC;IACtC,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9C,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;QAEzC,kFAAkF;QAClF,MAAM,UAAU,cAAc,aAAa,cAAc,0DAA0D;QAEnH,IAAI,mBAAmB,qBAAqB,cAAc,aAAa,iBAAiB;YACtF,MAAM,cAAc,kBAAkB,WAAW,IAAI;YACrD,gBACE,kBAAkB,QAAQ,EAC1B;gBAAE,GAAG,iBAAiB;gBAAE;YAAY,GACpC;gBACE,MAAM,aAAa,WAAW,CAAC,eAAe,CAAC,IAAI;gBACnD,UAAU,aAAa,WAAW,CAAC,eAAe,CAAC,QAAQ;gBAC3D,MAAM,aAAa,WAAW,CAAC,eAAe,CAAC,IAAI;gBACnD,WAAW,SAAS;YACtB;QAEJ;QAEA,cAAc;QACd,sBAAsB;QACtB,gBAAgB;QAChB,qBAAqB;IACvB,GAAG;QAAC;QAAc;QAAmB;QAAM;KAAgB;IAE3D,kCAAkC;IAClC,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5C,kCAAkC;QAClC,cAAc;QACd,eAAe,EAAE;QAEjB,cAAc;QACd,sBAAsB;QACtB,gBAAgB;QAChB,qBAAqB;IACvB,GAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,OAAO;QACL,MAAM,qBAAqB,YAAY,IAAI,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK;QACxE,IAAI,CAAC,oBAAoB;QAEzB,+BAA+B;QAC/B,MAAM,WAAW,MAAM,sBAAsB;QAE7C,IAAI,UAAU;YACZ,qCAAqC;YACrC,qBAAqB;YACrB,gBAAgB;YAChB,sBAAsB;QACxB,OAAO;YACL,gCAAgC;YAChC,IAAI,iBAAiB;gBACnB,8CAA8C;gBAC9C,MAAM,cAAc,mBAAmB,WAAW,IAAI;gBACtD,gBAAgB,UAAU;oBAAE,GAAG,kBAAkB;oBAAE;gBAAY;YACjE;QACF;QAEA,eAAe,EAAE;IACnB,GACA;QAAC;QAAa;QAAiB;KAAsB;IAGvD,qBACE;;0BACE,8OAAC,yHAAA,CAAA,YAAS;gBACR,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;;oCAClB,EAAE;kDACH,8OAAC,4HAAA,CAAA,kBAAe;kDACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;8DACN,8OAAC,4HAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,8OAAC,4HAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC;kEAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKd,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,4HAAA,CAAA,UAAO;oCAAC,cAAc;oCAAO,WAAU;;sDACtC,8OAAC,4HAAA,CAAA,eAAY;4CACX,OAAO;4CACP,aAAa,EAAE;4CACf,eAAe;4CACf,UAAU,CAAC,gBAAgB;4CAC3B,WAAW,YAAY,eAAe;4CACtC,QAAQ;gDACN,MAAM,MAAM;gDACZ,2CAA2C;gDAC3C,KAAK,QAAQ,CAAC,WAAW;4CAC3B;;;;;;sDAEF,8OAAC,4HAAA,CAAA,cAAW;sDACT,CAAC,kBAAkB,YAAY,MAAM,GAAG,mBACvC;;kEACE,8OAAC,4HAAA,CAAA,eAAY;kEACV,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC,4HAAA,CAAA,cAAW;gEAEV,OAAO,WAAW,QAAQ;gEAC1B,UAAU;gEACV,WAAU;;kFAEV,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEACjB,WAAW,WAAW;;+DANlB,WAAW,QAAQ;;;;;;;;;;kEAU9B,8OAAC,4HAAA,CAAA,mBAAgB;;;;;kEACjB,8OAAC,4HAAA,CAAA,eAAY;kEACX,cAAA,8OAAC,4HAAA,CAAA,cAAW;4DAAC,UAAU;4DAAsB,WAAU;sEACpD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQjB,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0BAMlB,8OAAC,wJAAA,CAAA,UAAqB;gBACpB,MAAM;gBACN,cAAc;gBACd,iBAAiB,cAAc;gBAC/B,aAAa,cAAc;gBAC3B,iBAAiB;gBACjB,eAAe;;;;;;;;AAIvB;uCAEe", "debugId": null}}, {"offset": {"line": 3800, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/RankChangeDialog.jsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from \"@/components/ui/alert-dialog\";\nimport { useTranslations } from \"next-intl\";\nimport { formatCurrency } from \"@/lib/utils\";\n\nconst RankChangeDialog = ({ \n  open, \n  onOpenChange, \n  rankChangeDetails, \n  onConfirm, \n  onCancel,\n}) => {\n  const t = useTranslations(\"PropertyForm\");\n  const tWallet = useTranslations(\"UserWalletPage\");\n\n  return (\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\n      <AlertDialogContent>\n        <AlertDialogHeader>\n          <AlertDialogTitle>{rankChangeDetails?.isUpgrade ? t(\"congratulations\") : t(\"announcementChange\")}</AlertDialogTitle>\n          <AlertDialogDescription>\n            {\n              rankChangeDetails\n                ? rankChangeDetails.isUpgrade\n                  ? t(\"rankUpgradeMessage\", {\n                      currentRank: tWallet(rankChangeDetails.currentRank) ?? rankChangeDetails.currentRank,\n                      previousPrice: formatCurrency(rankChangeDetails.previousPrice),\n                      currentPrice: formatCurrency(rankChangeDetails.currentPrice),\n                    })\n                  : t(\"rankChangeMessage\", {\n                      currentRank: tWallet(rankChangeDetails.currentRank) ?? rankChangeDetails.currentRank,\n                      previousPrice: formatCurrency(rankChangeDetails.previousPrice),\n                      currentPrice: formatCurrency(rankChangeDetails.currentPrice),\n                    })\n                : // Provide a fallback message or render nothing if rankChangeDetails is null\n                  t(\"loadingRankChangeInfo\") // Example fallback, adjust as needed\n            }\n          </AlertDialogDescription>\n        </AlertDialogHeader>\n        <AlertDialogFooter>\n          <AlertDialogCancel onClick={onCancel}>{t(\"cancel\")}</AlertDialogCancel>\n          <AlertDialogAction onClick={onConfirm}>\n            {rankChangeDetails?.isUpgrade ? t(\"continue\") : t(\"updatePrice\")}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>\n  );\n};\n\nexport default RankChangeDialog;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAUA;AACA;AAdA;;;;;;AAgBA,MAAM,mBAAmB,CAAC,EACxB,IAAI,EACJ,YAAY,EACZ,iBAAiB,EACjB,SAAS,EACT,QAAQ,EACT;IACC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,qBACE,8OAAC,oIAAA,CAAA,cAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,8OAAC,oIAAA,CAAA,qBAAkB;;8BACjB,8OAAC,oIAAA,CAAA,oBAAiB;;sCAChB,8OAAC,oIAAA,CAAA,mBAAgB;sCAAE,mBAAmB,YAAY,EAAE,qBAAqB,EAAE;;;;;;sCAC3E,8OAAC,oIAAA,CAAA,yBAAsB;sCAEnB,oBACI,kBAAkB,SAAS,GACzB,EAAE,sBAAsB;gCACtB,aAAa,QAAQ,kBAAkB,WAAW,KAAK,kBAAkB,WAAW;gCACpF,eAAe,CAAA,GAAA,4GAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,aAAa;gCAC7D,cAAc,CAAA,GAAA,4GAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,YAAY;4BAC7D,KACA,EAAE,qBAAqB;gCACrB,aAAa,QAAQ,kBAAkB,WAAW,KAAK,kBAAkB,WAAW;gCACpF,eAAe,CAAA,GAAA,4GAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,aAAa;gCAC7D,cAAc,CAAA,GAAA,4GAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,YAAY;4BAC7D,KAEF,EAAE,yBAAyB,qCAAqC;;;;;;;;;;;;8BAI1E,8OAAC,oIAAA,CAAA,oBAAiB;;sCAChB,8OAAC,oIAAA,CAAA,oBAAiB;4BAAC,SAAS;sCAAW,EAAE;;;;;;sCACzC,8OAAC,oIAAA,CAAA,oBAAiB;4BAAC,SAAS;sCACzB,mBAAmB,YAAY,EAAE,cAAc,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAM9D;uCAEe", "debugId": null}}, {"offset": {"line": 3895, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/switch.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Switch as SwitchPrimitives } from \"radix-ui\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-xs transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}>\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\r\n      )} />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxD,8OAAC,oMAAA,CAAA,SAAgB,CAAC,IAAI;QACpB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iYACA;QAED,GAAG,KAAK;QACT,KAAK;kBACL,cAAA,8OAAC,oMAAA,CAAA,SAAgB,CAAC,KAAK;YACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAIR,OAAO,WAAW,GAAG,oMAAA,CAAA,SAAgB,CAAC,IAAI,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3931, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/BadgeStatus.jsx"], "sourcesContent": ["import { PropertyStatus } from \"@/lib/enum\";\r\nimport { cn, formatStatusText } from \"@/lib/utils\";\r\nimport { Badge } from \"../ui/badge\";\r\n\r\nconst STATUS_STYLES = {\r\n  [PropertyStatus.DRAFT]: \"bg-gray-100 text-gray-700\",\r\n  [PropertyStatus.SOLD]: \"bg-sky-100 text-sky-700\",\r\n  [PropertyStatus.EXPIRED]: \"bg-amber-100 text-amber-700\",\r\n  [PropertyStatus.APPROVED]: \"bg-emerald-100 text-emerald-700\",\r\n  [PropertyStatus.PENDING_APPROVAL]: \"bg-yellow-100 text-yellow-700\",\r\n  [PropertyStatus.REJECTED_BY_ADMIN]: \"bg-rose-100 text-rose-700\",\r\n  [PropertyStatus.REJECTED_DUE_TO_UNPAID]: \"bg-red-100 text-red-700\",\r\n  [PropertyStatus.WAITING_PAYMENT]: \"bg-orange-100 text-orange-700\",\r\n};\r\n\r\nexport default function BadgeStatus({ status, statusText, className }) {\r\n  const style = status ? STATUS_STYLES[status] : \"bg-yellow-500 text-white\";\r\n  return (\r\n    <Badge rounded=\"full\" variant=\"ghost\" className={cn(`inline-flex items-center justify-center text-sm font-medium px-2`, style, className)}>\r\n      {statusText || formatStatusText(status)}\r\n    </Badge>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB;IACpB,CAAC,2GAAA,CAAA,iBAAc,CAAC,KAAK,CAAC,EAAE;IACxB,CAAC,2GAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,EAAE;IACvB,CAAC,2GAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,EAAE;IAC1B,CAAC,2GAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,EAAE;IAC3B,CAAC,2GAAA,CAAA,iBAAc,CAAC,gBAAgB,CAAC,EAAE;IACnC,CAAC,2GAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC,EAAE;IACpC,CAAC,2GAAA,CAAA,iBAAc,CAAC,sBAAsB,CAAC,EAAE;IACzC,CAAC,2GAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,EAAE;AACpC;AAEe,SAAS,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE;IACnE,MAAM,QAAQ,SAAS,aAAa,CAAC,OAAO,GAAG;IAC/C,qBACE,8OAAC,0HAAA,CAAA,QAAK;QAAC,SAAQ;QAAO,SAAQ;QAAQ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,CAAC,gEAAgE,CAAC,EAAE,OAAO;kBAC5H,cAAc,CAAA,GAAA,4GAAA,CAAA,mBAAgB,AAAD,EAAE;;;;;;AAGtC", "debugId": null}}, {"offset": {"line": 3971, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/CreatePropertyDetailInformation.jsx"], "sourcesContent": ["\"use client\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { CalendarIcon, Star, DollarSign, User, Timer } from \"lucide-react\";\r\nimport { format, addDays, set } from \"date-fns\";\r\nimport { vi } from \"date-fns/locale\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { DEFAULT_POST_PRICE, MemberRank, PropertyStatus } from \"@/lib/enum\";\r\nimport BadgeStatus from \"../layout/BadgeStatus\";\r\nimport BadgeUserRank from \"../layout/BadgeUserRank\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { getHighlightPriceNumber } from \"@/lib/memberRankUtils\";\r\nimport { FormControl, FormDescription, FormField, FormItem, FormLabel } from \"../ui/form\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Tooltip, TooltipContent, TooltipTrigger } from \"../ui/tooltip\";\r\n\r\nexport default function CreatePropertyDetailInformation({\r\n  form,\r\n  property,\r\n  isFormDisabled,\r\n  basePostPrice = DEFAULT_POST_PRICE,\r\n  onRankChange = () => {},\r\n  onRefreshRef = null,\r\n}) {\r\n  const t = useTranslations(\"CreatePropertyDetailInformation\");\r\n  const tCommon = useTranslations(\"Common\");\r\n  const { profile } = useAuth();\r\n  const displayDuration = 10; // days\r\n\r\n  // Get the user's rank from profile context\r\n  const userRankFromProfile = profile?.user?.memberRank || MemberRank.DEFAULT;\r\n\r\n  // State to track the current rank (can be updated by BadgeUserRank component)\r\n  const [currentRank, setCurrentRank] = useState(userRankFromProfile);\r\n\r\n  const status = property?.status || PropertyStatus.DRAFT;\r\n  const statusText = property?.status ? tCommon(`propertyStatus_${property?.status}`) : tCommon(`propertyStatus_${PropertyStatus.DRAFT}`);\r\n  const createdAt = property?.createdAt || new Date();\r\n  const expiresAt = property?.expiresAt;\r\n  const highlight = form.watch(\"isHighlighted\") || false;\r\n\r\n  // Update currentRank when profile changes\r\n  useEffect(() => {\r\n    if (profile?.user?.memberRank) {\r\n      setCurrentRank(profile?.user.memberRank);\r\n    }\r\n  }, [profile]);\r\n\r\n  // Calculate highlight price using the utility function\r\n  const highlightPrice = getHighlightPriceNumber(currentRank);\r\n  const totalPrice = basePostPrice + (highlight ? highlightPrice : 0);\r\n\r\n  const expirationDate = expiresAt || addDays(createdAt, displayDuration);\r\n\r\n  return (\r\n    <div className=\"space-y-3\">\r\n      {/* Customer Information Section */}\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex flex-col gap-2\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <User className=\"h-4 w-4 text-blue-600\" />\r\n            <h5 className=\"font-medium text-sm text-gray-900 mb-0\">{t(\"individualCustomer\")}</h5>\r\n          </div>\r\n          <BadgeUserRank\r\n            showRefreshButton={true}\r\n            onRankChange={(rankData) => {\r\n              // Only update if the rank has actually changed\r\n              if (rankData.currentRank !== currentRank) {\r\n                setCurrentRank(rankData.currentRank);\r\n                onRankChange(rankData);\r\n              }\r\n            }}\r\n            onRefreshRef={onRefreshRef}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Post Options Section */}\r\n      {!isFormDisabled && (\r\n        <div className=\"space-y-2\">\r\n          <FormField\r\n            control={form.control}\r\n            name=\"isHighlighted\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"bg-linear-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <Tooltip>\r\n                    <TooltipTrigger>\r\n                      <div className=\"flex items-center gap-2 space-y-1\">\r\n                        <Star className=\"h-4 w-4 text-yellow-600\" />\r\n                        <FormLabel className=\"font-medium text-sm text-gray-900\">{t(\"highlightPost\")}</FormLabel>\r\n                      </div>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>{t(\"highlightPostDescription\")}</TooltipContent>\r\n                  </Tooltip>\r\n                  <FormControl>\r\n                    <Switch checked={field.value} onCheckedChange={field.onChange} className=\"data-[state=checked]:bg-yellow-600\" />\r\n                  </FormControl>\r\n                </div>\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          <FormField\r\n            control={form.control}\r\n            name=\"isAutoRenew\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"bg-linear-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <Tooltip>\r\n                    <TooltipTrigger>\r\n                      <div className=\"flex items-center gap-2 space-y-1\">\r\n                        <Timer className=\"h-4 w-4 text-green-600\" />\r\n                        <FormLabel className=\"font-medium text-gray-900\">{t(\"autoRenew\")}</FormLabel>\r\n                      </div>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>{t(\"autoRenewDescription\")}</TooltipContent>\r\n                  </Tooltip>\r\n                  <FormControl>\r\n                    <Switch checked={field.value} onCheckedChange={field.onChange} className=\"data-[state=checked]:bg-green-600\" />\r\n                  </FormControl>\r\n                </div>\r\n              </FormItem>\r\n            )}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {/* Post Information Section */}\r\n      <div className=\"rounded-lg p-4 space-y-4 bg-gray-50\">\r\n        <div className=\"grid grid-cols-1 gap-1\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <Label className=\"text-xs font-medium text-gray-600 tracking-wide\">{t(\"postStatus\")}</Label>\r\n            <BadgeStatus status={status} statusText={statusText} rounded=\"full\" />\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between\">\r\n            <Label className=\"text-xs font-medium text-gray-600 tracking-wide\">{t(\"expirationTime\")}</Label>\r\n            <div className=\"flex items-center gap-1 text-sm font-medium text-gray-900\">\r\n              <Timer className=\"h-3 w-3 text-blue-600\" />\r\n              {displayDuration} {t(\"days\")}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between\">\r\n            <Label className=\"text-xs font-medium text-gray-600 tracking-wide\">{t(\"postDate\")}</Label>\r\n            <div className=\"flex items-center gap-1 text-sm font-medium text-gray-900\">\r\n              <CalendarIcon className=\"h-3 w-3 text-blue-600\" />\r\n              {format(createdAt, \"dd/MM/yyyy\", { locale: vi })}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between\">\r\n            <Label className=\"text-xs font-medium text-gray-600 tracking-wide\">{t(\"expirationDate\")}</Label>\r\n            <div className=\"flex items-center gap-1 text-sm font-medium text-gray-900\">\r\n              <CalendarIcon className=\"h-3 w-3 text-blue-600\" />\r\n              {format(expirationDate, \"dd/MM/yyyy\", { locale: vi })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Pricing Section */}\r\n      <div className=\"space-y-2\">\r\n        <Separator />\r\n        <div className=\"bg-linear-to-r from-gray-50 to-gray-100 rounded-lg p-3 space-y-2\">\r\n          <div className=\"flex items-center gap-2 mb-3\">\r\n            <DollarSign className=\"h-4 w-4 text-teal-600\" />\r\n            <h4 className=\"font-medium text-gray-900 text-sm mb-0\">{t(\"postFee\")}</h4>\r\n          </div>\r\n\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between items-center\">\r\n              <span className=\"text-sm text-gray-600\">{t(\"postFee\")}</span>\r\n              <span className=\"font-medium text-gray-900\">{basePostPrice.toLocaleString(\"vi-VN\")} đ</span>\r\n            </div>\r\n\r\n            {highlight && (\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm text-gray-600\">{t(\"highlightFee\")}</span>\r\n                <span className=\"font-medium text-yellow-700\">{highlightPrice.toLocaleString(\"vi-VN\")} đ</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <Separator />\r\n\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"font-semibold text-gray-900\">{t(\"total\")}</span>\r\n            <span className=\"font-bold text-lg text-green-700\">{totalPrice.toLocaleString(\"vi-VN\")} đ</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;;;AAiBe,SAAS,gCAAgC,EACtD,IAAI,EACJ,QAAQ,EACR,cAAc,EACd,gBAAgB,2GAAA,CAAA,qBAAkB,EAClC,eAAe,KAAO,CAAC,EACvB,eAAe,IAAI,EACpB;IACC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,kBAAkB,IAAI,OAAO;IAEnC,2CAA2C;IAC3C,MAAM,sBAAsB,SAAS,MAAM,cAAc,2GAAA,CAAA,aAAU,CAAC,OAAO;IAE3E,8EAA8E;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,SAAS,UAAU,UAAU,2GAAA,CAAA,iBAAc,CAAC,KAAK;IACvD,MAAM,aAAa,UAAU,SAAS,QAAQ,CAAC,eAAe,EAAE,UAAU,QAAQ,IAAI,QAAQ,CAAC,eAAe,EAAE,2GAAA,CAAA,iBAAc,CAAC,KAAK,EAAE;IACtI,MAAM,YAAY,UAAU,aAAa,IAAI;IAC7C,MAAM,YAAY,UAAU;IAC5B,MAAM,YAAY,KAAK,KAAK,CAAC,oBAAoB;IAEjD,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,YAAY;YAC7B,eAAe,SAAS,KAAK;QAC/B;IACF,GAAG;QAAC;KAAQ;IAEZ,uDAAuD;IACvD,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE;IAC/C,MAAM,aAAa,gBAAgB,CAAC,YAAY,iBAAiB,CAAC;IAElE,MAAM,iBAAiB,aAAa,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IAEvD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAG,WAAU;8CAA0C,EAAE;;;;;;;;;;;;sCAE5D,8OAAC,sIAAA,CAAA,UAAa;4BACZ,mBAAmB;4BACnB,cAAc,CAAC;gCACb,+CAA+C;gCAC/C,IAAI,SAAS,WAAW,KAAK,aAAa;oCACxC,eAAe,SAAS,WAAW;oCACnC,aAAa;gCACf;4BACF;4BACA,cAAc;;;;;;;;;;;;;;;;;YAMnB,CAAC,gCACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;gCAAC,WAAU;0CAClB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4HAAA,CAAA,UAAO;;8DACN,8OAAC,4HAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC,yHAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC,EAAE;;;;;;;;;;;;;;;;;8DAGhE,8OAAC,4HAAA,CAAA,iBAAc;8DAAE,EAAE;;;;;;;;;;;;sDAErB,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,2HAAA,CAAA,SAAM;gDAAC,SAAS,MAAM,KAAK;gDAAE,iBAAiB,MAAM,QAAQ;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnF,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;gCAAC,WAAU;0CAClB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4HAAA,CAAA,UAAO;;8DACN,8OAAC,4HAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC,yHAAA,CAAA,YAAS;gEAAC,WAAU;0EAA6B,EAAE;;;;;;;;;;;;;;;;;8DAGxD,8OAAC,4HAAA,CAAA,iBAAc;8DAAE,EAAE;;;;;;;;;;;;sDAErB,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,2HAAA,CAAA,SAAM;gDAAC,SAAS,MAAM,KAAK;gDAAE,iBAAiB,MAAM,QAAQ;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,WAAU;8CAAmD,EAAE;;;;;;8CACtE,8OAAC,oIAAA,CAAA,UAAW;oCAAC,QAAQ;oCAAQ,YAAY;oCAAY,SAAQ;;;;;;;;;;;;sCAG/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,WAAU;8CAAmD,EAAE;;;;;;8CACtE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAChB;wCAAgB;wCAAE,EAAE;;;;;;;;;;;;;sCAIzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,WAAU;8CAAmD,EAAE;;;;;;8CACtE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8MAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,cAAc;4CAAE,QAAQ,2IAAA,CAAA,KAAE;wCAAC;;;;;;;;;;;;;sCAIlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,WAAU;8CAAmD,EAAE;;;;;;8CACtE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8MAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,gBAAgB,cAAc;4CAAE,QAAQ,2IAAA,CAAA,KAAE;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAO3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8HAAA,CAAA,YAAS;;;;;kCACV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAG,WAAU;kDAA0C,EAAE;;;;;;;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAyB,EAAE;;;;;;0DAC3C,8OAAC;gDAAK,WAAU;;oDAA6B,cAAc,cAAc,CAAC;oDAAS;;;;;;;;;;;;;oCAGpF,2BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAyB,EAAE;;;;;;0DAC3C,8OAAC;gDAAK,WAAU;;oDAA+B,eAAe,cAAc,CAAC;oDAAS;;;;;;;;;;;;;;;;;;;0CAI5F,8OAAC,8HAAA,CAAA,YAAS;;;;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA+B,EAAE;;;;;;kDACjD,8OAAC;wCAAK,WAAU;;4CAAoC,WAAW,cAAc,CAAC;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnG", "debugId": null}}, {"offset": {"line": 4581, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/table.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Table = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props} />\r\n  </div>\r\n))\r\nTable.displayName = \"Table\"\r\n\r\nconst TableHeader = React.forwardRef(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n))\r\nTableHeader.displayName = \"TableHeader\"\r\n\r\nconst TableBody = React.forwardRef(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props} />\r\n))\r\nTableBody.displayName = \"TableBody\"\r\n\r\nconst TableFooter = React.forwardRef(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\"border-t bg-muted/50 font-medium last:[&>tr]:border-b-0\", className)}\r\n    {...props} />\r\n))\r\nTableFooter.displayName = \"TableFooter\"\r\n\r\nconst TableRow = React.forwardRef(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nTableRow.displayName = \"TableRow\"\r\n\r\nconst TableHead = React.forwardRef(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nTableHead.displayName = \"TableHead\"\r\n\r\nconst TableCell = React.forwardRef(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\r\n      \"p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nTableCell.displayName = \"TableCell\"\r\n\r\nconst TableCaption = React.forwardRef(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nTableCaption.displayName = \"TableCaption\"\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACvD,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAGf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2DAA2D;QACxE,GAAG,KAAK;;;;;;AAEb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAEb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wFACA;QAED,GAAG,KAAK;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC9D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAEb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4691, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/PricingDialog.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { <PERSON><PERSON>, DialogTrigger, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from \"@/components/ui/dialog\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from \"@/components/ui/table\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\"\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\r\nimport { InfoIcon, CheckCircle2, Clock, Zap, Shield, User, Coins, Award } from \"lucide-react\"\r\nimport { useTranslations } from \"next-intl\";\r\nimport { getMemberRankColor, getMemberRankIcon, getAllMemberRankPrices, getMemberRankThresholds, getMemberRankTranslationKey } from \"@/lib/memberRankUtils\";\r\n\r\nexport default function PricingDialog() {\r\n  const t = useTranslations(\"PricingPage\");\r\n  const tCommon = useTranslations(\"Common\");\r\n\r\n  return (\r\n    <Dialog>\r\n      <DialogTrigger asChild>\r\n        <Button variant=\"outline\" size=\"sm\" className=\"gap-1.5\">\r\n          <InfoIcon className=\"h-4 w-4\" />\r\n          {t('viewPricing')}\r\n        </Button>\r\n      </DialogTrigger>\r\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto p-6 md:p-8\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-2xl font-bold mb-2\">{t('pricingTable')}</DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <Tabs defaultValue=\"individual\" className=\"mt-6\">\r\n          <TabsList className=\"grid w-full grid-cols-3 mb-6\">\r\n            <TabsTrigger value=\"individual\">{t('individualCustomer')}</TabsTrigger>\r\n            <TabsTrigger value=\"membership\">{t('userRank')}</TabsTrigger>\r\n            <TabsTrigger value=\"highlight\">{t('highlightPost')}</TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"individual\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <User className=\"h-5 w-5 text-primary\" />\r\n                  {t('individualCustomerService')}\r\n                </CardTitle>\r\n                <CardDescription>{t('servicePriceIncludes10VAT')}</CardDescription>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <Table>\r\n                  <TableHeader className=\"bg-muted/50\">\r\n                    <TableRow>\r\n                      <TableHead className=\"w-[40%]\">{t('serviceHeader')}</TableHead>\r\n                      <TableHead className=\"w-[30%]\">{t('price')}</TableHead>\r\n                      <TableHead className=\"w-[30%]\">{t('displayTime')}</TableHead>\r\n                    </TableRow>\r\n                  </TableHeader>\r\n                  <TableBody>\r\n                    <TableRow className=\"hover:bg-muted/30\">\r\n                      <TableCell className=\"font-medium\">{t('eachPost')}</TableCell>\r\n                      <TableCell className=\"font-semibold text-primary\">55,000</TableCell>\r\n                      <TableCell className=\"flex items-center gap-1.5\">\r\n                        <Clock className=\"h-4 w-4 text-muted-foreground\" />\r\n                        <span>{t('10Days')}</span>\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  </TableBody>\r\n                </Table>\r\n              </CardContent>\r\n            </Card>\r\n\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <CheckCircle2 className=\"h-5 w-5 text-green-500\" />\r\n                  {t('memberBenefits')}\r\n                </CardTitle>\r\n                <CardDescription>{t('memberBenefitsDescription')}</CardDescription>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <ul className=\"space-y-3\">\r\n                  <li className=\"flex items-start gap-2\">\r\n                    <Badge rounded=\"full\" variant=\"outline\" className=\"mt-0.5\">\r\n                      <Shield className=\"h-3.5 w-3.5 mr-1\" />\r\n                      {t('trust')}\r\n                    </Badge>\r\n                    <span>{t('trustDescription')}</span>\r\n                  </li>\r\n                  <li className=\"flex items-start gap-2\">\r\n                    <Badge rounded=\"full\" variant=\"outline\" className=\"mt-0.5\">\r\n                      <Zap className=\"h-3.5 w-3.5 mr-1\" />\r\n                      {t('quick')}\r\n                    </Badge>\r\n                    <span>{t('quickDescription')}</span>\r\n                  </li>\r\n                  <li className=\"flex items-start gap-2\">\r\n                    <Badge rounded=\"full\" variant=\"outline\" className=\"mt-0.5\">\r\n                      <Coins className=\"h-3.5 w-3.5 mr-1\" />\r\n                      {t('save')}\r\n                    </Badge>\r\n                    <span>{t('saveDescription')}</span>\r\n                  </li>\r\n                </ul>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"membership\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <Award className=\"h-5 w-5 text-primary\" />\r\n                  {t('userRank')}\r\n                </CardTitle>\r\n                <CardDescription>\r\n                  {t('userRankDescription')}\r\n                </CardDescription>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"grid gap-4 md:grid-cols-2\">\r\n                  {getMemberRankThresholds().map((item, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className=\"flex items-center p-4 rounded-lg border border-border bg-card hover:bg-muted/30 transition-colors\"\r\n                    >\r\n                      <div className=\"mr-4\">\r\n                        <div className={`p-2 rounded-full bg-muted ${getMemberRankColor(item.rank)}`}>\r\n                          {getMemberRankIcon(item.rank)}\r\n                        </div>\r\n                      </div>\r\n                      <div>\r\n                        <h4 className={`font-semibold ${getMemberRankColor(item.rank)}`}>{tCommon(getMemberRankTranslationKey(item.rank))}</h4>\r\n                        <p className=\"text-sm text-muted-foreground\">{item.condition}</p>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"highlight\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <Zap className=\"h-5 w-5 text-primary\" />\r\n                  {t('highlightCheckpointPrice')}\r\n                </CardTitle>\r\n                <CardDescription>{t('highlightCheckpointDescription')}</CardDescription>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <Table>\r\n                  <TableHeader className=\"bg-muted/50\">\r\n                    <TableRow>\r\n                      <TableHead className=\"w-[50%]\">{t('userRank')}</TableHead>\r\n                      <TableHead className=\"w-[50%]\">{t('price')}</TableHead>\r\n                    </TableRow>\r\n                  </TableHeader>\r\n                  <TableBody>\r\n                    {getAllMemberRankPrices().map((item, index) => (\r\n                      <TableRow key={index} className=\"hover:bg-muted/30\">\r\n                        <TableCell>\r\n                          <div className=\"flex items-center gap-2\">\r\n                            {getMemberRankIcon(item.rank)}\r\n                            <span className={`font-medium ${getMemberRankColor(item.rank)}`}>{tCommon(getMemberRankTranslationKey(item.rank))}</span>\r\n                          </div>\r\n                        </TableCell>\r\n                        <TableCell className=\"font-semibold\">{item.price}</TableCell>\r\n                      </TableRow>\r\n                    ))}\r\n                  </TableBody>\r\n                </Table>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAVA;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,qBACE,8OAAC,2HAAA,CAAA,SAAM;;0BACL,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,WAAU;;sCAC5C,8OAAC,sMAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBACnB,EAAE;;;;;;;;;;;;0BAGP,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,2HAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,2HAAA,CAAA,cAAW;4BAAC,WAAU;sCAA2B,EAAE;;;;;;;;;;;kCAGtD,8OAAC,yHAAA,CAAA,OAAI;wBAAC,cAAa;wBAAa,WAAU;;0CACxC,8OAAC,yHAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,yHAAA,CAAA,cAAW;wCAAC,OAAM;kDAAc,EAAE;;;;;;kDACnC,8OAAC,yHAAA,CAAA,cAAW;wCAAC,OAAM;kDAAc,EAAE;;;;;;kDACnC,8OAAC,yHAAA,CAAA,cAAW;wCAAC,OAAM;kDAAa,EAAE;;;;;;;;;;;;0CAGpC,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;;kDACxC,8OAAC,yHAAA,CAAA,OAAI;;0DACH,8OAAC,yHAAA,CAAA,aAAU;;kEACT,8OAAC,yHAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,EAAE;;;;;;;kEAEL,8OAAC,yHAAA,CAAA,kBAAe;kEAAE,EAAE;;;;;;;;;;;;0DAEtB,8OAAC,yHAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;;sEACJ,8OAAC,0HAAA,CAAA,cAAW;4DAAC,WAAU;sEACrB,cAAA,8OAAC,0HAAA,CAAA,WAAQ;;kFACP,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAAW,EAAE;;;;;;kFAClC,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAAW,EAAE;;;;;;kFAClC,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAAW,EAAE;;;;;;;;;;;;;;;;;sEAGtC,8OAAC,0HAAA,CAAA,YAAS;sEACR,cAAA,8OAAC,0HAAA,CAAA,WAAQ;gEAAC,WAAU;;kFAClB,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAAe,EAAE;;;;;;kFACtC,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAA6B;;;;;;kFAClD,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;;0FACnB,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;0FACjB,8OAAC;0FAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQrB,8OAAC,yHAAA,CAAA,OAAI;;0DACH,8OAAC,yHAAA,CAAA,aAAU;;kEACT,8OAAC,yHAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,8OAAC,qNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DACvB,EAAE;;;;;;;kEAEL,8OAAC,yHAAA,CAAA,kBAAe;kEAAE,EAAE;;;;;;;;;;;;0DAEtB,8OAAC,yHAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAO,SAAQ;oEAAU,WAAU;;sFAChD,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,EAAE;;;;;;;8EAEL,8OAAC;8EAAM,EAAE;;;;;;;;;;;;sEAEX,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAO,SAAQ;oEAAU,WAAU;;sFAChD,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEACd,EAAE;;;;;;;8EAEL,8OAAC;8EAAM,EAAE;;;;;;;;;;;;sEAEX,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAO,SAAQ;oEAAU,WAAU;;sFAChD,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,EAAE;;;;;;;8EAEL,8OAAC;8EAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOnB,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;0CACjB,cAAA,8OAAC,yHAAA,CAAA,OAAI;;sDACH,8OAAC,yHAAA,CAAA,aAAU;;8DACT,8OAAC,yHAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,EAAE;;;;;;;8DAEL,8OAAC,yHAAA,CAAA,kBAAe;8DACb,EAAE;;;;;;;;;;;;sDAGP,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,IAAI,GAAG,CAAC,CAAC,MAAM,sBACpC,8OAAC;wDAEC,WAAU;;0EAEV,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAW,CAAC,0BAA0B,EAAE,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI,GAAG;8EACzE,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;0EAGhC,8OAAC;;kFACC,8OAAC;wEAAG,WAAW,CAAC,cAAc,EAAE,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI,GAAG;kFAAG,QAAQ,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD,EAAE,KAAK,IAAI;;;;;;kFAC/G,8OAAC;wEAAE,WAAU;kFAAiC,KAAK,SAAS;;;;;;;;;;;;;uDAVzD;;;;;;;;;;;;;;;;;;;;;;;;;;0CAmBjB,8OAAC,yHAAA,CAAA,cAAW;gCAAC,OAAM;0CACjB,cAAA,8OAAC,yHAAA,CAAA,OAAI;;sDACH,8OAAC,yHAAA,CAAA,aAAU;;8DACT,8OAAC,yHAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACd,EAAE;;;;;;;8DAEL,8OAAC,yHAAA,CAAA,kBAAe;8DAAE,EAAE;;;;;;;;;;;;sDAEtB,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;;kEACJ,8OAAC,0HAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,8OAAC,0HAAA,CAAA,WAAQ;;8EACP,8OAAC,0HAAA,CAAA,YAAS;oEAAC,WAAU;8EAAW,EAAE;;;;;;8EAClC,8OAAC,0HAAA,CAAA,YAAS;oEAAC,WAAU;8EAAW,EAAE;;;;;;;;;;;;;;;;;kEAGtC,8OAAC,0HAAA,CAAA,YAAS;kEACP,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,IAAI,GAAG,CAAC,CAAC,MAAM,sBACnC,8OAAC,0HAAA,CAAA,WAAQ;gEAAa,WAAU;;kFAC9B,8OAAC,0HAAA,CAAA,YAAS;kFACR,cAAA,8OAAC;4EAAI,WAAU;;gFACZ,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,IAAI;8FAC5B,8OAAC;oFAAK,WAAW,CAAC,YAAY,EAAE,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI,GAAG;8FAAG,QAAQ,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;;;;;;;kFAGnH,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAAiB,KAAK,KAAK;;;;;;;+DAPnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBrC", "debugId": null}}, {"offset": {"line": 5396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/user-property/LocationSelector.jsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, forwardRef, useImperativeHandle, useRef } from \"react\";\nimport { FormField, FormItem, FormLabel, FormMessage, FormControl } from \"@/components/ui/form\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { useTranslations } from \"next-intl\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { FormType } from \"@/lib/enum\";\n\nconst LocationSelector = forwardRef(({ form, isFormDisabled, property, formType, targetLocationNames, setTargetLocationNames }, ref) => {\n  const t = useTranslations(\"PropertyForm\");\n  const { toast } = useToast();\n\n  const [cities, setCities] = useState([]);\n  const [districts, setDistricts] = useState([]);\n  const [wards, setWards] = useState([]);\n\n  const prevSelectedCityRef = useRef();\n  const prevSelectedDistrictRef = useRef();\n  const prevSelectedWardRef = useRef();\n\n  // Expose cities, districts, and wards data to parent component\n  useImperativeHandle(ref, () => ({\n    cities,\n    districts,\n    wards,\n  }));\n\n  const selectedCity = form.watch(\"cityId\");\n  const selectedDistrict = form.watch(\"districtId\");\n\n  useEffect(() => {\n    if (property && formType === FormType.EDIT) {\n      prevSelectedCityRef.current = property?.cityId.toString();\n      prevSelectedDistrictRef.current = property?.districtId.toString();\n      prevSelectedWardRef.current = property?.wardId.toString();\n    }\n  }, [property, formType]);\n\n  // Fetch cities\n  useEffect(() => {\n    const fetchCities = async () => {\n      try {\n        const response = await fetch(`/api/map/cities`);\n        if (!response.ok) throw new Error(t(\"fetchCitiesError\"));\n        const data = await response.json();\n        setCities(data);\n      } catch (error) {\n        toast({ variant: \"destructive\", description: error.message || t(\"fetchCitiesError\") });\n      }\n    };\n\n    fetchCities();\n  }, [form, toast, t]);\n\n  // Fetch districts based on selected city\n  useEffect(() => {\n    const prev = prevSelectedCityRef.current;\n    const current = selectedCity;\n\n    if (!selectedCity && !targetLocationNames) {\n      setDistricts([]);\n      setWards([]);\n      form.setValue(\"districtId\", \"\");\n      form.setValue(\"wardId\", \"\");\n      return;\n    }\n\n    const fetchDistricts = async () => {\n      const controller = new AbortController();\n      try {\n        const response = await fetch(`/api/map/districts?cityId=${selectedCity}`, {\n          signal: controller.signal,\n        });\n        if (!response.ok) throw new Error(t(\"fetchDistrictsError\"));\n        const data = await response.json();\n        setDistricts(data);\n\n        if (targetLocationNames && targetLocationNames.district) {\n          const matchDistrictId = data.find((district) => district.nameWithType === targetLocationNames.district);\n          form.setValue(\"districtId\", matchDistrictId?.id.toString());\n        } else if (prev && prev !== current) {\n          form.setValue(\"districtId\", \"\");\n        }\n      } catch (error) {\n        if (error.name !== \"AbortError\") {\n          toast({ variant: \"destructive\", description: error.message || t(\"fetchDistrictsError\") });\n        }\n      }\n      return () => controller.abort();\n    };\n\n    fetchDistricts();\n    prevSelectedCityRef.current = selectedCity;\n  }, [selectedCity, form, t, targetLocationNames]);\n\n  // Fetch wards based on selected district\n  useEffect(() => {\n    const prev = prevSelectedDistrictRef.current;\n    const current = selectedDistrict;\n\n    if ((!selectedDistrict || selectedDistrict === \"\") && !targetLocationNames) {\n      setWards([]);\n      form.setValue(\"wardId\", \"\");\n      return;\n    }\n\n    const fetchWards = async () => {\n      const controller = new AbortController();\n      try {\n\n        if (!selectedDistrict || selectedDistrict === \"\") return;\n\n        const response = await fetch(`/api/map/wards?districtId=${selectedDistrict}`, {\n          signal: controller.signal,\n        });\n        if (!response.ok) throw new Error(t(\"fetchWardsError\"));\n        const data = await response.json();\n        setWards(data);\n\n        if (targetLocationNames && targetLocationNames.ward) {\n          const matchWardId = data.find((ward) => ward.nameWithType === targetLocationNames.ward);\n          form.setValue(\"wardId\", matchWardId?.id.toString());\n        } else if (prev && prev !== current) {\n          form.setValue(\"wardId\", \"\");\n        }\n      } catch (error) {\n        if (error.name !== \"AbortError\") {\n          toast({ variant: \"destructive\", description: error.message || t(\"fetchWardsError\") });\n        }\n      }\n      return () => controller.abort();\n    };\n\n    fetchWards();\n    prevSelectedDistrictRef.current = selectedDistrict;\n  }, [selectedDistrict, form, t, targetLocationNames]);\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mt-3\">\n      <div>\n        {/* City Select */}\n        <FormField\n          control={form.control}\n          name=\"cityId\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>{t(\"city\")}</FormLabel>\n              <FormControl>\n                <Select\n                  onValueChange={(e) => {\n                    field.onChange(e);\n                  }}\n                  defaultValue={field.value ?? \"\"}\n                  value={field.value}\n                  disabled={isFormDisabled}\n                >\n                  <SelectTrigger className=\"w-full\">\n                    <SelectValue placeholder={t(\"cityPlaceholder\")} />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {cities?.map((city) => (\n                      <SelectItem key={city.id} value={city?.id?.toString()}>\n                        {city.nameWithType}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n      </div>\n      <div>\n        {/* District Select */}\n        <FormField\n          control={form.control}\n          name=\"districtId\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>{t(\"district\")}</FormLabel>\n              <FormControl>\n                <Select\n                  onValueChange={field.onChange}\n                  defaultValue={field.value ?? \"\"}\n                  value={field.value}\n                  disabled={!selectedCity || isFormDisabled}\n                >\n                  <SelectTrigger className=\"w-full\">\n                    <SelectValue placeholder={t(\"districtPlaceholder\")} />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {districts.map((district) => (\n                      <SelectItem key={district.id} value={district.id?.toString()}>\n                        {district.nameWithType}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n      </div>\n      <div>\n        {/* Ward Select */}\n        <FormField\n          control={form.control}\n          name=\"wardId\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>{t(\"ward\")}</FormLabel>\n              <FormControl>\n                <Select\n                  onValueChange={field.onChange}\n                  defaultValue={field.value ?? \"\"}\n                  value={field.value}\n                  disabled={!selectedDistrict || isFormDisabled}\n                >\n                  <SelectTrigger className=\"w-full\">\n                    <SelectValue placeholder={t(\"wardPlaceholder\")} />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {wards.map((ward) => (\n                      <SelectItem key={ward.id} value={ward?.id?.toString()}>\n                        {ward.nameWithType}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n      </div>\n    </div>\n  );\n});\n\nexport default LocationSelector;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,EAAE;IAC9H,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAErC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACjC,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACrC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEjC,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAM,CAAC;YAC9B;YACA;YACA;QACF,CAAC;IAED,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,mBAAmB,KAAK,KAAK,CAAC;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,aAAa,2GAAA,CAAA,WAAQ,CAAC,IAAI,EAAE;YAC1C,oBAAoB,OAAO,GAAG,UAAU,OAAO;YAC/C,wBAAwB,OAAO,GAAG,UAAU,WAAW;YACvD,oBAAoB,OAAO,GAAG,UAAU,OAAO;QACjD;IACF,GAAG;QAAC;QAAU;KAAS;IAEvB,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,CAAC;gBAC9C,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU;YACZ,EAAE,OAAO,OAAO;gBACd,MAAM;oBAAE,SAAS;oBAAe,aAAa,MAAM,OAAO,IAAI,EAAE;gBAAoB;YACtF;QACF;QAEA;IACF,GAAG;QAAC;QAAM;QAAO;KAAE;IAEnB,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,oBAAoB,OAAO;QACxC,MAAM,UAAU;QAEhB,IAAI,CAAC,gBAAgB,CAAC,qBAAqB;YACzC,aAAa,EAAE;YACf,SAAS,EAAE;YACX,KAAK,QAAQ,CAAC,cAAc;YAC5B,KAAK,QAAQ,CAAC,UAAU;YACxB;QACF;QAEA,MAAM,iBAAiB;YACrB,MAAM,aAAa,IAAI;YACvB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,0BAA0B,EAAE,cAAc,EAAE;oBACxE,QAAQ,WAAW,MAAM;gBAC3B;gBACA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,aAAa;gBAEb,IAAI,uBAAuB,oBAAoB,QAAQ,EAAE;oBACvD,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,WAAa,SAAS,YAAY,KAAK,oBAAoB,QAAQ;oBACtG,KAAK,QAAQ,CAAC,cAAc,iBAAiB,GAAG;gBAClD,OAAO,IAAI,QAAQ,SAAS,SAAS;oBACnC,KAAK,QAAQ,CAAC,cAAc;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,IAAI,MAAM,IAAI,KAAK,cAAc;oBAC/B,MAAM;wBAAE,SAAS;wBAAe,aAAa,MAAM,OAAO,IAAI,EAAE;oBAAuB;gBACzF;YACF;YACA,OAAO,IAAM,WAAW,KAAK;QAC/B;QAEA;QACA,oBAAoB,OAAO,GAAG;IAChC,GAAG;QAAC;QAAc;QAAM;QAAG;KAAoB;IAE/C,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,wBAAwB,OAAO;QAC5C,MAAM,UAAU;QAEhB,IAAI,CAAC,CAAC,oBAAoB,qBAAqB,EAAE,KAAK,CAAC,qBAAqB;YAC1E,SAAS,EAAE;YACX,KAAK,QAAQ,CAAC,UAAU;YACxB;QACF;QAEA,MAAM,aAAa;YACjB,MAAM,aAAa,IAAI;YACvB,IAAI;gBAEF,IAAI,CAAC,oBAAoB,qBAAqB,IAAI;gBAElD,MAAM,WAAW,MAAM,MAAM,CAAC,0BAA0B,EAAE,kBAAkB,EAAE;oBAC5E,QAAQ,WAAW,MAAM;gBAC3B;gBACA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS;gBAET,IAAI,uBAAuB,oBAAoB,IAAI,EAAE;oBACnD,MAAM,cAAc,KAAK,IAAI,CAAC,CAAC,OAAS,KAAK,YAAY,KAAK,oBAAoB,IAAI;oBACtF,KAAK,QAAQ,CAAC,UAAU,aAAa,GAAG;gBAC1C,OAAO,IAAI,QAAQ,SAAS,SAAS;oBACnC,KAAK,QAAQ,CAAC,UAAU;gBAC1B;YACF,EAAE,OAAO,OAAO;gBACd,IAAI,MAAM,IAAI,KAAK,cAAc;oBAC/B,MAAM;wBAAE,SAAS;wBAAe,aAAa,MAAM,OAAO,IAAI,EAAE;oBAAmB;gBACrF;YACF;YACA,OAAO,IAAM,WAAW,KAAK;QAC/B;QAEA;QACA,wBAAwB,OAAO,GAAG;IACpC,GAAG;QAAC;QAAkB;QAAM;QAAG;KAAoB;IAEnD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;0BAEC,cAAA,8OAAC,yHAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;8CACP,8OAAC,yHAAA,CAAA,YAAS;8CAAE,EAAE;;;;;;8CACd,8OAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,eAAe,CAAC;4CACd,MAAM,QAAQ,CAAC;wCACjB;wCACA,cAAc,MAAM,KAAK,IAAI;wCAC7B,OAAO,MAAM,KAAK;wCAClB,UAAU;;0DAEV,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,8OAAC,2HAAA,CAAA,gBAAa;0DACX,QAAQ,IAAI,CAAC,qBACZ,8OAAC,2HAAA,CAAA,aAAU;wDAAe,OAAO,MAAM,IAAI;kEACxC,KAAK,YAAY;uDADH,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;8CAOhC,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;0BAKpB,8OAAC;0BAEC,cAAA,8OAAC,yHAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;8CACP,8OAAC,yHAAA,CAAA,YAAS;8CAAE,EAAE;;;;;;8CACd,8OAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,eAAe,MAAM,QAAQ;wCAC7B,cAAc,MAAM,KAAK,IAAI;wCAC7B,OAAO,MAAM,KAAK;wCAClB,UAAU,CAAC,gBAAgB;;0DAE3B,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,8OAAC,2HAAA,CAAA,gBAAa;0DACX,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,2HAAA,CAAA,aAAU;wDAAmB,OAAO,SAAS,EAAE,EAAE;kEAC/C,SAAS,YAAY;uDADP,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;8CAOpC,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;0BAKpB,8OAAC;0BAEC,cAAA,8OAAC,yHAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;8CACP,8OAAC,yHAAA,CAAA,YAAS;8CAAE,EAAE;;;;;;8CACd,8OAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,eAAe,MAAM,QAAQ;wCAC7B,cAAc,MAAM,KAAK,IAAI;wCAC7B,OAAO,MAAM,KAAK;wCAClB,UAAU,CAAC,oBAAoB;;0DAE/B,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;oDAAC,aAAa,EAAE;;;;;;;;;;;0DAE9B,8OAAC,2HAAA,CAAA,gBAAa;0DACX,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,2HAAA,CAAA,aAAU;wDAAe,OAAO,MAAM,IAAI;kEACxC,KAAK,YAAY;uDADH,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;8CAOhC,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;uCAEe", "debugId": null}}, {"offset": {"line": 5808, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/bds/new/components/PropertySaveButtons.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useCallback } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\nimport { ArrowLeft, OctagonX } from \"lucide-react\";\nimport { Link } from \"@/i18n/navigation\";\nimport { FormType, CAN_NOT_EDIT_STATUS, PropertyStatus } from \"@/lib/enum\";\nimport { useTranslations } from \"next-intl\";\n\n// Memoize the component to prevent unnecessary re-renders\nconst PropertySaveButtons = React.memo(({\n  formType,\n  propertyStatus,\n  isPending,\n  formHandleSubmit,\n  onSubmit\n}) => {\n  const t = useTranslations(\"PropertyForm\");\n\n  // Memoize the handler for saving as draft\n  const handleSaveDraft = useCallback(() => {\n    return formHandleSubmit((data) => onSubmit(data, \"saveDraft\"))();\n  }, [formHandleSubmit, onSubmit]);\n\n  if (formType === FormType.EDIT && CAN_NOT_EDIT_STATUS.includes(propertyStatus)) {\n    return (\n      <div className=\"flex flex-col items-center justify-center\">\n        <Alert className=\"bg-yellow-100\">\n          <OctagonX className=\"h-4 w-4\" />\n          <AlertDescription>{t(\"propertyIsBeingReviewedOrApprovedOrSold\")}</AlertDescription>\n        </Alert>\n        <Link href=\"/user/bds\" className=\"mt-3 flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700\">\n          <ArrowLeft className=\"h-4 w-4\" /> {t(\"backToPropertyList\")}\n        </Link>\n      </div>\n    );\n  } else {\n    return (\n      <div className=\"flex gap-4 items-center justify-center\">\n        <Button\n          type=\"button\"\n          className=\"py-4 bg-gray-500 hover:bg-gray-600\"\n          onClick={handleSaveDraft}\n          disabled={isPending}\n        >\n          {isPending ? t(\"processing\") : t(\"saveDraft\")}\n        </Button>\n\n        <Button type=\"submit\" className=\"py-4 bg-teal-600 hover:bg-teal-700\" disabled={isPending}>\n          {isPending ? t(\"processing\") : t(\"saveAndSendForApproval\")}\n        </Button>\n      </div>\n    );\n  }\n}, (prevProps, nextProps) => {\n  // Only re-render if these props change\n  return (\n    prevProps.formType === nextProps.formType &&\n    prevProps.propertyStatus === nextProps.propertyStatus &&\n    prevProps.isPending === nextProps.isPending\n    // We don't compare formHandleSubmit and onSubmit as they should be memoized by the parent\n  );\n});\n\nexport default PropertySaveButtons;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,0DAA0D;AAC1D,MAAM,oCAAsB,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EACtC,QAAQ,EACR,cAAc,EACd,SAAS,EACT,gBAAgB,EAChB,QAAQ,EACT;IACC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,0CAA0C;IAC1C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,OAAO,iBAAiB,CAAC,OAAS,SAAS,MAAM;IACnD,GAAG;QAAC;QAAkB;KAAS;IAE/B,IAAI,aAAa,2GAAA,CAAA,WAAQ,CAAC,IAAI,IAAI,2GAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,iBAAiB;QAC9E,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0HAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,0HAAA,CAAA,mBAAgB;sCAAE,EAAE;;;;;;;;;;;;8BAEvB,8OAAC,kHAAA,CAAA,OAAI;oBAAC,MAAK;oBAAY,WAAU;;sCAC/B,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAY;wBAAE,EAAE;;;;;;;;;;;;;IAI7C,OAAO;QACL,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,2HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,UAAU;8BAET,YAAY,EAAE,gBAAgB,EAAE;;;;;;8BAGnC,8OAAC,2HAAA,CAAA,SAAM;oBAAC,MAAK;oBAAS,WAAU;oBAAqC,UAAU;8BAC5E,YAAY,EAAE,gBAAgB,EAAE;;;;;;;;;;;;IAIzC;AACF,GAAG,CAAC,WAAW;IACb,uCAAuC;IACvC,OACE,UAAU,QAAQ,KAAK,UAAU,QAAQ,IACzC,UAAU,cAAc,KAAK,UAAU,cAAc,IACrD,UAAU,SAAS,KAAK,UAAU,SAAS;AAG/C;uCAEe", "debugId": null}}, {"offset": {"line": 5934, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/bds/new/PropertyForm.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRouter } from \"next/navigation\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { useState, useEffect, useActionState, useRef, startTransition, useCallback } from \"react\";\r\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { createProperty, updatePropertyById } from \"@/app/actions/server/property\";\r\nimport { propertyFormSchema } from \"@/lib/schemas/propertyFormSchema\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport { CircleAlert, HelpCircle, ShieldAlert } from \"lucide-react\";\r\nimport { CAN_NOT_EDIT_STATUS, DEFAULT_POST_PRICE, FormType, highlightPrices, PropertyStatus, PropertyType } from \"@/lib/enum\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { Card, CardContent, CardFooter, CardHeader } from \"@/components/ui/card\";\r\nimport { Label } from \"@/components/ui/label\";\r\n\r\nimport { useTranslations } from \"next-intl\";\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from \"@/components/ui/tooltip\";\r\n\r\nimport CollapseHeader from \"@/components/ui/collapse\";\r\nimport AdditionalInformation from \"@/components/user-property/AdditionalInformation\";\r\nimport PropertyPostInformation from \"@/components/user-property/PropertyPostInformation\";\r\nimport PropertyMediaSection from \"@/components/user-property/PropertyMediaSection\";\r\nimport PropertyBasicInfoSection from \"@/components/user-property/PropertyBasicInfoSection\";\r\nimport AddressInput from \"@/components/user-property/AddressInput\";\r\nimport RankChangeDialog from \"@/components/user-property/RankChangeDialog\";\r\nimport CreatePropertyDetailInformation from \"@/components/user-property/CreatePropertyDetailInformation\";\r\nimport PricingDialog from \"@/components/user-property/PricingDialog\";\r\nimport LocationSelector from \"@/components/user-property/LocationSelector\";\r\n\r\nimport dynamic from \"next/dynamic\";\r\nconst SelectedLocationMap = dynamic(() => import(\"./components/SelectedLocationMap\"), {\r\n  ssr: false,\r\n  loading: () => <div className=\"h-16 bg-white animate-pulse\"></div>,\r\n});\r\n\r\nimport PropertySaveButtons from \"./components/PropertySaveButtons\";\r\n\r\nconst initialState = {\r\n  errors: null,\r\n  message: null,\r\n  fieldValues: {\r\n    name: \"\",\r\n    description: \"\",\r\n  },\r\n};\r\n\r\nexport default function PropertyForm({ property: initialProperty, formType = FormType.NEW }) {\r\n  const t = useTranslations(\"PropertyForm\");\r\n  const router = useRouter();\r\n  const { toast } = useToast();\r\n\r\n  const [state, formAction, isPending] = useActionState(formType === FormType.NEW ? createProperty : updatePropertyById, initialState);\r\n\r\n  const formRef = useRef(null);\r\n\r\n  // State to manage property data that can be updated after successful submission\r\n  const [property, setProperty] = useState(initialProperty);\r\n  const [isFormDisabled, setIsFormDisabled] = useState(false);\r\n\r\n  const form = useForm({\r\n    resolver: zodResolver(propertyFormSchema),\r\n    defaultValues: {\r\n      formType: formType,\r\n      propertyId: property?.id || \"\",\r\n      videoUrl: property?.videoUrl || \"\",\r\n      postType: property?.postType || PropertyType.SALE,\r\n      propertyType: property?.propertyType || \"\",\r\n      price: property?.price || \"\",\r\n      cityId: property?.cityId?.toString() || \"\",\r\n      districtId: property?.districtId?.toString() || \"\",\r\n      wardId: property?.wardId?.toString() || \"\",\r\n      address: property?.address || \"\",\r\n      addressSelected: property?.addressSelected || \"\",\r\n      name: property?.name || \"\",\r\n      description: property?.description || \"\",\r\n      area: property?.area || \"\",\r\n      floors: property?.floors || \"\",\r\n      rooms: property?.rooms || \"\",\r\n      toilets: property?.toilets || \"\",\r\n      direction: property?.direction || \"\",\r\n      balconyDirection: property?.balconyDirection || \"\",\r\n      legality: property?.legality || \"\",\r\n      interior: property?.interior || \"\",\r\n      width: property?.width || \"\",\r\n      roadWidth: property?.roadWidth || \"\",\r\n      latitude: property?.latitude || \"\",\r\n      longitude: property?.longitude || \"\",\r\n      placeData: property?.placeData || \"\",\r\n      status: property?.status || PropertyStatus.DRAFT,\r\n      isHighlighted: property?.isHighlighted || false,\r\n      isAutoRenew: property?.isAutoRenew || false,\r\n      ...(state?.fields ?? {}),\r\n    },\r\n    mode: \"onChange\",\r\n    // disabled: isFormDisabled,\r\n  });\r\n\r\n  const [targetLocationNames, setTargetLocationNames] = useState(null);\r\n  const selectedCity = form.watch(\"cityId\");\r\n  const selectedDistrict = form.watch(\"districtId\");\r\n  const selectedWard = form.watch(\"wardId\");\r\n\r\n  const locationSelectorRef = useRef(null);\r\n\r\n  const [selectedLocation, setSelectedLocation] = useState(null);\r\n  // Always initialize with an empty string to ensure it's controlled\r\n  const [addressSelected, setAddressSelected] = useState(\"\");\r\n\r\n  const [uploadedFiles, setUploadedFiles] = useState([]);\r\n  const uploadedFilesRef = useRef([]);\r\n\r\n  const [addressSelectedInputRef, setAddressSelectedInputRef] = useState(null);\r\n\r\n  const [showRankChangeDialog, setShowRankChangeDialog] = useState(false);\r\n  const [rankChangeDetails, setRankChangeDetails] = useState(null);\r\n  const [basePostPrice, setBasePostPrice] = useState(DEFAULT_POST_PRICE);\r\n  const rankRefreshRef = useRef(null);\r\n\r\n  // Update property state when initialProperty changes\r\n  useEffect(() => {\r\n    setProperty(initialProperty);\r\n  }, [initialProperty]);\r\n\r\n  useEffect(() => {\r\n    const isFormDisabled = formType === FormType.EDIT && CAN_NOT_EDIT_STATUS.includes(property?.status);\r\n    setIsFormDisabled(isFormDisabled);\r\n  }, [formType, property?.status]);\r\n\r\n  useEffect(() => {\r\n    if (property && formType === FormType.EDIT) {\r\n      if (property.latitude && property.longitude) {\r\n        setSelectedLocation({ latitude: parseFloat(property.latitude), longitude: parseFloat(property.longitude) });\r\n      }\r\n\r\n      // Ensure we always set a string value\r\n      setAddressSelected(property.addressSelected || property.address || \"\");\r\n      if (property.placeData) {\r\n        try {\r\n          const placeData = JSON.parse(property.placeData);\r\n          if (placeData.result && placeData.result.formatted_address) {\r\n            setAddressSelected(placeData.result.formatted_address || \"\");\r\n          }\r\n        } catch (error) {\r\n          // Silent error handling\r\n        }\r\n      }\r\n    }\r\n  }, [property, formType]);\r\n\r\n  useEffect(() => {\r\n    if (state?.success === true) {\r\n      toast({\r\n        title: t(\"saveSuccess\"),\r\n        description: t(\"propertyUpdated\"),\r\n        className: \"bg-teal-600 text-white\",\r\n      });\r\n\r\n      // Update property state with the returned data for edit operations\r\n      if (formType === FormType.EDIT && state?.data) {\r\n        setProperty(state.data);\r\n      } else if (formType === FormType.EDIT) {\r\n        setProperty(prevProperty => ({\r\n          ...prevProperty,\r\n          status: PropertyStatus.PENDING_APPROVAL\r\n        }));\r\n      }\r\n\r\n      const timeout = setTimeout(() => {\r\n        if (formType === FormType.NEW) {\r\n          if (state?.data?.status === PropertyStatus.DRAFT) {\r\n            router.push(\"/user/bds\");\r\n          } else {\r\n            router.push(\"/user/bds/new/success\");\r\n          }\r\n        }\r\n      }, 300);\r\n\r\n      return () => clearTimeout(timeout);\r\n    } else if (state?.success === false) {\r\n      toast({\r\n        title: t(\"saveFailed\"),\r\n        description: state.message || t(\"propertyNotUpdated\"),\r\n        className: \"bg-red-600 text-white\",\r\n      });\r\n    }\r\n  }, [state, router, formType, t]);\r\n\r\n  useEffect(() => {\r\n    if (property?.propertyMedia) {\r\n      setUploadedFiles(property.propertyMedia);\r\n    }\r\n  }, [property]);\r\n\r\n  // Update ref whenever uploadedFiles state changes\r\n  useEffect(() => {\r\n    uploadedFilesRef.current = uploadedFiles;\r\n  }, [uploadedFiles]);\r\n\r\n  const onUploadComplete = (files) => {\r\n    setUploadedFiles(files);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const formValues = {\r\n      formType: formType,\r\n      propertyId: property?.id || \"\",\r\n      videoUrl: property?.videoUrl || \"\",\r\n      postType: property?.postType || PropertyType.SALE,\r\n      propertyType: property?.propertyType || \"\",\r\n      price: property?.price || \"\",\r\n      cityId: property?.cityId?.toString() || \"\",\r\n      districtId: property?.districtId?.toString() || \"\",\r\n      wardId: property?.wardId?.toString() || \"\",\r\n      address: property?.address || \"\",\r\n      addressSelected: property?.addressSelected || \"\",\r\n      name: property?.name || \"\",\r\n      description: property?.description || \"\",\r\n      area: property?.area || \"\",\r\n      floors: property?.floors || \"\",\r\n      rooms: property?.rooms || \"\",\r\n      toilets: property?.toilets || \"\",\r\n      direction: property?.direction || \"\",\r\n      balconyDirection: property?.balconyDirection || \"\",\r\n      legality: property?.legality || \"\",\r\n      interior: property?.interior || \"\",\r\n      width: property?.width || \"\",\r\n      roadWidth: property?.roadWidth || \"\",\r\n      latitude: property?.latitude || \"\",\r\n      longitude: property?.longitude || \"\",\r\n      placeData: property?.placeData || \"\",\r\n      status: property?.status || PropertyStatus.DRAFT,\r\n      isHighlighted: property?.isHighlighted || false,\r\n      isAutoRenew: property?.isAutoRenew || false,\r\n      ...(state?.fields ?? {}),\r\n    };\r\n    form.reset(formValues);\r\n  }, [property, state?.fields, formType]);\r\n\r\n  const onSubmit = useCallback(\r\n    (values, action) => {\r\n      const formData = new FormData(formRef.current);\r\n      Object.keys(values).forEach((key) => formData.set(key, values[key]));\r\n\r\n      // Use ref to get current value instead of state\r\n      const currentUploadedFiles = uploadedFilesRef.current;\r\n\r\n      formData.set(\"BasePostPrice\", basePostPrice);\r\n      formData.set(\"UploadedFiles\", JSON.stringify(currentUploadedFiles));\r\n\r\n      if (action === \"saveDraft\") {\r\n        formData.set(\"status\", PropertyStatus.DRAFT);\r\n      } else {\r\n        formData.set(\"status\", PropertyStatus.PENDING_APPROVAL);\r\n      }\r\n\r\n      startTransition(() => formAction(formData));\r\n    },\r\n    [formRef, basePostPrice, formAction]\r\n  );\r\n\r\n  const handleSelectAddress = async (place_id, selectedPrediction, resolvedLocation) => {\r\n    try {\r\n      const responsePlaceData = await fetch(`/api/map/place-detail?place_id=${place_id}`);\r\n      const data = await responsePlaceData.json();\r\n\r\n      // Ensure we have a valid description string\r\n      const description = selectedPrediction?.description || \"\";\r\n      // Update state with the description\r\n      setAddressSelected(description);\r\n\r\n      const latitude = data?.result?.geometry?.location?.lat;\r\n      const longitude = data?.result?.geometry?.location?.lng;\r\n\r\n      setSelectedLocation({ latitude, longitude });\r\n\r\n      // Update form values with safe values\r\n      form.setValue(\"address\", data?.result?.name || \"\");\r\n      form.setValue(\"addressSelected\", description);\r\n      form.setValue(\"placeData\", JSON.stringify(data?.result || {}));\r\n      form.setValue(\"longitude\", longitude || \"\");\r\n      form.setValue(\"latitude\", latitude || \"\"); // Handling resolved location from conflict dialog\r\n\r\n      if (resolvedLocation && resolvedLocation.newCityId) {\r\n        form.setValue(\"cityId\", resolvedLocation.newCityId.toString());\r\n        setTargetLocationNames({\r\n          district: resolvedLocation.district,\r\n          ward: resolvedLocation.ward,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      toast({ variant: \"destructive\", description: t(\"fetchPlaceError\") });\r\n    }\r\n  };\r\n\r\n  const handleMarkerDragEnd = useCallback(\r\n    async (markerPositionDragEnd) => {\r\n      const { lat, lng } = markerPositionDragEnd;\r\n      setSelectedLocation({ longitude: lng, latitude: lat });\r\n\r\n      form.setValue(\"longitude\", markerPositionDragEnd.lng);\r\n      form.setValue(\"latitude\", markerPositionDragEnd.lat);\r\n\r\n      const controller = new AbortController();\r\n      try {\r\n        const response = await fetch(`/api/map/geocode?latlng=${lat},${lng}`, {\r\n          signal: controller.signal,\r\n        });\r\n        if (!response.ok) throw new Error(t(\"handleMarkerDragEnd\"));\r\n        const data = await response.json();\r\n        form.setValue(\"placeData\", JSON.stringify(data?.results[0]));\r\n\r\n      } catch (error) {\r\n        if (error.name !== \"AbortError\") {\r\n          toast({ variant: \"destructive\", description: error.message || t(\"handleMarkerDragEnd\") });\r\n        }\r\n      }\r\n      return () => controller.abort();\r\n    },\r\n    [form, toast, t]\r\n  );\r\n\r\n  const handleManualAddressClick = () => {\r\n    if (addressSelectedInputRef) {\r\n      addressSelectedInputRef.focus();\r\n    }\r\n  };\r\n\r\n  const handleRankChange = useCallback(({ previousRank, currentRank }) => {\r\n    const previousPrice = highlightPrices[previousRank] || DEFAULT_POST_PRICE;\r\n    const currentPrice = highlightPrices[currentRank] || DEFAULT_POST_PRICE;\r\n\r\n    if (previousPrice !== currentPrice) {\r\n      setRankChangeDetails({\r\n        previousRank,\r\n        currentRank,\r\n        previousPrice,\r\n        currentPrice,\r\n        isUpgrade: currentPrice < previousPrice,\r\n      });\r\n      setShowRankChangeDialog(true);\r\n    }\r\n  }, []);\r\n\r\n  const handleConfirmPriceChange = () => {\r\n    if (rankChangeDetails) {\r\n      setBasePostPrice(rankChangeDetails.currentPrice);\r\n    }\r\n    setShowRankChangeDialog(false);\r\n  };\r\n\r\n  const handleCancelPriceChange = () => {\r\n    setShowRankChangeDialog(false);\r\n  };\r\n\r\n  return (\r\n    <div className={`inset-0 z-0 bg-slate-50 px-4 md:px-16 py-4`}>\r\n      <Form {...form} className={`container ${isFormDisabled ? \"cursor-not-allowed\" : \"\"}`}>\r\n        {state.message && (\r\n          <Alert variant=\"destructive\" className=\"mb-4\">\r\n            <CircleAlert className=\"h-4 w-4\" />\r\n            <AlertTitle>{t(\"createPostFailed\")}</AlertTitle>\r\n            <AlertDescription>{state.message}</AlertDescription>\r\n          </Alert>\r\n        )}\r\n        {isFormDisabled && (\r\n          <Alert className=\"mb-4 bg-yellow-50 border-yellow-200\">\r\n            <ShieldAlert className=\"h-6 w-6 text-yellow-800 mr-3\" />\r\n            <AlertTitle className=\"text-yellow-800\">{t(\"postCannotBeEdited\")}</AlertTitle>\r\n            <AlertDescription className=\"text-yellow-700\">{t(\"propertyCannotBeEdited\")}</AlertDescription>\r\n          </Alert>\r\n        )}\r\n        <form ref={formRef} onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n            <input type=\"hidden\" name=\"propertyId\" value={property?.id}></input>\r\n            <input type=\"hidden\" name=\"formType\" value={formType}></input>\r\n            <input type=\"hidden\" name=\"status\" value={property?.status}></input>\r\n            {/* Property Information Section (Left) */}\r\n            <div className=\"lg:col-span-2\">\r\n              <PropertyMediaSection\r\n                form={form}\r\n                property={property}\r\n                uploadedFiles={uploadedFiles}\r\n                setUploadedFiles={onUploadComplete}\r\n                isFormDisabled={isFormDisabled}\r\n              />\r\n\r\n              <PropertyBasicInfoSection form={form} isFormDisabled={isFormDisabled} />\r\n\r\n              <CollapseHeader title={t(\"addressSection\")} subTitle={t(\"requiredInfo\")}>\r\n                <Separator className=\"mb-6\" />\r\n                <LocationSelector\r\n                  ref={locationSelectorRef}\r\n                  form={form}\r\n                  isFormDisabled={isFormDisabled}\r\n                  property={property}\r\n                  formType={formType}\r\n                  targetLocationNames={targetLocationNames}\r\n                  setTargetLocationNames={setTargetLocationNames}\r\n                />\r\n                <div className=\"mt-3\">\r\n                  <div>\r\n                    <AddressInput\r\n                      form={form}\r\n                      isFormDisabled={isFormDisabled}\r\n                      selectedCity={selectedCity}\r\n                      selectedDistrict={selectedDistrict}\r\n                      selectedWard={selectedWard}\r\n                      onAddressSelect={handleSelectAddress}\r\n                      onManualAddressClick={handleManualAddressClick}\r\n                      locationSelectorRef={locationSelectorRef}\r\n                    />\r\n                  </div>\r\n                  <div className=\"mt-3\">\r\n                    <div>\r\n                      <div className=\"flex gap-3 mb-3 items-center justify-between\"></div>\r\n                      <FormField\r\n                        control={form.control}\r\n                        name=\"addressSelected\"\r\n                        render={({ field }) => (\r\n                          <FormItem>\r\n                            <FormLabel>\r\n                              <div className=\"flex gap-3\">\r\n                                <Label htmlFor=\"address\">{t(\"addressSelected\")}</Label>\r\n                                <TooltipProvider>\r\n                                  <Tooltip>\r\n                                    <TooltipTrigger asChild>\r\n                                      <HelpCircle className=\"h-4 w-4 text-gray-500 cursor-help\" />\r\n                                    </TooltipTrigger>\r\n                                    <TooltipContent>\r\n                                      <p>{t(\"addressDescription\")}</p>\r\n                                    </TooltipContent>\r\n                                  </Tooltip>\r\n                                </TooltipProvider>\r\n                              </div>\r\n                            </FormLabel>\r\n                            <FormControl>\r\n                              <Input\r\n                                ref={(el) => setAddressSelectedInputRef(el)}\r\n                                placeholder={t(\"addressSelectedPlaceholder\")}\r\n                                {...field}\r\n                                disabled={isFormDisabled}\r\n                                readOnly={isFormDisabled}\r\n                              />\r\n                            </FormControl>\r\n                            <FormMessage />\r\n                          </FormItem>\r\n                        )}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </CollapseHeader>\r\n\r\n              <CollapseHeader title={t(\"map\")} subTitle={t(\"mapDescription\")}>\r\n                <Separator className=\"mb-6\" />\r\n                <SelectedLocationMap\r\n                  selectedLocation={selectedLocation}\r\n                  isDisabled={isFormDisabled}\r\n                  onMarkerDragEnd={!isFormDisabled ? handleMarkerDragEnd : undefined}\r\n                />\r\n              </CollapseHeader>\r\n\r\n              <PropertyPostInformation form={form} isFormDisabled={isFormDisabled}></PropertyPostInformation>\r\n              <AdditionalInformation form={form} isFormDisabled={isFormDisabled}></AdditionalInformation>\r\n            </div>\r\n            {/* Post Information Section (Right) */}\r\n            <div>\r\n              <Card className={`shadow-md sticky top-4`}>\r\n                <CardHeader className=\"p-3\">\r\n                  <div className=\"flex justify-between items-center\">\r\n                    <h4 className=\"font-semibold\">{t(\"postInformation\")}</h4>\r\n                    <PricingDialog />\r\n                  </div>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-2\">\r\n                  <CreatePropertyDetailInformation\r\n                    form={form}\r\n                    property={property}\r\n                    isFormDisabled={isFormDisabled}\r\n                    basePostPrice={basePostPrice}\r\n                    onRankChange={handleRankChange}\r\n                    onRefreshRef={rankRefreshRef}\r\n                  ></CreatePropertyDetailInformation>\r\n                </CardContent>\r\n                <CardFooter className=\"flex gap-4 justify-end\">\r\n                  <PropertySaveButtons\r\n                    formType={formType}\r\n                    propertyStatus={property?.status}\r\n                    isPending={isPending}\r\n                    formHandleSubmit={form.handleSubmit}\r\n                    onSubmit={onSubmit}\r\n                  />\r\n                </CardFooter>\r\n              </Card>\r\n            </div>\r\n          </div>\r\n        </form>\r\n\r\n        {/* Add the rank change dialog */}\r\n        <RankChangeDialog\r\n          open={showRankChangeDialog}\r\n          onOpenChange={setShowRankChangeDialog}\r\n          rankChangeDetails={rankChangeDetails}\r\n          onConfirm={handleConfirmPriceChange}\r\n          onCancel={handleCancelPriceChange}\r\n        />\r\n      </Form>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAMA;;AAtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,MAAM,sBAAsB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAChC,KAAK;IACL,SAAS,kBAAM,8OAAC;YAAI,WAAU;;;;;;;;AAKhC,MAAM,eAAe;IACnB,QAAQ;IACR,SAAS;IACT,aAAa;QACX,MAAM;QACN,aAAa;IACf;AACF;AAEe,SAAS,aAAa,EAAE,UAAU,eAAe,EAAE,WAAW,2GAAA,CAAA,WAAQ,CAAC,GAAG,EAAE;IACzF,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,OAAO,YAAY,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,2GAAA,CAAA,WAAQ,CAAC,GAAG,GAAG,gKAAA,CAAA,iBAAc,GAAG,gKAAA,CAAA,qBAAkB,EAAE;IAEvH,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,gFAAgF;IAChF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE;QACnB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,qIAAA,CAAA,qBAAkB;QACxC,eAAe;YACb,UAAU;YACV,YAAY,UAAU,MAAM;YAC5B,UAAU,UAAU,YAAY;YAChC,UAAU,UAAU,YAAY,2GAAA,CAAA,eAAY,CAAC,IAAI;YACjD,cAAc,UAAU,gBAAgB;YACxC,OAAO,UAAU,SAAS;YAC1B,QAAQ,UAAU,QAAQ,cAAc;YACxC,YAAY,UAAU,YAAY,cAAc;YAChD,QAAQ,UAAU,QAAQ,cAAc;YACxC,SAAS,UAAU,WAAW;YAC9B,iBAAiB,UAAU,mBAAmB;YAC9C,MAAM,UAAU,QAAQ;YACxB,aAAa,UAAU,eAAe;YACtC,MAAM,UAAU,QAAQ;YACxB,QAAQ,UAAU,UAAU;YAC5B,OAAO,UAAU,SAAS;YAC1B,SAAS,UAAU,WAAW;YAC9B,WAAW,UAAU,aAAa;YAClC,kBAAkB,UAAU,oBAAoB;YAChD,UAAU,UAAU,YAAY;YAChC,UAAU,UAAU,YAAY;YAChC,OAAO,UAAU,SAAS;YAC1B,WAAW,UAAU,aAAa;YAClC,UAAU,UAAU,YAAY;YAChC,WAAW,UAAU,aAAa;YAClC,WAAW,UAAU,aAAa;YAClC,QAAQ,UAAU,UAAU,2GAAA,CAAA,iBAAc,CAAC,KAAK;YAChD,eAAe,UAAU,iBAAiB;YAC1C,aAAa,UAAU,eAAe;YACtC,GAAI,OAAO,UAAU,CAAC,CAAC;QACzB;QACA,MAAM;IAER;IAEA,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,mBAAmB,KAAK,KAAK,CAAC;IACpC,MAAM,eAAe,KAAK,KAAK,CAAC;IAEhC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,mEAAmE;IACnE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAElC,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,2GAAA,CAAA,qBAAkB;IACrE,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,aAAa,2GAAA,CAAA,WAAQ,CAAC,IAAI,IAAI,2GAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,UAAU;QAC5F,kBAAkB;IACpB,GAAG;QAAC;QAAU,UAAU;KAAO;IAE/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,aAAa,2GAAA,CAAA,WAAQ,CAAC,IAAI,EAAE;YAC1C,IAAI,SAAS,QAAQ,IAAI,SAAS,SAAS,EAAE;gBAC3C,oBAAoB;oBAAE,UAAU,WAAW,SAAS,QAAQ;oBAAG,WAAW,WAAW,SAAS,SAAS;gBAAE;YAC3G;YAEA,sCAAsC;YACtC,mBAAmB,SAAS,eAAe,IAAI,SAAS,OAAO,IAAI;YACnE,IAAI,SAAS,SAAS,EAAE;gBACtB,IAAI;oBACF,MAAM,YAAY,KAAK,KAAK,CAAC,SAAS,SAAS;oBAC/C,IAAI,UAAU,MAAM,IAAI,UAAU,MAAM,CAAC,iBAAiB,EAAE;wBAC1D,mBAAmB,UAAU,MAAM,CAAC,iBAAiB,IAAI;oBAC3D;gBACF,EAAE,OAAO,OAAO;gBACd,wBAAwB;gBAC1B;YACF;QACF;IACF,GAAG;QAAC;QAAU;KAAS;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,YAAY,MAAM;YAC3B,MAAM;gBACJ,OAAO,EAAE;gBACT,aAAa,EAAE;gBACf,WAAW;YACb;YAEA,mEAAmE;YACnE,IAAI,aAAa,2GAAA,CAAA,WAAQ,CAAC,IAAI,IAAI,OAAO,MAAM;gBAC7C,YAAY,MAAM,IAAI;YACxB,OAAO,IAAI,aAAa,2GAAA,CAAA,WAAQ,CAAC,IAAI,EAAE;gBACrC,YAAY,CAAA,eAAgB,CAAC;wBAC3B,GAAG,YAAY;wBACf,QAAQ,2GAAA,CAAA,iBAAc,CAAC,gBAAgB;oBACzC,CAAC;YACH;YAEA,MAAM,UAAU,WAAW;gBACzB,IAAI,aAAa,2GAAA,CAAA,WAAQ,CAAC,GAAG,EAAE;oBAC7B,IAAI,OAAO,MAAM,WAAW,2GAAA,CAAA,iBAAc,CAAC,KAAK,EAAE;wBAChD,OAAO,IAAI,CAAC;oBACd,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF;YACF,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B,OAAO,IAAI,OAAO,YAAY,OAAO;YACnC,MAAM;gBACJ,OAAO,EAAE;gBACT,aAAa,MAAM,OAAO,IAAI,EAAE;gBAChC,WAAW;YACb;QACF;IACF,GAAG;QAAC;QAAO;QAAQ;QAAU;KAAE;IAE/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,eAAe;YAC3B,iBAAiB,SAAS,aAAa;QACzC;IACF,GAAG;QAAC;KAAS;IAEb,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB,OAAO,GAAG;IAC7B,GAAG;QAAC;KAAc;IAElB,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;IACnB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,UAAU;YACV,YAAY,UAAU,MAAM;YAC5B,UAAU,UAAU,YAAY;YAChC,UAAU,UAAU,YAAY,2GAAA,CAAA,eAAY,CAAC,IAAI;YACjD,cAAc,UAAU,gBAAgB;YACxC,OAAO,UAAU,SAAS;YAC1B,QAAQ,UAAU,QAAQ,cAAc;YACxC,YAAY,UAAU,YAAY,cAAc;YAChD,QAAQ,UAAU,QAAQ,cAAc;YACxC,SAAS,UAAU,WAAW;YAC9B,iBAAiB,UAAU,mBAAmB;YAC9C,MAAM,UAAU,QAAQ;YACxB,aAAa,UAAU,eAAe;YACtC,MAAM,UAAU,QAAQ;YACxB,QAAQ,UAAU,UAAU;YAC5B,OAAO,UAAU,SAAS;YAC1B,SAAS,UAAU,WAAW;YAC9B,WAAW,UAAU,aAAa;YAClC,kBAAkB,UAAU,oBAAoB;YAChD,UAAU,UAAU,YAAY;YAChC,UAAU,UAAU,YAAY;YAChC,OAAO,UAAU,SAAS;YAC1B,WAAW,UAAU,aAAa;YAClC,UAAU,UAAU,YAAY;YAChC,WAAW,UAAU,aAAa;YAClC,WAAW,UAAU,aAAa;YAClC,QAAQ,UAAU,UAAU,2GAAA,CAAA,iBAAc,CAAC,KAAK;YAChD,eAAe,UAAU,iBAAiB;YAC1C,aAAa,UAAU,eAAe;YACtC,GAAI,OAAO,UAAU,CAAC,CAAC;QACzB;QACA,KAAK,KAAK,CAAC;IACb,GAAG;QAAC;QAAU,OAAO;QAAQ;KAAS;IAEtC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACzB,CAAC,QAAQ;QACP,MAAM,WAAW,IAAI,SAAS,QAAQ,OAAO;QAC7C,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAC,MAAQ,SAAS,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI;QAElE,gDAAgD;QAChD,MAAM,uBAAuB,iBAAiB,OAAO;QAErD,SAAS,GAAG,CAAC,iBAAiB;QAC9B,SAAS,GAAG,CAAC,iBAAiB,KAAK,SAAS,CAAC;QAE7C,IAAI,WAAW,aAAa;YAC1B,SAAS,GAAG,CAAC,UAAU,2GAAA,CAAA,iBAAc,CAAC,KAAK;QAC7C,OAAO;YACL,SAAS,GAAG,CAAC,UAAU,2GAAA,CAAA,iBAAc,CAAC,gBAAgB;QACxD;QAEA,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE,IAAM,WAAW;IACnC,GACA;QAAC;QAAS;QAAe;KAAW;IAGtC,MAAM,sBAAsB,OAAO,UAAU,oBAAoB;QAC/D,IAAI;YACF,MAAM,oBAAoB,MAAM,MAAM,CAAC,+BAA+B,EAAE,UAAU;YAClF,MAAM,OAAO,MAAM,kBAAkB,IAAI;YAEzC,4CAA4C;YAC5C,MAAM,cAAc,oBAAoB,eAAe;YACvD,oCAAoC;YACpC,mBAAmB;YAEnB,MAAM,WAAW,MAAM,QAAQ,UAAU,UAAU;YACnD,MAAM,YAAY,MAAM,QAAQ,UAAU,UAAU;YAEpD,oBAAoB;gBAAE;gBAAU;YAAU;YAE1C,sCAAsC;YACtC,KAAK,QAAQ,CAAC,WAAW,MAAM,QAAQ,QAAQ;YAC/C,KAAK,QAAQ,CAAC,mBAAmB;YACjC,KAAK,QAAQ,CAAC,aAAa,KAAK,SAAS,CAAC,MAAM,UAAU,CAAC;YAC3D,KAAK,QAAQ,CAAC,aAAa,aAAa;YACxC,KAAK,QAAQ,CAAC,YAAY,YAAY,KAAK,kDAAkD;YAE7F,IAAI,oBAAoB,iBAAiB,SAAS,EAAE;gBAClD,KAAK,QAAQ,CAAC,UAAU,iBAAiB,SAAS,CAAC,QAAQ;gBAC3D,uBAAuB;oBACrB,UAAU,iBAAiB,QAAQ;oBACnC,MAAM,iBAAiB,IAAI;gBAC7B;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBAAE,SAAS;gBAAe,aAAa,EAAE;YAAmB;QACpE;IACF;IAEA,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,OAAO;QACL,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;QACrB,oBAAoB;YAAE,WAAW;YAAK,UAAU;QAAI;QAEpD,KAAK,QAAQ,CAAC,aAAa,sBAAsB,GAAG;QACpD,KAAK,QAAQ,CAAC,YAAY,sBAAsB,GAAG;QAEnD,MAAM,aAAa,IAAI;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,wBAAwB,EAAE,IAAI,CAAC,EAAE,KAAK,EAAE;gBACpE,QAAQ,WAAW,MAAM;YAC3B;YACA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,EAAE;YACpC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,KAAK,QAAQ,CAAC,aAAa,KAAK,SAAS,CAAC,MAAM,OAAO,CAAC,EAAE;QAE5D,EAAE,OAAO,OAAO;YACd,IAAI,MAAM,IAAI,KAAK,cAAc;gBAC/B,MAAM;oBAAE,SAAS;oBAAe,aAAa,MAAM,OAAO,IAAI,EAAE;gBAAuB;YACzF;QACF;QACA,OAAO,IAAM,WAAW,KAAK;IAC/B,GACA;QAAC;QAAM;QAAO;KAAE;IAGlB,MAAM,2BAA2B;QAC/B,IAAI,yBAAyB;YAC3B,wBAAwB,KAAK;QAC/B;IACF;IAEA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,EAAE,YAAY,EAAE,WAAW,EAAE;QACjE,MAAM,gBAAgB,2GAAA,CAAA,kBAAe,CAAC,aAAa,IAAI,2GAAA,CAAA,qBAAkB;QACzE,MAAM,eAAe,2GAAA,CAAA,kBAAe,CAAC,YAAY,IAAI,2GAAA,CAAA,qBAAkB;QAEvE,IAAI,kBAAkB,cAAc;YAClC,qBAAqB;gBACnB;gBACA;gBACA;gBACA;gBACA,WAAW,eAAe;YAC5B;YACA,wBAAwB;QAC1B;IACF,GAAG,EAAE;IAEL,MAAM,2BAA2B;QAC/B,IAAI,mBAAmB;YACrB,iBAAiB,kBAAkB,YAAY;QACjD;QACA,wBAAwB;IAC1B;IAEA,MAAM,0BAA0B;QAC9B,wBAAwB;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0CAA0C,CAAC;kBAC1D,cAAA,8OAAC,yHAAA,CAAA,OAAI;YAAE,GAAG,IAAI;YAAE,WAAW,CAAC,UAAU,EAAE,iBAAiB,uBAAuB,IAAI;;gBACjF,MAAM,OAAO,kBACZ,8OAAC,0HAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,WAAU;;sCACrC,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC,0HAAA,CAAA,aAAU;sCAAE,EAAE;;;;;;sCACf,8OAAC,0HAAA,CAAA,mBAAgB;sCAAE,MAAM,OAAO;;;;;;;;;;;;gBAGnC,gCACC,8OAAC,0HAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC,0HAAA,CAAA,aAAU;4BAAC,WAAU;sCAAmB,EAAE;;;;;;sCAC3C,8OAAC,0HAAA,CAAA,mBAAgB;4BAAC,WAAU;sCAAmB,EAAE;;;;;;;;;;;;8BAGrD,8OAAC;oBAAK,KAAK;oBAAS,UAAU,KAAK,YAAY,CAAC;oBAAW,WAAU;8BACnE,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,MAAK;gCAAS,MAAK;gCAAa,OAAO,UAAU;;;;;;0CACxD,8OAAC;gCAAM,MAAK;gCAAS,MAAK;gCAAW,OAAO;;;;;;0CAC5C,8OAAC;gCAAM,MAAK;gCAAS,MAAK;gCAAS,OAAO,UAAU;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uJAAA,CAAA,UAAoB;wCACnB,MAAM;wCACN,UAAU;wCACV,eAAe;wCACf,kBAAkB;wCAClB,gBAAgB;;;;;;kDAGlB,8OAAC,2JAAA,CAAA,UAAwB;wCAAC,MAAM;wCAAM,gBAAgB;;;;;;kDAEtD,8OAAC,6HAAA,CAAA,UAAc;wCAAC,OAAO,EAAE;wCAAmB,UAAU,EAAE;;0DACtD,8OAAC,8HAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC,mJAAA,CAAA,UAAgB;gDACf,KAAK;gDACL,MAAM;gDACN,gBAAgB;gDAChB,UAAU;gDACV,UAAU;gDACV,qBAAqB;gDACrB,wBAAwB;;;;;;0DAE1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEACC,cAAA,8OAAC,+IAAA,CAAA,UAAY;4DACX,MAAM;4DACN,gBAAgB;4DAChB,cAAc;4DACd,kBAAkB;4DAClB,cAAc;4DACd,iBAAiB;4DACjB,sBAAsB;4DACtB,qBAAqB;;;;;;;;;;;kEAGzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC,yHAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;8FACP,8OAAC,yHAAA,CAAA,YAAS;8FACR,cAAA,8OAAC;wFAAI,WAAU;;0GACb,8OAAC,0HAAA,CAAA,QAAK;gGAAC,SAAQ;0GAAW,EAAE;;;;;;0GAC5B,8OAAC,4HAAA,CAAA,kBAAe;0GACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;sHACN,8OAAC,4HAAA,CAAA,iBAAc;4GAAC,OAAO;sHACrB,cAAA,8OAAC,kNAAA,CAAA,aAAU;gHAAC,WAAU;;;;;;;;;;;sHAExB,8OAAC,4HAAA,CAAA,iBAAc;sHACb,cAAA,8OAAC;0HAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8FAMhB,8OAAC,yHAAA,CAAA,cAAW;8FACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;wFACJ,KAAK,CAAC,KAAO,2BAA2B;wFACxC,aAAa,EAAE;wFACd,GAAG,KAAK;wFACT,UAAU;wFACV,UAAU;;;;;;;;;;;8FAGd,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAS1B,8OAAC,6HAAA,CAAA,UAAc;wCAAC,OAAO,EAAE;wCAAQ,UAAU,EAAE;;0DAC3C,8OAAC,8HAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDACC,kBAAkB;gDAClB,YAAY;gDACZ,iBAAiB,CAAC,iBAAiB,sBAAsB;;;;;;;;;;;;kDAI7D,8OAAC,0JAAA,CAAA,UAAuB;wCAAC,MAAM;wCAAM,gBAAgB;;;;;;kDACrD,8OAAC,wJAAA,CAAA,UAAqB;wCAAC,MAAM;wCAAM,gBAAgB;;;;;;;;;;;;0CAGrD,8OAAC;0CACC,cAAA,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAW,CAAC,sBAAsB,CAAC;;sDACvC,8OAAC,yHAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAiB,EAAE;;;;;;kEACjC,8OAAC,gJAAA,CAAA,UAAa;;;;;;;;;;;;;;;;sDAGlB,8OAAC,yHAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC,kKAAA,CAAA,UAA+B;gDAC9B,MAAM;gDACN,UAAU;gDACV,gBAAgB;gDAChB,eAAe;gDACf,cAAc;gDACd,cAAc;;;;;;;;;;;sDAGlB,8OAAC,yHAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC,8LAAA,CAAA,UAAmB;gDAClB,UAAU;gDACV,gBAAgB,UAAU;gDAC1B,WAAW;gDACX,kBAAkB,KAAK,YAAY;gDACnC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStB,8OAAC,mJAAA,CAAA,UAAgB;oBACf,MAAM;oBACN,cAAc;oBACd,mBAAmB;oBACnB,WAAW;oBACX,UAAU;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}]}