import { useState } from 'react';
import Sidebar from './Sidebar';
import { Menu, X } from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Desktop Sidebar */}
      <div className={`hidden md:block`}>
        <Sidebar collapsed={sidebarCollapsed} toggleCollapse={toggleSidebar} />
      </div>

      {/* Mobile Sidebar */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={toggleMobileMenu}></div>
          <div className="fixed inset-y-0 left-0 z-40 w-64">
            <Sidebar collapsed={false} toggleCollapse={() => {}} />
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className={`flex-1 flex flex-col overflow-hidden ${sidebarCollapsed ? 'md:ml-20' : 'md:ml-64'}`}>
        {/* Top Bar */}
        <header className="bg-white shadow-sm h-16 flex items-center px-4">
          <button 
            onClick={toggleMobileMenu}
            className="md:hidden mr-4 text-gray-600 hover:text-gray-900"
          >
            <Menu size={24} />
          </button>
          <button
            onClick={toggleSidebar}
            className="hidden md:block text-gray-600 hover:text-gray-900"
          >
            {sidebarCollapsed ? <Menu size={24} /> : <X size={24} />}
          </button>
          <h1 className="text-xl font-semibold ml-4">YEZ Home Admin</h1>
        </header>

        {/* Content */}
        <main className="flex-1 overflow-auto p-4">
          {children}
        </main>
      </div>
    </div>
  );
} 