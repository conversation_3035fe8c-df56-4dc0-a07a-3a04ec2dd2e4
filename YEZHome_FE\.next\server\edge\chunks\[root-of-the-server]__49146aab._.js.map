{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/i18n/routing.ts"], "sourcesContent": ["import {defineRouting} from 'next-intl/routing';\r\n \r\nexport const routing = defineRouting({\r\n  // A list of all locales that are supported\r\n  locales: ['en', 'vi'],\r\n \r\n  // Used when no locale matches\r\n  defaultLocale: 'vi'\r\n});"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,UAAU,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;KAAK;IAErB,8BAA8B;IAC9B,eAAe;AACjB"}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/lib/sessionUtils.js"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\nimport { jwtDecode } from \"jwt-decode\";\r\nimport { redirect } from \"next/navigation\";\r\nimport { jwtVerify } from \"jose\";\r\n\r\nconst SECRET_KEY_STRING = process.env.JWT_SECRET;\r\n\r\nconst SECRET_KEY = new TextEncoder().encode(SECRET_KEY_STRING);\r\n\r\nexport async function createSession(name, value, options) {\r\n  const cookieStore = await cookies();\r\n\r\n  const defaultOptions = {\r\n    secure: true,\r\n    httpOnly: true,\r\n    expires: Date.now() + 24 * 60 * 60 * 1000, // 1 days\r\n    path: \"/\",\r\n    sameSite: \"strict\",\r\n  };\r\n\r\n  cookieStore.set(name, value, {\r\n    ...defaultOptions,\r\n    ...options,\r\n  });\r\n}\r\n\r\nexport async function getSession(name) {\r\n  const cookieStore = await cookies();\r\n\r\n  return cookieStore.get(name)?.value;\r\n}\r\n\r\nexport async function deleteSession(name) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.delete(name);\r\n}\r\n\r\nexport async function fetchWithAuth(url, options = {}) {\r\n  try {\r\n    const token = await getSession(\"Authorization\"); // Lấy token từ session\r\n    if (!token) {\r\n      console.log(\"No token found\");\r\n      return {\r\n        success: false,\r\n        errorType: \"no_token\",\r\n        message: \"User is not logged in.\",\r\n      };\r\n    }\r\n\r\n    console.log(`[${options.method}]: ${url} - Fetching with auth token: ${url} `);\r\n    console.log(`BODY: ${options?.body}`);\r\n\r\n    const response = await fetch(url, {\r\n      ...options,\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        ...options.headers,\r\n      },\r\n      credentials: \"include\",\r\n    });\r\n\r\n    // Xử lý lỗi 401 (Unauthorized)\r\n    if (response.status === 401) {\r\n      try {\r\n        console.log(response);\r\n        // Xử lý lỗi token hết hạn nếu có 401 nhưng token vẫn có trong cooike thì xóa token => vì token đã hết hạn\r\n        if (token) {\r\n          console.warn(`Token expired. Clearing session...${url}`);\r\n          return {\r\n            success: false,\r\n            errorType: \"token_expired\",\r\n            message: \"Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại.\",\r\n          };\r\n        }\r\n      } catch (error) {\r\n        console.warn(\"Lỗi khi xử lý phản hồi 401:\", error);\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        errorType: \"unauthorized\",\r\n        message: \"401. Vui lòng đăng nhập.\",\r\n      };\r\n    }\r\n\r\n    // Xử lý lỗi chung\r\n    if (!response.ok) {\r\n      console.error(`API request failed: ${response.status} ${response.statusText}`);\r\n\r\n      const errorData = await response.json();\r\n      console.error(\"Error data:\", errorData);\r\n\r\n      let message = `Code: ${response.status} - ${response.statusText}: Đã xảy ra lỗi khi xử lý dữ liệu.`;\r\n      if (errorData?.detail || errorData?.message) {\r\n        message = errorData?.detail || errorData?.message;\r\n      } else if (typeof errorData === \"object\") {\r\n        for (const key in errorData) {\r\n          if (errorData.hasOwnProperty(key)) {\r\n            message = errorData[key];\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        errorType: (errorData && errorData?.errorType) || \"api_error\",\r\n        message: message,\r\n      };\r\n    }\r\n\r\n    console.log(\"response\", response);\r\n    // Nếu gọi API thành công, trả về No Content 204 hoặc 201\r\n    if (response.status === 204 || response.status === 201) {\r\n      const data = await response.json();\r\n      console.log(\"data\", data);\r\n      return {\r\n        success: true,\r\n        data,\r\n      };\r\n    } else {\r\n      // Nếu gọi API thành công, trả về dữ liệu chuẩn\r\n      const data = await response.json();\r\n      return {\r\n        success: true,\r\n        data,\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Fetch error:\", error);\r\n    return {\r\n      success: false,\r\n      errorType: \"network_error\",\r\n      message:\r\n        errorData &&\r\n        (errorData?.detail ||\r\n          errorData?.message ||\r\n          `Code: ${response?.status} - ${response?.statusText}: Failed to connect to the server. Please try again later.`),\r\n    };\r\n  }\r\n}\r\n\r\nexport async function fetchWithoutAuth(url, options = {}) {\r\n  try {\r\n    console.log(`[${options.method || \"GET\"}]: ${url} - Fetching without auth`);\r\n    if (options?.body) {\r\n      console.log(`BODY: ${options.body}`);\r\n    }\r\n\r\n    const response = await fetch(url, {\r\n      ...options,\r\n      headers: {\r\n        ...(options.headers || {}),\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      console.error(`Public API request failed: ${response.status} ${response.statusText}`);\r\n      let errorData;\r\n      try {\r\n        errorData = await response.json();\r\n      } catch (e) {\r\n        errorData = { message: response.statusText };\r\n      }\r\n      return {\r\n        success: false,\r\n        errorType: (errorData && errorData?.errorType) || \"api_error\",\r\n        message: errorData?.detail || errorData?.message || `Code: ${response.status} - ${response.statusText}: Đã xảy ra lỗi khi xử lý dữ liệu.`,\r\n      };\r\n    }\r\n\r\n    if (response.status === 204 || response.status === 201) {\r\n      return {\r\n        success: true,\r\n        data: null,\r\n      };\r\n    } else {\r\n      const data = await response.json();\r\n      return {\r\n        success: true,\r\n        data,\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Fetch error (without auth):\", error);\r\n    return {\r\n      success: false,\r\n      errorType: \"network_error\",\r\n      message: `Failed to connect to the server. Please try again later. (${error.message || \"Unknown network error\"})`,\r\n    };\r\n  }\r\n}\r\n\r\nexport async function clearSessionAndBackToLogin() {\r\n  const cookieStore = await cookies();\r\n  cookieStore.delete(\"Authorization\"); // Remove auth token\r\n  redirect(\"/dang-nhap\"); // Redirect to login page\r\n}\r\n\r\nexport async function getJwtInfo() {\r\n  const cookieStore = await cookies();\r\n  const decodedToken = jwtDecode(cookieStore.get(\"Authorization\")?.value);\r\n  return decodedToken;\r\n}\r\n\r\nexport async function verifyJwtToken(token) {\r\n  try {\r\n    const { payload } = await jwtVerify(token, SECRET_KEY);\r\n\r\n    return payload;\r\n  } catch (error) {\r\n    // Xử lý các lỗi cụ thể do 'jose' ném ra (TokenExpiredError, JWSInvalid...)\r\n    // Các lỗi này là runtime error do bản thân token, không phải lỗi cấu hình\r\n    // Log các lỗi này ở mức độ thấp hơn (warn, error)\r\n    if (error.name === 'JOSEError' && error.message === 'signature verification failed') {\r\n         console.warn('JWT verification failed: Invalid signature.');\r\n    } else if (error.name === 'JWTExpired') {\r\n         console.warn('JWT verification failed: Token has expired.');\r\n         // Bạn có thể return một giá trị đặc biệt hoặc ném lỗi khác\r\n         // nếu middleware cần phân biệt hết hạn và invalid signature\r\n    } else {\r\n      console.error('Unexpected JWT verification error:', error);\r\n    }\r\n\r\n    return null; // Trả về null nếu token không hợp lệ hoặc hết hạn\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAAA;AACA;AACA;AAAA;AACA;AALA;;;;;AAOA,MAAM,oBAAoB,QAAQ,GAAG,CAAC,UAAU;AAEhD,MAAM,aAAa,IAAI,cAAc,MAAM,CAAC;AAErC,eAAe,cAAc,IAAI,EAAE,KAAK,EAAE,OAAO;IACtD,MAAM,cAAc,MAAM,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,iBAAiB;QACrB,QAAQ;QACR,UAAU;QACV,SAAS,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;QACrC,MAAM;QACN,UAAU;IACZ;IAEA,YAAY,GAAG,CAAC,MAAM,OAAO;QAC3B,GAAG,cAAc;QACjB,GAAG,OAAO;IACZ;AACF;AAEO,eAAe,WAAW,IAAI;IACnC,MAAM,cAAc,MAAM,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,YAAY,GAAG,CAAC,OAAO;AAChC;AAEO,eAAe,cAAc,IAAI;IACtC,MAAM,cAAc,MAAM,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC;AACrB;AAEO,eAAe,cAAc,GAAG,EAAE,UAAU,CAAC,CAAC;IACnD,IAAI;QACF,MAAM,QAAQ,MAAM,WAAW,kBAAkB,uBAAuB;QACxE,IAAI,CAAC,OAAO;YACV,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;YACX;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,GAAG,EAAE,IAAI,6BAA6B,EAAE,IAAI,CAAC,CAAC;QAC7E,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,MAAM;QAEpC,MAAM,YAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,GAAG,QAAQ,OAAO;YACpB;YACA,aAAa;QACf;QAEA,+BAA+B;QAC/B,IAAI,UAAS,MAAM,KAAK,KAAK;YAC3B,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,0GAA0G;gBAC1G,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC,CAAC,kCAAkC,EAAE,KAAK;oBACvD,OAAO;wBACL,SAAS;wBACT,WAAW;wBACX,SAAS;oBACX;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,+BAA+B;YAC9C;YAEA,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;YACX;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,UAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,UAAS,MAAM,CAAC,CAAC,EAAE,UAAS,UAAU,EAAE;YAE7E,MAAM,aAAY,MAAM,UAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,eAAe;YAE7B,IAAI,UAAU,CAAC,MAAM,EAAE,UAAS,MAAM,CAAC,GAAG,EAAE,UAAS,UAAU,CAAC,kCAAkC,CAAC;YACnG,IAAI,YAAW,UAAU,YAAW,SAAS;gBAC3C,UAAU,YAAW,UAAU,YAAW;YAC5C,OAAO,IAAI,OAAO,eAAc,UAAU;gBACxC,IAAK,MAAM,OAAO,WAAW;oBAC3B,IAAI,WAAU,cAAc,CAAC,MAAM;wBACjC,UAAU,UAAS,CAAC,IAAI;wBACxB;oBACF;gBACF;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,WAAW,AAAC,cAAa,YAAW,aAAc;gBAClD,SAAS;YACX;QACF;QAEA,QAAQ,GAAG,CAAC,YAAY;QACxB,yDAAyD;QACzD,IAAI,UAAS,MAAM,KAAK,OAAO,UAAS,MAAM,KAAK,KAAK;YACtD,MAAM,OAAO,MAAM,UAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,QAAQ;YACpB,OAAO;gBACL,SAAS;gBACT;YACF;QACF,OAAO;YACL,+CAA+C;YAC/C,MAAM,OAAO,MAAM,UAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YACL,SAAS;YACT,WAAW;YACX,SACE,aACA,CAAC,WAAW,UACV,WAAW,WACX,CAAC,MAAM,EAAE,UAAU,OAAO,GAAG,EAAE,UAAU,WAAW,0DAA0D,CAAC;QACrH;IACF;AACF;AAEO,eAAe,iBAAiB,GAAG,EAAE,UAAU,CAAC,CAAC;IACtD,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,MAAM,IAAI,MAAM,GAAG,EAAE,IAAI,wBAAwB,CAAC;QAC1E,IAAI,SAAS,MAAM;YACjB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;QACrC;QAEA,MAAM,YAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,GAAI,QAAQ,OAAO,IAAI,CAAC,CAAC;YAC3B;QACF;QAEA,IAAI,CAAC,UAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,UAAS,MAAM,CAAC,CAAC,EAAE,UAAS,UAAU,EAAE;YACpF,IAAI;YACJ,IAAI;gBACF,aAAY,MAAM,UAAS,IAAI;YACjC,EAAE,OAAO,GAAG;gBACV,aAAY;oBAAE,SAAS,UAAS,UAAU;gBAAC;YAC7C;YACA,OAAO;gBACL,SAAS;gBACT,WAAW,AAAC,cAAa,YAAW,aAAc;gBAClD,SAAS,YAAW,UAAU,YAAW,WAAW,CAAC,MAAM,EAAE,UAAS,MAAM,CAAC,GAAG,EAAE,UAAS,UAAU,CAAC,kCAAkC,CAAC;YAC3I;QACF;QAEA,IAAI,UAAS,MAAM,KAAK,OAAO,UAAS,MAAM,KAAK,KAAK;YACtD,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,OAAO;YACL,MAAM,OAAO,MAAM,UAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YACL,SAAS;YACT,WAAW;YACX,SAAS,CAAC,0DAA0D,EAAE,MAAM,OAAO,IAAI,wBAAwB,CAAC,CAAC;QACnH;IACF;AACF;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC,kBAAkB,oBAAoB;IACzD,CAAA,GAAA,+KAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,yBAAyB;AACnD;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD;IAChC,MAAM,eAAe,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,YAAY,GAAG,CAAC,kBAAkB;IACjE,OAAO;AACT;AAEO,eAAe,eAAe,KAAK;IACxC,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAE3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,2EAA2E;QAC3E,0EAA0E;QAC1E,kDAAkD;QAClD,IAAI,MAAM,IAAI,KAAK,eAAe,MAAM,OAAO,KAAK,iCAAiC;YAChF,QAAQ,IAAI,CAAC;QAClB,OAAO,IAAI,MAAM,IAAI,KAAK,cAAc;YACnC,QAAQ,IAAI,CAAC;QACb,2DAA2D;QAC3D,4DAA4D;QACjE,OAAO;YACL,QAAQ,KAAK,CAAC,sCAAsC;QACtD;QAEA,OAAO,MAAM,kDAAkD;IACjE;AACF"}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport createMiddleware from \"next-intl/middleware\";\r\nimport { cookies } from \"next/headers\";\r\nimport { NextResponse } from \"next/server\";\r\nimport { routing } from \"./i18n/routing\";\r\nimport { getSession, verifyJwtToken } from \"./lib/sessionUtils\";\r\n\r\n// 1. Specify protected and public routes (locale-agnostic)\r\nconst protectedRoutes = [\r\n  \"/user/profile\",\r\n  \"/user/payments\",\r\n  \"/user/notifications\",\r\n  \"/user/bds\",\r\n  \"/user/wallet\",\r\n  \"/user/setting\",\r\n  \"/user/dashboard\",\r\n  \"/user/favorite\",\r\n  \"/user/transactions\",\r\n  \"/test-profile-context\",\r\n];\r\nconst publicRoutes = [\"/dang-ki\", \"/dang-nhap\", \"/quen-mat-khau\"];\r\n\r\nconst handleI18nRouting = createMiddleware(routing);\r\n\r\n// Regex for the dynamic EDIT route (locale-stripped)\r\nconst propertyRegex = /^\\/user\\/bds\\/[a-zA-Z0-9-]+$/;\r\n\r\n// Your authentication middleware logic\r\nasync function authMiddleware(request) {\r\n  // pathname here might still have locale prefix when auth runs after intl\r\n  const { pathname, origin } = request.nextUrl;\r\n  // --- Robust locale detection from pathname ---\r\n  let detectedLocale = routing.defaultLocale; // Start with default\r\n  let localeStrippedPathname = pathname;\r\n\r\n  for (const locale of routing.locales) {\r\n    if (pathname === `/${locale}` || pathname.startsWith(`/${locale}/`)) {\r\n      detectedLocale = locale;\r\n      if (pathname === `/${locale}`) {\r\n        localeStrippedPathname = \"/\"; // Root path for the locale\r\n      } else {\r\n        localeStrippedPathname = pathname.substring(`/${locale}`.length) || \"/\"; // Path after locale prefix\r\n      }\r\n      break; // Found the locale\r\n    }\r\n  }\r\n\r\n  // 2. Check if the *locale-stripped* route is protected or public\r\n  const isProtectedRoute = protectedRoutes.includes(localeStrippedPathname) || propertyRegex.test(localeStrippedPathname);\r\n\r\n  const isPublicRoute = publicRoutes.includes(localeStrippedPathname);\r\n\r\n  // Check for cookie\r\n  const token = await getSession(\"Authorization\");\r\n\r\n  let isAuthenticated = false;\r\n\r\n  if (token) {\r\n    const decodedPayload = await verifyJwtToken(token); \r\n\r\n    if (decodedPayload) {\r\n      isAuthenticated = true;\r\n    } else {\r\n      cookies().delete(\"Authorization\");\r\n    }\r\n  }\r\n\r\n  if (isProtectedRoute && !isAuthenticated) {\r\n    const locale = detectedLocale;\r\n    const loginPath = `/${locale}/dang-nhap`.replace(\"//\", \"/\");\r\n    return NextResponse.redirect(new URL(loginPath, origin));\r\n  }\r\n\r\n  if (isAuthenticated && isPublicRoute && !localeStrippedPathname.startsWith(\"/user/profile\")) {\r\n    const locale = detectedLocale;\r\n    const profilePath = `/${locale}/user/profile`.replace(\"//\", \"/\");\r\n    return NextResponse.redirect(new URL(profilePath, origin));\r\n  }\r\n\r\n  return null; // Indicate auth middleware allows the request\r\n}\r\n\r\nexport default async function middleware(request) {\r\n  const originalPathname = request.nextUrl.pathname;\r\n\r\n  // 1. Run next-intl middleware first.\r\n  // It handles localization, strips locale prefix from pathname for subsequent checks,\r\n  // and might return a response (e.g., redirect for locale preference or cookie setting).\r\n  const intlResponse = handleI18nRouting(request);\r\n\r\n\r\n  // 2. Run authentication middleware *after* intl middleware.\r\n  // Pass the request object potentially modified by intl middleware.\r\n  const authResponse = await authMiddleware(request);\r\n\r\n  // 3. Prioritize auth response (redirect to login if required).\r\n  if (authResponse) {\r\n    return authResponse;\r\n  }\r\n\r\n  // 4. If auth allowed, check if intl middleware had a response to return.\r\n  if (intlResponse) {\r\n    return intlResponse;\r\n  }\r\n\r\n  // 5. If neither middleware returned a response, proceed to the requested page\r\n  return NextResponse.next({\r\n    request: {\r\n      headers: new Headers(request.headers),\r\n    },\r\n  });\r\n}\r\n\r\n// Routes Middleware should not run on\r\nexport const config = {\r\n  matcher: [\"/((?!api|_next/static|_next/image|_vercel|.*\\\\.png|.*\\\\.svg|.*\\\\.webp|.*\\\\.jpg$).*)\"],\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AANA;;;;;;AAQA,2DAA2D;AAC3D,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAM,eAAe;IAAC;IAAY;IAAc;CAAiB;AAEjE,MAAM,oBAAoB,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE,uHAAA,CAAA,UAAO;AAElD,qDAAqD;AACrD,MAAM,gBAAgB;AAEtB,uCAAuC;AACvC,eAAe,eAAe,OAAO;IACnC,yEAAyE;IACzE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,QAAQ,OAAO;IAC5C,gDAAgD;IAChD,IAAI,iBAAiB,uHAAA,CAAA,UAAO,CAAC,aAAa,EAAE,qBAAqB;IACjE,IAAI,yBAAyB;IAE7B,KAAK,MAAM,UAAU,uHAAA,CAAA,UAAO,CAAC,OAAO,CAAE;QACpC,IAAI,aAAa,CAAC,CAAC,EAAE,QAAQ,IAAI,SAAS,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;YACnE,iBAAiB;YACjB,IAAI,aAAa,CAAC,CAAC,EAAE,QAAQ,EAAE;gBAC7B,yBAAyB,KAAK,2BAA2B;YAC3D,OAAO;gBACL,yBAAyB,SAAS,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,KAAK,KAAK,2BAA2B;YACtG;YACA,OAAO,mBAAmB;QAC5B;IACF;IAEA,iEAAiE;IACjE,MAAM,mBAAmB,gBAAgB,QAAQ,CAAC,2BAA2B,cAAc,IAAI,CAAC;IAEhG,MAAM,gBAAgB,aAAa,QAAQ,CAAC;IAE5C,mBAAmB;IACnB,MAAM,QAAQ,MAAM,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD,EAAE;IAE/B,IAAI,kBAAkB;IAEtB,IAAI,OAAO;QACT,MAAM,iBAAiB,MAAM,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;QAE5C,IAAI,gBAAgB;YAClB,kBAAkB;QACpB,OAAO;YACL,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD,IAAI,MAAM,CAAC;QACnB;IACF;IAEA,IAAI,oBAAoB,CAAC,iBAAiB;QACxC,MAAM,SAAS;QACf,MAAM,YAAY,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM;QACvD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,WAAW;IAClD;IAEA,IAAI,mBAAmB,iBAAiB,CAAC,uBAAuB,UAAU,CAAC,kBAAkB;QAC3F,MAAM,SAAS;QACf,MAAM,cAAc,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC,CAAC,OAAO,CAAC,MAAM;QAC5D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,aAAa;IACpD;IAEA,OAAO,MAAM,8CAA8C;AAC7D;AAEe,eAAe,WAAW,OAAO;IAC9C,MAAM,mBAAmB,QAAQ,OAAO,CAAC,QAAQ;IAEjD,qCAAqC;IACrC,qFAAqF;IACrF,wFAAwF;IACxF,MAAM,eAAe,kBAAkB;IAGvC,4DAA4D;IAC5D,mEAAmE;IACnE,MAAM,eAAe,MAAM,eAAe;IAE1C,+DAA+D;IAC/D,IAAI,cAAc;QAChB,OAAO;IACT;IAEA,yEAAyE;IACzE,IAAI,cAAc;QAChB,OAAO;IACT;IAEA,8EAA8E;IAC9E,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;YACP,SAAS,IAAI,QAAQ,QAAQ,OAAO;QACtC;IACF;AACF;AAGO,MAAM,SAAS;IACpB,SAAS;QAAC;KAAsF;AAClG"}}]}